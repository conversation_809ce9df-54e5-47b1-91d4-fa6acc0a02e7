import { ref } from 'vue'
import { GetAllUserPages } from '@/api/modules/user'

const optionList = ref([])
const isload = ref(false)

function loadOption() {
  if (isload.value) return
  GetAllUserPages().then((res) => {
    optionList.value = res.list || []
    isload.value = true
  })
}

function format(accountName) {
  return optionList.value.find((item) => item.accountName == accountName)?.userName || accountName
}

function formatTable(row, column, cellValue) {
  return format(cellValue)
}

export function useUserDict() {
  loadOption()
  return {
    format,
    formatTable
  }
}
