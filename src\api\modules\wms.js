import request from '@/common/request.js'

// 1.入库需求单 ------------------------------------------------

/**
 * @description  获取入库需求单数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetInboundDemandOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取入库需求单物料明细数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetInboundMatInfoByStkinNoList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  查询入库需求单是否有子任务
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const CheckInWmsStockStatus = (params = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/checkInStatus',
    method: 'GET',
    params
  })
}

/**
 * @description  获取收货包装号明细数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetwmReceiptIteminNoList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  提交质检
 * @param { Object }: data
 * @return { Promise }
 */
export const SubmitQuality = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/submitQuality`,
    method: 'POST',
    data
  })
}

/**
 * @description  提交上架
 * @param { Object }: data
 * @return { Promise }
 */
export const SubmitOnShelf = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/onShelf`,
    method: 'POST',
    data
  })
}

/**
 * @description  新增（保存、提交）入库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const addInStockOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/savaInStockReq',
    method: 'POST',
    data
  })
}

/**
 * @description  编辑入库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const editInStockOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/updateInStockReq',
    method: 'POST',
    data
  })
}

/**
 * @description  关闭入库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const closeInStockOrderItem = (id) => {
  return request({
    url: `/mes_api/v1/inStockDeal/${id}/closeInStockReq`,
    method: 'POST'
  })
}

/**
 * @description  收货生成包装号
 * @param { Object }: data
 * @return { Promise }
 */
export const createRecievedGenerateSuNo = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/generateSuNo`,
    method: 'POST',
    data
  })
}

/**
 * @description  无单据生成包装号
 * @param { Object }: data
 * @return { Promise }
 */
export const createNoSourceRecievedGenerateSuNo = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/noSourceGenerateSuNo`,
    method: 'POST',
    data
  })
}

/**
 * @description  提交收货
 * @param { Object }: data
 * @return { Promise }
 */
export const noSourceSubmitDelivery = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/noSourceSubmitTakeDelivery`,
    method: 'POST',
    data
  })
}

/**
 * @description  根据物料号查询收货明细且没有生成收货单号
 * @return { Promise }
 */
export const GetPckInfoByMatCode = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/queryByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description  根据物料号进行分组查询
 * @return { Promise }
 */
export const GetItemInfoByMat = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinItem/paginByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description  提交收货
 * @param { Object }: data
 * @return { Promise }
 */
export const submitTakeDelivery = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/submitTakeDelivery`,
    method: 'POST',
    data
  })
}

/**
 * @description  无单据提交
 * @param { Object }: data
 * @return { Promise }
 */
export const noSourceSubmitTakeDelivery = (data) => {
  return request({
    url: `/mes_api/v1/inStockDeal/saveNoSourceReceipt`,
    method: 'POST',
    data
  })
}

/**
 * @description  删除物料明细及关联的包装信息
 * @param { Object }: params
 * @return { Promise }
 */
export const delNoSourceMatItem = (params = {}) => {
  return request({
    url: `/mes_api/v1/wmReceiptItem/deleteByMatCode`,
    method: 'DELETE',
    params
  })
}

/**
 * @description  取消清除所有暂存的物料明细及关联的包装信息
 * @param { Object }: data
 * @return { Promise }
 */
export const cancelNoSourcePckInfo = () => {
  return request({
    url: `/mes_api/v1/wmReceiptItem/cancel`,
    method: 'DELETE'
  })
}

/**
 * @description  获取仓库主数据
 * @return { Promise }
 */
export const GetWhList = () => {
  return request({
    url: '/mes_api/v1/wmWarehouse/getAllHouse',
    method: 'GET'
  })
}

/**
 * @description  查询待收货采购单明细
 * @param { Object }: data
 * @return { Promise }
 */
export const QueryPurchaseOrdermatList = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPurchaseOrder/getHarvestInfo',
    method: 'POST',
    data
  })
}

/**
 * @description  收货分包
 * @param { Object }: data
 * @return { Promise }
 */
export const ReceivingGoodspacking = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinOrder/createStockItem',
    method: 'POST',
    data
  })
}

/**
 * @description  获取某仓库库位组织架构图
 * @param { Object }: params
 * @return { Promise }
 */
// export const GetStructuralStation = (params = {}) => {
//   return request({
//     url: '/mes_api/v1/wmWarehouse/getStructuralStation',
//     method: 'GET',
//     params
//   })
// }
export const GetStructuralStation = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/getMatchBinByBussRule',
    method: 'GET',
    params
  })
}

/**
 * @description  提交收货数据
 * @data { Object }: data
 * @return { Promise }
 */
export const SaveRecivingInfoData = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinOrder/submitStockItem',
    method: 'POST',
    data
  })
}

/**
 * @description  获取待收货的采购单的入库明细
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetStockinItemInfo = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPurchaseOrder/getStockinItem',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  关闭数据
 * @data { Object }: data
 * @return { Promise }
 */
export const DelRecivingItem = (id) => {
  return request({
    url: `/mes_api/v1/wmPurchaseOrder/${id}/delete`,
    method: 'DELETE'
  })
}

// 2.入库单 ------------------------------------------------

/**
 * @description  获取入库清单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetStockOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinOrder/getItemList',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取待收货的采购单的入库明细
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetStockOrderItemInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStockinOrder/getItemBagList',
    method: 'GET',
    params
  })
}

/**
 * @description  获取收货单列表
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetwmInboundDocList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmInboundDoc/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取入库凭证明细查询
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetwmInboundDocItemInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmInboundDoc/getInDocItem',
    method: 'GET',
    params
  })
}

// 3.出库单 ------------------------------------------------
/**
 * @description  获取出库清单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetOutboundOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/getOrderPage',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  出库需求单关闭
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const CloseoutOrder = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/closeOrder',
    method: 'POST',
    data
  })
}

/**
 * @description  获取出库需求单子任务状态
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetOutWmshasSubtask = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/hasSubtask',
    method: 'GET',
    params
  })
}

/**
 * @description  提交出库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const SubmitoutOrder = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/submit',
    method: 'POST',
    data
  })
}

/**
 * @description  确认拣货
 * @param { Object }: data
 * @return { Promise }
 */
export const SubmitPickingIds = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/submitPickTask',
    method: 'PUT',
    data
  })
}

/**
 * @description  新增出库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const AddOutboundOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/create',
    method: 'POST',
    data
  })
}

/**
 * @description  编辑出库需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const EditOutboundOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/updateOrder',
    method: 'POST',
    data
  })
}

/**
 * @description  查看出库需求单
 * @param { Object }: params
 * @return { Promise }
 */
export const QeuryOutboundOrderItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/getOutItem',
    method: 'GET',
    params
  })
}

/**
 * @description  获取物料可用库存
 * @param { Object }: params
 * @return { Promise }
 */
export const GetAvailableMat = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmStockoutOrder/availableMat',
    method: 'GET',
    params
  })
}

/**
 * @description  获取拣货单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetWmPickingTask = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/pageRated',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  关闭拣货单
 * @param { Object }: params
 * @return { Promise }
 */
export const CloseWmPickingTaskItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/setClose',
    method: 'PUT',
    params
  })
}

/**
 * @description  获取替换或拆包详情
 * @param { Object }: params
 * @return { Promise }
 */
export const GetWmPickingInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/getSingleTask',
    method: 'GET',
    params
  })
}

/**
 * @description  分包
 * @param { Object }: params
 * @return { Promise }
 */
export const SubWmPicking = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/pickPackage',
    method: 'POST',
    data
  })
}

/**
 * @description  替换
 * @param { Object }: params
 * @return { Promise }
 */
export const ReplaceWmPicking = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/transferPackage',
    method: 'POST',
    data
  })
}

/**
 * @description 查询可用库存清单
 * @param { Object }: params
 * @return { Promise }
 */
export const GetWmsQuant = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/paginStorage',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description 查询出库单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetwmOutboundDocList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutcfmOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description 查询可用库存清单
 * @param { Object }: params
 * @return { Promise }
 */
export const GetGetOutDocItemInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmOutboundDoc/getOutDocItem',
    method: 'GET',
    params
  })
}

/**
 * @description 拆包入库
 * @param { Object }: data
 * @return { Promise }
 */
export const WmPickingTakeApart = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/takeApart',
    method: 'POST',
    data
  })
}

/**
 * @description 查询成品入库单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetWmShopprodinOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmShopprodinOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description 查询成品入库单详情接口
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetWmShopprodinItemList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmShopprodinItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  新增成品入库单
 * @param { Object }: data
 * @return { Promise }
 */
export const AddWmShopprodinOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmShopprodinOrder/save',
    method: 'POST',
    data
  })
}

/**
 * @description 编辑成品入库单
 * @param { Object }: data
 * @return { Promise }
 */
export const editWmShopprodinOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmShopprodinOrder/update',
    method: 'PUT',
    data
  })
}

/**
 * @description 删除成品入库项
 * @param { Object }: data
 * @return { Promise }
 */
export const delWmShopprodinItem = (ids) => {
  return request({
    url: `/mes_api/v1/wmShopprodinOrder/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description 删除成品入库子项
 * @param { Object }: data
 * @return { Promise }
 */
export const delWmShopprodinPckItem = (ids) => {
  return request({
    url: `/mes_api/v1/wmShopprodinItem/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description 批量提交成品入库单
 * @param { Object }: data
 * @return { Promise }
 */
export const submitManyWmShopprodinOrder = (ids) => {
  return request({
    url: `/mes_api/v1/wmShopprodinOrder/${ids}/submit`,
    method: 'POST'
  })
}

// 4. 收货单 ------------------------------------------------

/**
 * @description  获取收货库清单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取收货的物料汇总数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptSummaryItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/paginByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description  获取收货的物料的包装明细
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptItemInfo = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/pages',
    method: 'POST',
    params,
    data
  })
}

// 4. 入库单凭证 ------------------------------------------------

/**
 * @description  获取入库单凭证清单
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptDocList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmInboundDoc/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取入库单凭证的汇总数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptSummaryDocItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmInboundItem/paginByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description  获取收货的物料的包装明细
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetReceiptDocItemInfo = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmInboundItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取调拨单数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetTransshipOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取物料号分组的汇总数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetTransshipSummaryItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipItem/paginByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description  获取调拨单包装明细
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetTransshipOrderItemInfo = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  获取仓库及联数据
 * @param { Object }: params
 * @return { Promise }
 */
export const QueryWareHouseData = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/getBinCodeStructure',
    method: 'GET',
    params
  })
}

/**
 * @description  获取仓库及联数据
 * @param { Object }: params
 * @return { Promise }
 */
export const GetMatBatchInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialBatch/getMatBatch',
    method: 'GET',
    params
  })
}

/**
 * @description  查询物料的收货方式
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetMatReceiptType = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmWhsMat/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  查询所有的待上架且上架任务号为空的收货明细
 * @param { Object }: data
 * @return { Promise }
 */
export const GetBeOnShelfData = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/getBeOnShelf',
    method: 'POST',
    data
  })
}

/**
 * @description  新增上架提交
 * @param { Object }: data
 * @return { Promise }
 */
export const SaveFromWmquant = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/saveFromWmquant',
    method: 'POST',
    data
  })
}

// 删除

export const deleteReceiptDataByUserName = () => {
  return request({
    url: '/mes_api/v1/wmReceiptItem/deleteByUserName',
    method: 'DELETE'
  })
}

/**
 * @description  根据物料号查询批次数据
 * @param { Object }: data
 * @return { Promise }
 */
export const GetBatchListByMat = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/batchRecord/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  新增批次记录
 * @param { Object }: data
 * @return { Promise }
 */
export const addBatchItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/getBatchCode',
    method: 'POST',
    data
  })
}

/**
 * @description  查询收货明细根据入库需求单or托盘号
 * @param { Object }: data
 * @return { Promise }
 */
export const GetGenReByOneKeyPalletList = (params = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/genReByOneKeyPallet',
    method: 'GET',
    params
  })
}

/**
 * @description  一键收货、按托收货进行提交操作
 * @param { Object }: data
 * @return { Promise }
 */
export const AddOneKeyPalletItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/submitOneKeyPallet',
    method: 'POST',
    data
  })
}

/**
 * @description  校验无单据提交数据
 * @param { Object }: data
 * @return { Promise }
 */
export const checkNosourceData = (data = {}) => {
  return request({
    url: '/mes_api/v1/inStockDeal/preCheckReceiptItem',
    method: 'POST',
    data
  })
}

/*  其他入库   */

/**
 * @description  分页查询
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const otherWmsStockList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmOthstkinOrder/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description  根据其他入库单号查询入库单明细
 * @param { Object }: params
 * @return { Promise }
 */
export const GetOtherWmsStockInfoList = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmOthstkinOrder/pagesByOthstkinNo',
    method: 'GET',
    params
  })
}

/**
 * @description  保存或提交其他入库需求
 * @param { Object }: data
 * @return { Promise }
 */
export const saveOrAddWmsStockItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOthstkinOrder/saveOthstkin',
    method: 'POST',
    data
  })
}

/**
 * @description  编辑其他入库需求
 * @param { Object }: data
 * @return { Promise }
 */
export const eidtWmsStockItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOthstkinOrder/updateOthstkin',
    method: 'POST',
    data
  })
}

/**
 * @description  删除其他入库需求
 * @param { Object }: data
 * @return { Promise }
 */
export const DelWmsStockItem = (ids) => {
  return request({
    url: `/mes_api/v1/wmOthstkinOrder/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description 删除调拨需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const DelWmsTransshipOrderItem = (params = {}) => {
  return request({
    url: `/mes_api/v1/wmTransshipOrder/deleteById`,
    method: 'DELETE',
    showLoading: true,
    params
  })
}

// 获取工厂下的仓库列表
export const getWareHouseList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmWarehouse/pages',
    method: 'POST',
    params,
    data
  })
}

// 委外任务关联查询
export const QueryOutSourceList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/pageRelated',
    method: 'POST',
    params,
    data
  })
}

//委外任务明细关联查询
export const QueryOutSourceInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/pageItemRelated',
    method: 'GET',
    params
  })
}

//新建委外任务
export const CreateOutSourceTask = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/createOrder',
    method: 'POST',
    data
  })
}

//编辑委外任务
export const EditOutSourceTask = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/updateOrder',
    method: 'POST',
    data
  })
}

//编辑委外任务
export const GetBinCodeByWhcode = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description 删除调拨需求单
 * @param { Object }: data
 * @return { Promise }
 */
export const DelOutSourceItem = (id) => {
  return request({
    url: `/mes_api/v1/wmOutprodOrder/${id}/delete`,
    method: 'DELETE',
    showLoading: true
  })
}
//委外任务在制品查询
export const QueryOutprodOrderList = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/getProMat',
    method: 'POST',
    data
  })
}

//委外任务Bom查询
export const QueryOutprodBomOrderList = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/getRowMat',
    method: 'POST',
    data
  })
}

// 产品交货报告要求功能接口
export const getWmMatDevDoc = (params, data) => {
  return request({
    url: '/mes_api/v1/wmMatDevdoc/pages',
    method: 'POST',
    params,
    data
  })
}

// 产品交货报告新增、编辑接口

export const saveOrEditWmMatDevDoc = (data) => {
  return request({
    url: '/mes_api/v1/wmMatDevdoc/saveOrUpdateDevdoc',
    method: 'POST',
    data
  })
}

/**
 * @description  产品交货报告新增、编辑接口
 * @param { Object }: data
 * @return { Promise }
 */
export const DelWmMatDevDocItem = (ids) => {
  return request({
    url: `/mes_api/v1/wmMatDevdoc/${ids}/delete`,
    method: 'DELETE',
    showLoading: true
  })
}

export const GetOutOrderProgress = (data) => {
  return request({
    url: '/mes_api/v1/wmOutprodOrder/outOrderProgress',
    method: 'POST',
    data
  })
}

/**
 * @description  获取推荐库位
 * @param { Object }: data
 * @return { Promise }
 */
export const GetSugBinCode = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/getSugBin',
    method: 'POST',
    showLoading: true,
    data
  })
}

// 根据仓库查询下面的库区数据
export const GetAreaList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageArea/pages',
    method: 'POST',
    params,
    data
  })
}

//查询库区下的库位
export const GetstagBinList = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmWarehouse/getBinInArea',
    method: 'GET',
    params
  })
}

/*     补货模块      */

//补货策略查询
export const GetReplenishList = (data = {}) => {
  return request({
    url: '/mes_api/wmStrategyReplenish/pages',
    method: 'POST',
    data
  })
}

// 补货策略新增
export const AddReplenishList = (data = {}) => {
  return request({
    url: '/mes_api/wmStrategyReplenish/add',
    method: 'POST',
    data
  })
}

// 补货策略编辑
export const EditReplenishList = (data = {}) => {
  return request({
    url: '/mes_api/wmStrategyReplenish/edit',
    method: 'POST',
    data
  })
}

// 补货策略删除
export const DelReplenishList = (id) => {
  return request({
    url: `/mes_api/wmStrategyReplenish/${id}`,
    method: 'DELETE'
  })
}

// 补货需求单查询
export const GetRepOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/wmReplenishOrder/getOrderPage',
    method: 'POST',
    params,
    data
  })
}

// 补货需求单详情查看
export const GetRepInfo = (params = {}) => {
  return request({
    url: '/mes_api/wmReplenishOrder/getRepItem',
    method: 'GET',
    params
  })
}

// 补货物料清单查询
export const GetMatInfoByRepList = (data = {}) => {
  return request({
    url: '/mes_api/wmReplenishItem/pages',
    method: 'POST',
    data
  })
}

// 补货任务查询
export const GetRepTaskList = (data = {}) => {
  return request({
    url: '/mes_api/wmReplenishTask/getTaskPage',
    method: 'POST',
    data
  })
}
// 补货任务明细查询
export const GetRepInfoTaskList = (data = {}) => {
  return request({
    url: '/mes_api/wmReplenishTaskItem/getTaskItemPage',
    method: 'POST',
    data
  })
}

//确认补货
export const cofrimRepItem = (data = {}) => {
  return request({
    url: '/mes_api/wmReplenishTaskItem/confirmReplenish',
    method: 'POST',
    data
  })
}

//关闭补货需求单

export const CloseRepItem = (params) => {
  return request({
    url: `/mes_api/wmReplenishOrder/close`,
    method: 'DELETE',
    params
  })
}

//质检回撤操作
export const QuanlityInspectionRevoke = (data = {}) => {
  return request({
    url: `/mes_api/v1/wmQcTask/revokeWmQcTask`,
    method: 'PUT',
    data
  })
}

/**
 * @description  调拨单 查询调拨单是否有未完成的任务
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetwmTransshipOrderInAndOut = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/checkInAndOut',
    method: 'GET',
    params
  })
}

// 收发存汇汇总业务
export const GetWiTurnStatList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantDaily/doWiTurnStat',
    method: 'POST',
    params,
    data
  })
}

//收发存汇库存周转
export const GetWiInvtTurnList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantDaily/doWiInvtTurn',
    method: 'POST',
    params,
    data
  })
}

//拣货添加物料校验
export const PickCheck = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmPickingTask/pickCheck',
    method: 'POST',
    data
  })
}

//查询库存转换汇总记录
export const GetWmsConvertList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantConvert/pagesConvNo',
    method: 'POST',
    params,
    data
  })
}

// 查询库存转换记录
export const GetWmsConvertData = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantConvert/pages',
    method: 'POST',
    params,
    data
  })
}

// 保存库存转换记录
export const SaveWmsConvertData = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantConvert/saveBatch',
    method: 'POST',
    data
  })
}

// 提交库存转换记录
export const SubmitWmsConvertData = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuantConvert/submitBatch',
    method: 'POST',
    data
  })
}

//删除库存转换记录
export const DelWmsConvertData = (ids) => {
  return request({
    url: `/mes_api/v1/wmQuantConvert/${ids}/delete`,
    method: 'DELETE'
  })
}

// 提交库存转换记录
export const GetTimelyDeliveryRate = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPurchaseOrder/timelyDeliveryRate',
    method: 'POST',
    params,
    data
  })
}

export const proRoutinginfo2 = (params = {}) => {
  return request({
    url: '/mes_api/v1/proRouting/info2',
    method: 'GET',
    params,
  })
}
