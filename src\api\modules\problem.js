import request from '@/common/request.js'

// ===================== 问题管理 =====================
// 查询问题列表
export const apiPbPage = (data = {}) => {
  return request({
    url: '/pb_api/pbProblem/pageList',
    method: 'POST',
    data
  })
}

// 新增问题
export const apiAddPb = (data = {}) => {
  return request({
    url: '/pb_api/pbProblem',
    method: 'POST',
    data
  })
}

// 修改问题
export const apiUpdatePb = (data = {}) => {
  return request({
    url: `/pb_api/pbProblem/${data.id}`,
    method: 'PUT',
    data
  })
}

// 问题详情
export const apiGetPbDetail = (id) => {
  return request({
    url: `/pb_api/pbProblem/${id}`,
    method: 'GET'
  })
}

// 问题状态统计
export const apiGetPbStatusTotal = (id) => {
  return request({
    url: `/pb_api/pbProblem/pbStatusNumList`,
    method: 'GET'
  })
}

// ===================== 8D =====================
// 8D详情列表
export const apiPbDetail8DPage = (problemCode) => {
  return request({
    url: '/pb_api/pb8dDetail/pageList',
    method: 'POST',
    data: {
      pageSize: 1,
      size: 1,
      problemCode
    }
  })
}

// 8D详情保存
export const apiPbDetail8DSave = (data) => {
  return request({
    url: `/pb_api/pb8dDetail/${data.id}`,
    method: 'PUT',
    data
  })
}

// ===================== A3 =====================
// A3详情列表
export const apiPbDetailA3Page = (problemCode) => {
  return request({
    url: '/pb_api/pbA3Detail/pageList',
    method: 'POST',
    data: {
      pageSize: 1,
      size: 1,
      problemCode
    }
  })
}

// A3详情保存
export const apiPbDetailA3Save = (data) => {
  return request({
    url: `/pb_api/pbA3Detail/${data.id}`,
    method: 'PUT',
    data
  })
}

// ===================== 行动计划 =====================
// Ac详情列表 行动计划
export const apiPbDetailAcPage = (problemCode) => {
  return request({
    url: '/pb_api/pbActionDetail/pageList',
    method: 'POST',
    data: {
      pageSize: 1,
      size: 1,
      problemCode
    }
  })
}

// Ac详情保存 行动计划
export const apiPbDetailAcSave = (data) => {
  return request({
    url: `/pb_api/pbActionDetail/${data.id}`,
    method: 'PUT',
    data
  })
}

// ===================== 子任务 =====================
// 子任务列表
export const apiPbProblemTaskAll = (problemCode) => {
  return request({
    url: `/pb_api/pbProblemTask/pageList`,
    method: 'POST',
    data: {
      page: 1,
      size: 9999999,
      problemCode: problemCode
    }
  })
}

// 子任务新增
export const apiPbProblemTaskAdd = (data) => {
  return request({
    url: `/pb_api/pbProblemTask`,
    method: 'POST',
    data: data
  })
}

// 子任务修改
export const apiPbProblemTaskUpdate = (id, data) => {
  return request({
    url: `/pb_api/pbProblemTask/${id}`,
    method: 'PUT',
    data: data
  })
}

// 子任务删除
export const apiPbProblemTaskDelete = (id) => {
  return request({
    url: `/pb_api/pbProblemTask/${id}`,
    method: 'DELETE'
  })
}

// ===================== A3问题关闭人配置 =====================
// A3问题关闭人配置分页列表
export const apiPbA3CloseRulePage = (data) => {
  return request({
    url: `/pb_api/pbA3CloseRule/pageList`,
    method: 'POST',
    data
  })
}

// A3问题关闭人配置 新增
export const apiPbA3CloseRuleAdd = (data) => {
  return request({
    url: `/pb_api/pbA3CloseRule`,
    method: 'POST',
    data
  })
}

// A3问题关闭人配置 修改
export const apiPbA3CloseRuleUpdate = (data) => {
  return request({
    url: `/pb_api/pbA3CloseRule/${data.id}`,
    method: 'PUT',
    data: data
  })
}

// A3问题关闭人配置 删除
export const apiPbA3CloseRuleDelete = (id) => {
  return request({
    url: `/pb_api/pbA3CloseRule/${id}`,
    method: 'DELETE'
  })
}

// ===================== 行动计划责任人配置 =====================
// 行动计划责任人配置分页列表
export const apiPbActionPlanCloseRulePage = (data) => {
  return request({
    url: `/pb_api/pbActionPlanCloseRule/pageList`,
    method: 'POST',
    data
  })
}

// 行动计划责任人配置 新增
export const apiPbActionPlanCloseRuleAdd = (data) => {
  return request({
    url: `/pb_api/pbActionPlanCloseRule`,
    method: 'POST',
    data
  })
}

// 行动计划责任人配置 修改
export const apiPbActionPlanCloseRuleUpdate = (data) => {
  return request({
    url: `/pb_api/pbActionPlanCloseRule/${data.id}`,
    method: 'PUT',
    data: data
  })
}

// 行动计划责任人配置 删除
export const apiPbActionPlanCloseRuleDelete = (id) => {
  return request({
    url: `/pb_api/pbActionPlanCloseRule/${id}`,
    method: 'DELETE'
  })
}

// ===================== 上板审核人配置 =====================
// 行动计划责任人配置分页列表
export const apiPbBlockRulePage = (data) => {
  return request({
    url: `/pb_api/pbStartApprovalRule/pageList`,
    method: 'POST',
    data
  })
}

// 行动计划责任人配置 新增
export const apiPbBlockRuleAdd = (data) => {
  return request({
    url: `/pb_api/pbStartApprovalRule`,
    method: 'POST',
    data
  })
}

// 行动计划责任人配置 修改
export const apiPbBlockRuleUpdate = (data) => {
  return request({
    url: `/pb_api/pbStartApprovalRule/${data.id}`,
    method: 'PUT',
    data: data
  })
}

// 行动计划责任人配置 删除
export const apiPbBlockRuleDelete = (id) => {
  return request({
    url: `/pb_api/pbStartApprovalRule/${id}`,
    method: 'DELETE'
  })
}

// ===================== 行动计划子任务 =====================
// 行动计划子任务列表
export const apiPbProblemAcTaskAll = (problemCode) => {
  return request({
    url: `/pb_api/pbActionSubtask/pageList`,
    method: 'POST',
    data: {
      page: 1,
      size: 9999999,
      problemCode: problemCode
    }
  })
}

// 行动计划子任务分野
export const apiPbProblemAcTaskPage = (data) => {
  return request({
    url: `/pb_api/pbActionSubtask/pageList`,
    method: 'POST',
    data
  })
}

// 行动计划子任务新增
export const apiPbProblemAcTaskAdd = (data) => {
  return request({
    url: `/pb_api/pbActionSubtask`,
    method: 'POST',
    data: data
  })
}

// 行动计划子任务修改
export const apiPbProblemAcTaskUpdate = (data) => {
  return request({
    url: `/pb_api/pbActionSubtask/${data.id}`,
    method: 'PUT',
    data: data
  })
}

// 行动计划子任务删除
export const apiPbProblemAcTaskDelete = (id) => {
  return request({
    url: `/pb_api/pbActionSubtask/${id}`,
    method: 'DELETE'
  })
}
