<script setup>
// eslint-disable-next-line no-unused-vars
import { computed, reactive, ref, watch, getCurrentInstance } from 'vue'
import { Loading, ArrowDown } from '@element-plus/icons-vue'
import uniqBy from 'lodash/uniqBy'
const instance = getCurrentInstance()
const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  // 数据
  modelValue: {
    type: [String, Array],
    required: true,
    default: ''
  },
  // 请求
  request: {
    type: Function,
    required: true,
    default: () => null
  },
  // 请求参数
  paramsKey: {
    type: String,
    required: true,
    default: ''
  },
  optionValue: {
    type: String,
    required: true,
    default: ''
  },
  optionLabel: {
    type: String,
    required: true,
    default: ''
  },
  montage: {
    type: Boolean,
    default: true
  },
  index: {
    type: Number,
    default: null
  }
})

const pages = reactive({
  page: 1,
  size: 20,
  total: 0
})

const selectOptions = computed(() => ({
  filterable: true,
  remote: true,
  reserveKeyword: true,
  clearable: true,
  remoteShowSuffix: true
}))

const options = ref([])
const loading = ref(false)
const isload = ref(false)

const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    emits('change', val, options.value, props.index)
  }
})

const remoteMethod = (value) => {
  if (value) {
    getOptionsByRequest(value)
  }
}

const visibleChange = (value) => {
  text.value = ''
  if (value) {
    getOptionsByRequest(props.modelValue)
  }
}

//切换分页
const text = ref(null)
const selectName = ref(null)
const handleCurrentChange = (e) => {
  getPageList()
  text.value = keyWords.value
}

// 请求
const keyWords = ref('')
let timer = ref(null)
const getOptionsByRequest = async (value = '') => {
  pages.page = 1
  text.value = ''
  keyWords.value = value
  if (typeof props.request === 'function') {
    if (!props.paramsKey) return
    loading.value = true
    if (timer.value) clearTimeout(timer.value)
    timer.value = setTimeout(async () => {
      try {
        const params = {
          [props.paramsKey]: value
        }
        const result = await props.request(pages, params)
        const list = result.list.map((item) => {
          return {
            ...item,
            label: item[props.optionLabel],
            value: item[props.optionValue]
          }
        })
        options.value = uniqBy(list, (item) => item[props.optionValue])
        pages.total = result.total
        isload.value = true
      } catch (e) {
        options.value = []
        pages.total = 0
      } finally {
        loading.value = false
      }
    }, 500)
  }
}

const getPageList = async () => {
  try {
    const params = {
      [props.paramsKey]: keyWords.value
    }
    const result = await props.request(pages, params)
    const list = result.list.map((item) => {
      return {
        ...item,
        label: item[props.optionLabel],
        value: item[props.optionValue]
      }
    })
    options.value = uniqBy(list, (item) => item[props.optionValue])
    pages.total = result.total
  } catch (error) {
    options.value = []
    pages.total = 0
  }
}
const clearFn = () => {
  // props.modelValue = ''
  modelValue.value = ''
  console.log(modelValue.value, 3333)
  // modelValue.value
}
const pageClick = (event) => {
  event.preventDefault()
}

watch(
  () => props.modelValue,
  (val) => {
    if (val && !isload.value) {
      getOptionsByRequest(val)
    }
  },
  { immediate: true }
)
</script>

<template>
  <el-select
    v-model="modelValue"
    class="el-select-remote"
    :class="{ 'is-loading': loading }"
    ref="selectName"
    :loading="loading"
    v-bind="($attrs, selectOptions)"
    :suffix-icon="loading ? Loading : ArrowDown"
    :placeholder="text ? text : $t('form.pleaseInputKeyWord')"
    filterable
    @clear="clearFn"
    :remote-method="remoteMethod"
    @visible-change="visibleChange"
  >
    <el-option v-for="(item, index) in options" :key="index" :value="item.value" :label="montage ? `${item.label} (${item.value})` : item.label" />
    <!-- <template #footer> -->
    <el-col :span="24">
      <div class="bottomPage">
        <el-pagination @click="pageClick" @current-change="handleCurrentChange" v-model:currentPage="pages.page" v-model:page-size="pages.size" layout="total, prev, pager, next" :total="pages.total">
        </el-pagination>
      </div>
    </el-col>
    <!-- </template> -->
  </el-select>
</template>

<style lang="scss" scoped>
.el-select-remote {
  min-width: 160px;
}
.el-select-remote.is-loading .el-icon svg {
  color: $primary !important;
  animation: rotate 1s linear infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
.bottomPage {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  display: flex;
  margin: 5px 10px 5px 20px;
}
</style>
