<script setup>
import { ref, watchEffect } from 'vue'

const props = defineProps({
  scroll: {
    type: Boolean,
    default: true
  }
})

const scroll = ref(props.scroll)
watchEffect(() => {
  scroll.value = props.scroll
})
</script>

<template>
  <div class="base-page">
    <el-scrollbar v-if="scroll" wrap-class="scrollbar-wrapper">
      <slot />
    </el-scrollbar>
    <slot v-else />
  </div>
</template>

<style lang="scss" scoped>
.base-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
