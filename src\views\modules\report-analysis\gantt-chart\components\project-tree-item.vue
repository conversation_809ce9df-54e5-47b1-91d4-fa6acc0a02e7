<template>
  <div 
    class="project-tree-item"
    :class="{ 
      'has-children': project.hasChildren,
      'expanded': project.expanded,
      'loading': project.loading,
      'selected': selectedProjectId === project.id
    }"
    :style="{ 
      height: rowHeight + 'px',
      paddingLeft: (level * 20 + 16) + 'px'
    }"
    @click="handleClick"
  >
    <!-- 展开/收起图标 -->
    <div class="expand-icon" v-if="project.hasChildren" @click.stop="handleToggle">
      <el-icon v-if="project.loading" class="loading-icon">
        <Loading />
      </el-icon>
      <el-icon v-else-if="project.expanded">
        <ArrowDown />
      </el-icon>
      <el-icon v-else>
        <ArrowRight />
      </el-icon>
    </div>
    
    <!-- 项目图标 -->
    <div class="project-icon">
      <el-icon v-if="project.hasChildren">
        <Folder />
      </el-icon>
      <el-icon v-else>
        <Document />
      </el-icon>
    </div>
    
    <!-- 项目信息 -->
    <div class="project-info">
      <div class="project-name" :title="project.name">
        {{ project.name }}
      </div>
      <div class="project-details" v-if="project.rawData">
        <div class="project-code">{{ project.rawData.prodCode }}</div>
        <div class="project-customer">{{ project.rawData.prodCustomer }}</div>
        <div class="project-dates">
          {{ project.rawData.prodCreateTime }} ~ {{ project.rawData.prodDeliveryTime }}
        </div>
      </div>
    </div>

    <!-- 项目状态指示器 -->
    <div class="project-status" v-if="project.status">
      <el-tag
        :type="getStatusType(project.status)"
        size="small"
        effect="plain"
      >
        {{ project.status }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { 
  ArrowDown, 
  ArrowRight, 
  Folder, 
  Document, 
  Loading 
} from '@element-plus/icons-vue'

const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  rowHeight: {
    type: Number,
    default: 80
  },
  loadingProjects: {
    type: Set,
    default: () => new Set()
  },
  selectedProjectId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['toggle', 'load-children', 'select'])

// 处理点击事件
const handleClick = () => {
  emit('select', props.project)
}

// 处理展开/收起
const handleToggle = () => {
  if (props.project.hasChildren) {
    emit('toggle', props.project)
    
    // 如果需要加载子项目
    if (!props.project.expanded && (!props.project.children || props.project.children.length === 0)) {
      emit('load-children', props.project)
    }
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    '进行中': 'primary',
    '已完成': 'success',
    '已延期': 'danger',
    '未开始': 'info',
    '已暂停': 'warning'
  }
  return statusMap[status] || 'info'
}
</script>

<style scoped lang="scss">
.project-tree-item {
  display: flex;
  align-items: flex-start;
  padding-top: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.selected {
    background-color: #e6f7ff;
    border-color: #409eff;
    
    .project-name {
      color: #409eff;
      font-weight: 600;
    }
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  .expand-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    cursor: pointer;
    border-radius: 2px;
    transition: background-color 0.2s;
    
    &:hover {
      background-color: #e6f7ff;
    }
    
    .loading-icon {
      animation: rotate 1s linear infinite;
    }
  }
  
  .project-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: #606266;
  }
  
  .project-info {
    flex: 1;
    min-width: 0; // 确保flex子项可以收缩

    .project-name {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 4px;
    }

    .project-details {
      font-size: 12px;
      color: #909399;
      line-height: 1.4;

      .project-code {
        font-weight: 500;
        color: #606266;
        margin-bottom: 2px;
      }

      .project-customer {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 2px;
      }

      .project-dates {
        font-size: 11px;
        color: #c0c4cc;
      }
    }
  }
  
  .project-status {
    margin-left: 8px;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
