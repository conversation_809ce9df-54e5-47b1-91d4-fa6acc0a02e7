import request from '@/common/request.js'
//获取容器分页查询
export const fetchGetContainerData = (params, data) => {
  return request({
    url: '/mes_api/v1/wmStorageUnit/queryContainer',
    method: 'POST',
    params,
    data
  })
}

//新增或者编辑容器
// export const fetchSaveOrUpdateContainer = (data) => {
//   return request({
//     url: '/mes_api/v1/wmStorageUnit/saveOrUpdate',
//     method: 'POST',
//     data
//   })
// }

// 新增容器
export const fetchSaveContainer = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageUnit/cntrCreate',
    method: 'POST',
    data
  })
}

// 编辑容器
export const fetchUpdateContainer = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageUnit/cntrUpdate',
    method: 'POST',
    data
  })
}

//删除容器
export const fetchDeleteContainer = (params) => {
  return request({
    url: `/mes_api/v1/wmStorageUnit/deleteContainer`,
    method: 'DELETE',
    params
  })
}

//容器占用详情
export const fetchContainerOccupyDetail = (data) => {
  return request({
    url: `/mes_api/v1/wmStorageUnit/queryOccpConQuant`,
    method: 'post',
    data
  })
}
