<script setup>
import { ref, computed } from 'vue'
import { ApiActivitiHistoryAll } from '@/api/modules/bpmn'

// 对话框配置项
const dialogVisible = ref(false)
const dialogOptions = computed(() => {
  return {
    title: '审核记录',
    width: '1000px',
    top: '10%',
    closeOnClickModal: false
  }
})

const historyTabActive = ref('')
const historyTabData = ref([])
const openDialog = async ({ pageCode, docCode }) => {
  dialogVisible.value = true
  historyTabData.value = await ApiActivitiHistoryAll({ pageCode, docCode })
  if (historyTabData.value.length > 0) {
    historyTabActive.value = historyTabData.value[0].modelId + '0'
  }
}

// #Expose
defineExpose({
  openDialog
})
</script>
<template>
  <el-dialog v-model="dialogVisible" v-bind="dialogOptions">
    <el-tabs v-model="historyTabActive">
      <el-tab-pane :label="item.modelName" :name="item.modelId + idx" v-for="(item, idx) in historyTabData" :key="item.modelId + idx">
        <div style="height: 500px" class="flex-column">
          <base-table fill border :data="item.historyList">
            <el-table-column prop="id" label="ID" align="center" class-name="select-all" />
            <el-table-column prop="assignee" label="审批人" align="center" />
            <el-table-column prop="status" label="状态" align="center" />
            <el-table-column prop="comment" label="审批意见" align="center" />
            <el-table-column prop="startTime" label="开始时间" align="center" />
            <el-table-column prop="endTime" label="结束时间" align="center" />
          </base-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.el-form {
  width: 90%;
}
</style>
