import zhLocale from './zh.js'
import enLocale from './en.js'
import enJson from './en.json'
import zhJson from './zh.json'
import { createI18n, } from 'vue-i18n'
const defaultLangue = localStorage.getItem('langue') || 'zh'
// 语言集
const messages = {
  zh: {
    ...zhLocale,
    ...zhJson,
  },
  en: {
    ...enLocale,
    ...enJson,
  }
}
const i18n = createI18n({
  legacy: false,
  globalInjection: true, // 全局模式，可以直接使用 $t
  locale: defaultLangue,
  messages
})
Object.assign(i18n, {
  $t: i18n.global.t,
  t: i18n.global.t,
})
export default i18n
