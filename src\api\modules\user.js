import request from '@/common/request.js'

/*
 *  获取用户分页列表
 *  @param { params }
 *  @returns { Promise }
 *
 *  { params }
 *  keywords:String,                // 搜索关键字
 *  status: String,                 // 状态 0:禁用 1:启用
 *  deptId: Int,                    // 部门ID
 *  pageNum: Int,                   // 页码
 *  pageSize: Int,                  // 每页条数
 */
export const GetUserPages = (params) => {
  return request({
    url: '/api/v1/user/pages',
    method: 'GET',
    params
  })
}

// 用于select-remote
export const GetUserPagesSelect = (params, data) => {
  return request({
    url: '/api/v1/user/pages',
    method: 'GET',
    params: {
      ...params,
      ...data
    }
  })
}

// 获取所有用户
export const GetAllUserPages = () => {
  return request({
    url: '/api/v1/user/pages',
    method: 'GET',
    params: { size: 9999999 }
  })
}

/*
 *  新增用户
 *  @param { void }
 *  @data { user }
 *  @returns { Promise }
 *
 *  { user }
 *  accountName: String,            // 账号
 *  userName: String,               // 用户名称
 *  sex: Int,                       // 性别 1:男 2:女 0:未知
 *  age: Int,                       // 年龄
 *  email: String,                  // 邮箱
 *  idCard: String,                 // 身份证号
 *  status: Int,                    // 状态 0:禁用 1:启用
 *  staffCode: String,              // 工号 *required
 *  mobile: String,                 // 手机号 *required
 *  deptId: Int,                    // 部门ID *required
 *  identityId: Int,                // 身份标识 *required
 *  roleIds: String,                // 角色ID集合 ,拼接 *required
 *
 *  NO-US-02 增加微信账号维护、职位维护，去掉性别未知选项、去掉身份字段
 *  wxAccount: String,              // 微信号
 *  jobName: String,                // 职位名称
 *  companyName: String,            // 公司名称
 *  companyId: Int,                 // 公司ID
 */
export const AddUser = (data) => {
  return request({
    url: '/api/v1/user/save',
    method: 'POST',
    data
  })
}
/*
 *  修改用户
 */
export const EditUser = (data) => {
  return request({
    url: `/api/v1/user/${data.userId}/update`,
    method: 'PUT',
    data
  })
}

/*
 *  获取用户根据角色ID
 *  @param { params } roleId
 *  @returns { Promise }
 */
export const GetUserByRoleId = (id) => {
  return request({
    url: '/api/v1/roles/userByRole',
    method: 'GET',
    params: {
      roleId: id
    }
  })
}

/*
 *  删除用户
 *  @param { userid }
 *  @returns { Promise }
 */
export const DelUserByIds = (ids) => {
  return request({
    url: `/api/v1/user/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  批量添加角色
 *  @param { users,roles }
 *  @returns { Promise }
 */
export const AddUserRoles = (userIds, roleIds) => {
  return request({
    url: `/api/v1/user/batchRole`,
    method: 'POST',
    data: {
      userIds,
      roleIds
    }
  })
}

/*
 *  用户详情
 *  @param { users,roles }
 *  @returns { Promise }
 */
export const GetUserDetailById = (id) => {
  return request({
    url: `/api/v1/user/${id}/form`,
    method: 'GET'
  })
}

/*
 *  登录
 *  @param { params }
 *  @returns { Promise }
 */
export const Login = (params) => {
  return request({
    url: `/api/user/login`,
    method: 'POST',
    withToken: false,
    params
  })
}

/*
 *  修改用户密码
 *  @param { params }
 *  @returns { Promise }
 */
export const ChangePassword = (accountId, params) => {
  return request({
    url: `/api/v1/user/${accountId}/password`,
    method: 'PUT',
    // withToken: false,
    params
  })
}

/*
 * TPM
 *  查询系统默认角色
 *  @param { params }
 *  @returns { Promise }
 */

// export const queryTPMRoleList = (params) => {
//   return request({
//     url: `/tpm_api/v1/tpFlowRole/flowRole`,
//     method: 'GET',
//     params
//   })
// }

export const queryTPMRoleList = (params) => {
  return request({
    url: `/tpm_api/v1/sysDict/list`,
    method: 'GET',
    params
  })
}

/*
 * TPM
 *  查询系统默认角色关联的用户角色
 *  @param { params }
 *  @returns { Promise }
 */

export const querydefaultRoleList = (page, data) => {
  return request({
    url: `/tpm_api/v1/tpFlowRole/pages?page=${page.page}&size=${page.size}`,
    method: 'POST',
    data
  })
}

/*
 * TPM
 *  删除系统默认角色关联的用户角色
 *  @param { params }
 *  @returns { Promise }
 */

export const delDefaultRoleList = (ids) => {
  return request({
    url: `/tpm_api/v1/tpFlowRole/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 * TPM
 *  批量保存默认角色关联的用户角色
 *  @param { params }
 *  @returns { Promise }
 */

export const saveBachRelativeRoleList = (data) => {
  return request({
    url: `/tpm_api/v1/tpFlowRole/saveBach`,
    method: 'POST',
    data
  })
}

/*
 *  用户中心
 *  查询用户的管理范围数据
 *  @param { params }
 *  @returns { Promise }
 */

export const queryUserInfo = (userId) => {
  return request({
    url: `/api/v1/user/${userId}/form`,
    method: 'GET'
  })
}

/*
 *  用户中心
 *  修改用户的管理范围
 *  @param { params }
 *  @returns { Promise }
 */

export const editUserScopeList = (data) => {
  return request({
    url: `/api/v1/sysUserScope/saveBach`,
    method: 'POST',
    data
  })
}

export const getUserAllList = (data) => {
  return request({
    url: '/api/v1/user/pages',
    method: 'GET',
    data
  })
}

// 根据编码获取编码所属关系
export const getFactorySuperList = (code) => {
  return request({
    url: '/api/v1/factory/super',
    method: 'GET',
    params: { code: code }
  })
}

/**
 * @description:获取交接班记录
 * @return { Promise }
 * @param params
 * @param data
 */

export const GetShiftRecord = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/shiftRecord/pages',
    method: 'POST',
    params,
    data
  })
}
