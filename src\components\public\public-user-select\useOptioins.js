import { ref } from 'vue'
import { GetAllUserPages as apiRequst } from '@/api/modules/user'
const valueKey = 'accountName'
const labelKey = 'userName'

const optionList = ref([])
let isload = false
let loading = false

function loadOption() {
  if (isload || loading) return
  loading = true
  apiRequst()
    .then((res) => {
      loading = false
      optionList.value = (res.list || []).map((i) => ({
        value: i[valueKey],
        label: i[labelKey]
      }))
      isload = true
    })
    .catch(() => {
      loading = false
      isload = false
    })
}

export function isLoaded() {
  return isload
}

export function getOptionList() {
  loadOption()
  return optionList
}
