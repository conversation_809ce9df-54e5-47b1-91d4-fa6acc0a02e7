export default {
  // 路由菜单
  menu: {
    // 用户中心
    userCenter: {
      module: 'User Center',
      personalCenter: 'Personal Center',
      user: 'User ',
      organization: 'Organizational',
      permission: 'Permission',
      role: 'Role',
      factoryModel: 'Factory Model',
      dictionary: 'Dictionary',
      shift: 'Shift',
      partner: 'Partner'
    },
    //质量
    quality: {
      management: 'Quality Control',
      procurementQuality: 'Purchase Quality',
      qualityInspection: 'Quality Inspection',
      processControl: 'Process Control',
      productionRelease: 'Production Release',
      releaseRecord: 'Release Record',
      unqualifiedReview: 'Review Of Nonconform Products',
      processInspection: 'Process Inspection',
      processInspectionRecord: 'Process Inspection Record',
      processInspectionConfig: 'Process inspection configuration',
      addProcessConfig: 'Process inspection configuration-Add',
      editProcessConfig: 'Process inspection configuration-Edit',
      qualityTrace: 'Quality Trace',
      singleTrace: 'Single Trace',
      batchTrace: 'Batch Trace',
      trackNoTrace: 'Track No. Trace',
      inspectionRecord: 'Inspection Record',
      inspectionRecordDetail: 'Inspection Record Detail',
      inspectionConfig: 'Inspection Configuration',
      inspectionConfigAdd: 'Inspection Configuration-Add',
      inspectionConfigEdit: 'Inspection Configuration-Edit',
      inspectionReview: 'Inspection Review',
      layerAudit: 'Layer Audit',
      score: 'Score',
      organizationManagement: 'Organization Management',
      userManagement: 'User Management',
      userManagementEdit: 'User Management-Edit',
      roleManagement: 'Role Management',
      shiftManagement: 'Shift Management',

    },
    masterData: {
      masterManagement: 'Master Data',
      masterConfig: 'Configuration',
      materail: 'Material',
      materailAdd: 'Material-Add',
      materailEdit: 'Material-Edit',
      materailDetail: 'Material-Detail',
      stationConfig: 'Station Configuration',
      defectMode: 'Defect Mode'
    },
    oee: {
      masterConfig: 'Master Data',
      shutdownType: 'Shutdown Type',
      ratedBeat: 'Rated Beat',
      reportAnalysis: 'Report Analysis',
      shiftOee: 'Shift OEE Statistics',
      shiftYield: 'Shift Yield Statistics',
      shiftBeat: 'Shift Beat Statistics',
      shutdownReason: 'Shutdown Reasons Statistics',
      dataRecord: 'Data Record',
      shutdownRecord: 'Shutdown Record',
      yieldRecord: 'Yield Record',
      oeeBoard: 'OEE Board',
      outputStatistics: 'Output Statistics'
    },
    iiot: {
      middleware: 'IIOT Middleware',
      config: 'Configuration',
      opcua: 'OPC UA Connection',
      eventRules: 'Event Rules',
      deviceConnect: 'Device Connection',
      iiotStation: 'Workstation IIOT Traceability',
      opcuaNotificationObject: 'Opcua Notification Object',
      equipmentManage: 'Equipment Manage',
      equipmentSetting: 'Equipment Setting'
    },
    planManage: {
      planning: 'Planned Management',
      planOrder: 'Planned Order',
      planOrderAdd: 'Planned Order-Add',
      planOrderEdit: 'Planned Order-Edit',
      orderReport: 'Order Analysis',
      loadAnalysis: 'Load Analysis',
      processAnalysis: 'Progress Analysis'
    }
  },
  // 通用
  base: {
    langue: 'Language',
    resourceDownload: 'Resource Download',
    backHome: 'Back Home',
    back: 'Back',
    exit: 'Exit',
    zh: 'zh',
    en: 'En',
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    clear: 'Clear',
    dataList: 'Data List',
    open: 'Open',
    close: 'Close',
    closed: 'Close',
    role: 'Role',
    noRole: 'No Role',
    status: 'Status',
    tips: 'Tips',
    confirm: 'Confirm',
    cancel: 'Cancel',
    enable: 'Enable',
    notEnable: 'Not Enable',
    disable: 'Disable',
    No: 'No.',
    name: 'Name',
    sex: 'Sex',
    email: 'Email',
    phone: 'Phone',
    employeeId: 'Employee ID',
    company: 'Company',
    ID: 'ID',
    organization: 'Organization',
    account: 'Account',
    operation: 'Operation',
    remove: 'Remove',
    level: 'Level',
    system: 'System',
    catalog: 'Catalog',
    menu: 'Menu',
    button: 'Button',
    other: 'Other',
    factory: 'Factory',
    workShop: 'Work Shop',
    workSection: 'Work Section',
    line: 'Line',
    station: 'Work Station',
    equipment: 'Equipment',
    type: 'Type',
    code: 'Code',
    originName: 'Name',
    superior: 'Superior',
    remark: 'Remark',
    supplier: 'Supplier',
    customer: 'Customer',
    save: 'Save',
    time: 'Time',
    startTime: 'Start Time',
    endTime: 'End Time',
    whetherEnable: 'Whether Enable',
    shift: 'Shift',
    batchDelete: 'Batch Delete',
    baseInfo: 'Basic Information',
    age: 'Age',
    position: 'Position',
    wx: 'Wechat Number',
    number: 'Number',
    unit: 'Unit',
    qualified: 'Qualified',
    unqualified: 'Unqualified',
    createTime: 'Create Time',
    detailsView: 'Details',
    revoke: 'Revoke',
    productCode: 'Product Code',
    productName: 'Product Name',
    mateCode: 'Material Code',
    mateName: 'Material Name',
    mateNo: 'Material No.',
    storage: 'Storage Location',
    orderNo: 'Order No.',
    orderCode: 'Order Code',
    process: 'Process',
    operator: 'Operator',
    operateTime: 'Operation Time',
    inStation: 'In-Station',
    outStation: 'Out-Station',
    trackNo: 'Track No.',
    batchNo: 'Batch No.',
    supplierCode: 'Supplier Code',
    supplierName: 'Supplier Name',
    output: 'Output',
    inspection: 'Inspection',
    result: 'Result',
    inspector: 'Inspector',
    inspectionTime: 'Inspection Time',
    detail: 'Details',
    creator: 'Creator',
    deviceCode: 'Device Code',
    factoryModel: 'Factory Model',
    productionObject: 'Production Object',
    dimension: 'Dimension',
    yes: 'Yes',
    no: 'No',
    date: 'Date',
    month: 'Month',
    export: 'Export',
    total: 'Total',
    currentShift: 'Current Shift',
    error: 'Error',
    h: 'h',
    m: 'm',
    s: 's',
    startDate: 'Start Date',
    endDate: 'End Date',
    timeTo: 'to',
    copy: 'Copy',
    config: 'Configuration',
    GraphicalConfiguration: 'Graphical Configuration',
    processCode: 'processCode',
    processName: 'processName',
    processType: 'processType',
    upload: 'Upload',
    StationNumber: 'Station Number',
    stationName: 'station Name',
    toolingNumber: 'Tooling Number',
    toolingName: 'Tooling Name',
    submit: 'Submit',
    addTo: 'Add',
    customerCode: 'Customer Code',
    customerName: 'Customer Name',
    department: 'Department'
  },
  // 消息提示
  message: {
    confirm: 'Are you sure to do this?',
    confirmDelete: 'Are you sure to delete?',
    deleteSuccess: 'Delete Successful',
    addSuccess: 'Add Successful',
    editSuccess: 'Edit Successful',
    modifySuccess: 'Modify Successful',
    saveSuccess: 'Save Successful',
    submitSuccess: 'Submit Successful',
    confirmRemoveUser: 'Are you sure to remove this user?',
    removeSuccess: 'Remove Successful',
    searchFail: 'Data search failed',
    noData: 'No Data',
    selectAtLeastOne: 'Please select at least one option for operation',
    operationSuccess: 'Operation Successful',
    emptySubmit: 'Cannot submit empty data',
    selectReviewData: 'Please select review data',
    inspectionReviewSubmit: 'Only when the quality inspection status is pending can the quality inspection be submitted',
    matchAllInspectionList: 'Does it match all the pending quality inspection lists that meet this condition?',
    selectFourSame: 'The selected data should belong to the same material, batch, warehouse, and quality inspection scope',
    selectOneFullCheck: 'Only one full inspection type can be selected for quality inspection review',
    revokeSuccess: 'revoke Successful'
  },
  // 表单
  form: {
    keySearch: 'Please enter keywords to search',
    selectRole: 'Please select role',
    pleaseInput: 'Please input',
    pleaseSelect: 'Please select',
    selectPermission: 'Please select permission',
    selectType: 'Please select type',
    inputCode: 'Please input code',
    inputOriginName: 'Please input name',
    inputPhone: 'Please input phone',
    inputEmail: 'Please input email',
    emailValidate: 'Please enter the correct email format',
    inputPassWord: 'Please input password',
    inputPassWordMore: 'Please input password again',
    inputAccount: 'Please input account',
    inputAge: 'Please input age',
    inputName: 'Please input name',
    selectSex: 'Please select sex',
    inputIdentityId: 'Please input identity id',
    inputEmployeeId: 'Please input employee id',
    inputID: 'Please input ID',
    IDValidate: 'Please enter the correct ID card format',
    inputJob: 'Please input the position',
    selectCompany: 'Please select the company',
    inputWx: 'Please enter your WeChat ID',
    selectUserType: 'Please select user type',
    selectPartner: 'Please select partner',
    selectDept: 'Please select department',
    pleaseInputKeyWord: 'Please input keyword',
    selectFactory: 'Please select factory',
    selectWorkShop: 'Please select work shop',
    selectSection: 'Please select work section',
    selectLine: 'Please select line',
    selectStation: 'Please select station',
    selectEquipment: 'Please select equipment',
    inputProductNum: 'Please input product number',
    inputProductName: 'Please input product name',
    selectProductObjectType: 'Please select production object type',
    inputProductObjectNum: 'Please input production object number',
    inputProductObjectName: 'Please input production object name',
    inputRatedBeat: 'Please input rated beat',
    selectMonth: 'Please select month',
    selectWeek: 'Please select week',
    selectDimension: 'Please select dimension',
    selectShutdownType: 'Please select shutdown type',
    selectShutdownReason: 'Please select shutdown reason',
    selectIsPlanned: 'Please select whether it is planned',
    selectStartTime: 'Please select start time',
    selectEndTime: 'Please select end time',
    inputShutdownDesc: 'Please input shutdown description',
    inputNumber: 'Please input number',
    inputActualBeat: 'Please input actual beat',
    selectOutputTime: 'Please select output time',
    selectQualified: 'Please select whether it is qualified',
    selectOnePass: 'Please select whether it is once passed',
    selectDateOrWeek: 'Please select date or week',
    selectMatCode: 'Please select material code',
    systemBringsOut: 'System Brought Out'
  },
  // 功能模块

  /**
   * 用户中心-----------------------------------------------------------
   */

  // 角色配置
  role: {
    member: 'Members',
    authSetting: 'Permission Configuration',
    roleName: 'Role Name',
    roleCode: 'Role Code',
    addRole: 'Add Role',
    editRole: 'Edit Role',
    managementScope: 'Management Scope',
    permissionChange: 'Permission Change',
    permissionChangeSuccess: 'Permission change successful',
    inputName: 'Please input role name',
    inputCode: 'Please input role code'
  },
  // 权限管理
  permission: {
    permissionType: 'Permission Type',
    permissionCode: 'Permission Code',
    permissionName: 'Permission Name',
    addPermission: 'Add Permission',
    editPermission: 'Edit Permission',
    permissionChange: 'Change Permission',
    selectType: 'Please select permission type',
    inputName: 'Please input permission name',
    inputRoute: 'Please input route address',
    inputCode: 'Please input permission code'
  },
  // 字典管理
  dict: {
    dictName: 'Dictionary Name',
    dictCode: 'Dictionary Code',
    dictType: 'Dictionary Type',
    dictValue: 'Dictionary Value',
    dictGroup: 'Dictionary Group',
    dictExplain: 'Dictionary Explain',
    dictLabel: 'Dictionary Label',
    dictSort: 'Dictionary Sort',
    addDict: 'Add Dictionary',
    editDict: 'Edit Dictionary',
    dictChange: 'Dictionary Change',
    dictChangeSuccess: 'Dictionary change successful'
  },
  // 工厂模型
  factory: {
    pleaseInput: 'Please input department or workshop',
    addOrganization: 'Add Organization',
    chooseModel: 'Please choose factory model'
  },
  // 组织架构
  organ: {
    addOrganization: 'Add Organization',
    inputOrganName: 'Please input organization name',
    selectOrganType: 'Please select organization type',
    selectOrgan: 'Please select organization',
    superiorOrgan: 'Superior Organization',
    organType: 'Organization Type',
    organName: 'Organization Name'
  },
  // 合作伙伴
  partner: {
    partner: 'Partner',
    code: 'Partner Code',
    name: 'Partner Name',
    type: 'Partner Type',
    contact: 'Contact',
    phone: 'Phone',
    address: 'Address',
    remark: 'Remark',
    inputCode: 'Please input partner code',
    inputName: 'Please input partner name',
    inputContact: 'Please input contact',
    inputPhone: 'Please input phone',
    inputAddress: 'Please input address'
  },
  // 个人中心
  person: {
    personalData: 'Personal Data',
    accountInfo: 'Account Information',
    password: 'Password',
    newPassWord: 'New Password',
    confirmPassWord: 'Confirm Password',
    noMatchPassWord: 'The two passwords do not match',
    modifySuccess: 'Modify password successful',
    phoneEmpty: 'The phone number cannot be empty',
    phoneFormat: 'The format of the phone number is incorrect'
  },
  //   班次管理
  shift: {
    code: 'Shift Code',
    name: 'Shift Name',
    selectFactory: 'Please select factory',
    inputCode: 'Please input shift code',
    inputName: 'Please input shift name',
    selectStart: 'Please select start time',
    selectEnd: 'Please select end time'
  },
  //   用户管理
  user: {
    checkUser: 'Please select user',
    confirmReset: "Are you sure to reset this user's password?",
    resetSuccess: 'Reset Successful',
    userList: 'User List',
    userNumber: 'members',
    pleaseInput: 'Please input user name/nick name/phone',
    addUser: 'Add User',
    batchAssignRoles: 'Batch Assign Roles',
    deptMove: 'Move Into Department',
    resetPassWord: 'Reset Password',
    userType: 'User Type',
    externalUsers: 'External Users',
    internalUsers: 'Internal Users',
    roleAssign: 'Role Assign',
    jurisdictionScope: 'Scope of Jurisdiction',
    partner: 'Partner',
    editUser: 'Edit User',
    allocateSuccess: 'Allocation Successful'
  },

  /**
   * 质量管理-----------------------------------------------------------
   */

  // 采购质量
  purchase: {
    qualityOrder: 'Quality Control No.',
    warehousOrder: 'Warehouse No.',
    mateCode: 'Material Code',
    mateName: 'Material Name',
    trackNo: 'Track No.',
    supplierCode: 'Supplier Code',
    supplierName: 'Supplier Name',
    qualityResult: 'Inspection Result',
    taskStatus: 'Task Status',
    inspectionTime: 'Inspection Time',
    inspectionList: 'Inspection List',
    damageNum: 'Number Of Damages',
    batchNo: 'Batch No.',
    trackNoType: 'Track No. Type',
    trayNo: 'Tray No.',
    storageNo: 'Warehouse Location No.',
    inspector: 'Inspector',
    unqualifiedValidate: 'Please select data from the same warehouse to perform non-conforming operations',
    revokeSuccess: 'Revoke Successful',
    defective: 'Unqualified Products',
    stashLocation: 'Stash Location',
    emptyValidate: 'Storage area, partition, and location data cannot be empty',
    SN: 'SN Query',
    inventoryRequirementNo: 'Inventory Requirement No.',
    receiptTime: 'Receipt Time',
    inventoryStatus: 'Inventory Status',
    whetherStrictly: 'Whether Strictly Inspection',
    inspectionScope: 'Inspection Scope',
    inspectionChecklist: 'Pending Inspection Checklist',
    inspectionInventoryList: 'Inspection Inventory List',
    qualityInspectionItem: 'Quality Inspection Item',
    scrapNumber: 'Scrap Number',
    trackingNumberSearch: 'Track No. Search',
    inspectionItem: 'Inspection Item',
    resultType: 'Result Type',
    upperLimit: 'Upper Limit',
    lowerLimit: 'Lower Limit',
    standardValue: 'Standard Value',
    fileUpload: 'File Upload',
    qualityEvaluation: 'Quality Evaluation',
    qualified: 'Qualified',
    unqualified: 'UnQualified',
    unqualifiedStorage: 'Storage location of unqualified products',
    viewSN: 'View SN',
    scrapMoreThanNum: 'The number of damages cannot exceed the quantity',
    resultEmpty: 'The inspection result cannot be empty',
    selectUnqualifiedStorage: 'Please select the storage location for unQualified products',
    inspectionNo: 'Inspection No.',
    inspectionPerson: 'Inspection Person',
    confirmRevoke: 'Are you sure to revoke the quality inspection record?',
    inspectionItemConfig: 'Inspection Project Configuration',
    deleteConfigConfirm: 'Do you want to delete this configuration item?',
    inspectionExist: 'The inspection item already exists',
    pleaseAddConfig: 'Please add configuration items'
  },
  // 过程控制
  processControl: {
    inventory: 'Inventory List',
    thaw: 'Thaw',
    controlType: 'Control Type',
    controlStartTime: 'Control Start Time',
    releaseList: 'Release List',
    submissionTime: 'Submission Time',
    unqualifiedNo: 'Unqualified No.',
    unqualifiedList: 'Unqualified List',
    defectNum: 'Defect Quantity',
    defectMode: 'Defect Mode',
    defectRemarks: 'Defect Remark',
    occurrenceProcess: 'Occurrence Process',
    productionProcess: 'Production Process',
    submissionProcess: 'Submission Process',
    reviewResult: 'Review Results',
    processingStatus: 'Processing Status',
    review: 'Review',
    reviewComments: 'Review Comments',
    qualityInfo: 'Quality Information',
    materialLoss: 'Material Loss',
    scrapType: 'Scrap Type',
    batchStatua: 'Batch Production Status',
    whetherDebugging: 'Whether Debugging',
    singleCode: 'Single Piece Code',
    consumedQuantity: 'Consumed Quantity',
    disassemble: 'Disassembly',
    scrap: 'Scrap',
    repair: 'Repair',
    compromise: 'Compromise',
    reviewCommentsValidate: 'Review comments cannot be empty!',
    cannotSubmit: 'The processing status has changed and cannot be submitted'
  },
  // 过程检验
  processInspection: {
    orderStartTime: 'Start Time',
    firstCheckFrequency: 'First Inspection Frequency',
    lastCheckFrequency: 'Final Inspection Frequency',
    spotCheckFrequency: 'Sampling Frequency',
    firstCheck: 'First Inspection',
    lastCheck: 'Final Inspection',
    spotCheck: 'Sampling Inspection',
    spotCheckProduct: 'Sampling inspection of products',
    initDelete: 'The initial first inspection item cannot be deleted',
    confirmDelete: 'Are you sure to delete the sampled product and corresponding inspection data?',
    lastInspectionTime: 'Last inspection time',
    inspectionNo: 'Inspection No.',
    checkItemValidate: 'The inspection item results for each table are mandatory and cannot be left blank!',
    checkSuccess: 'The inspection task operation was successful!',
    samplingRules: 'Sampling Rules',
    productionNum: 'Production Quantity',
    spotNum: 'Sampling Quantity',
    allowUnqualified: 'Allow Unqualified Quantity',
    inspectionItems: 'Inspection Item',
    processStandards: 'Process Standards',
    standardValue: 'Standard Value',
    errorRange: 'Error range',
    checkType: 'Inspection Type',
    checkResult: 'Inspection Result',
    resultType: 'Result Type',
    configured: 'Configured',
    notConfigured: 'Not Configured',
    mateValidate: 'The material number cannot be empty!',
    addInspection: 'Add Inspection Items',
    addSamplingRules: 'Add sampling rules',
    signalUrl: 'Signal URL',
    removefromBack: 'The sampling rules that are currently configured at the back cannot be removed. If you want to remove them, please remove them from the back to the front!',
    spotItem: 'Sampling inspection items',
    input: 'Input'
  },
  //  质量追溯
  qualityTrace: {
    mateconsumption: 'Material Consumption',
    singleBind: 'Single Item Binding',
    processParameters: 'Process Parameters',
    productionRecords: 'Production Records',
    productionTime: 'Production Time',
    usedNum: 'Quantity Used',
    singleBar: 'Single Barcode',
    offLineTime: 'Offline Time',
    forwardTrace: 'Forward Tracing',
    reverseTrace: 'Reverse Tracing',
    conversionRecord: 'Conversion Record',
    batchAttribute: 'Batch Attribute',
    orderInfo: 'Order Information',
    inventoryInfo: 'Inventory Information',
    traceValidate: 'Batch No. and material code need to be entered simultaneously for traceability!',
    batchOld: 'Old Batch No.',
    batchNew: 'New Batch No.',
    mateOld: 'Old Material Code',
    mateNew: 'New Material Code',
    numOld: 'Old Quantity',
    numNew: 'New Quantity',
    documentNo: 'Document No.',
    documentType: 'Document Type',
    productionTime: 'Production Time',
    productGrade: 'Product Grade',
    deliveryTime: 'Delivery Time',
    productionBatch: 'Production Batch',
    productionOrder: 'Production Order',
    productionShift: 'Production Shift',
    expiringTime: 'Expiring Time',
    trackValidate: 'The tracking number cannot be empty!',
    mateInput: 'Material Input',
    mateOutput: 'Material Output',
    inStationTime: 'In-Station Time',
    outStationTime: 'Out-Station Time'
  },
  // oee 模块
  // oee: {
  //   outputStatistics: 'Output Statistics',
  //   unit: 'pcs',
  //   shiftTarget: 'Shift Target',
  //   shifyActual: 'Shift Actual',
  //   hourYield: 'Hour Yield',
  //   ngCount: 'NG Count',
  //   date: 'Date'
  // },
  /**
   * 主数据管理
   */
  // 物料
  // material: {
  //   category: 'Category',
  //   materDesc: 'Material Description',
  //   mateList: 'Material List',
  //   bigCategory: 'Material Major Categories',
  //   smallCategory: 'Material Subclass Categories',
  //   unit: 'Measurement Unit',
  //   specifications: 'Specifications Description',
  //   qualityCheck: 'Quality Inspection',
  //   needQualityCheck: 'Need Quality Inspection',
  //   deadlineWarning: 'Advance Warning Days For Inventory Expiration Date',
  //   batchManage: 'Use Batch Management',
  //   batchName: 'Batch Attribute Name',
  //   batchCode: 'Batch Attribute Code',
  //   inputControl: 'Input Control',
  //   batchRule: 'Batch Rules',
  //   storageRule: 'Storage Rules',
  //   expirationDateManage: 'Use Expiration Date Management',
  //   qualityPeriod: 'Quality Guarantee Period',
  //   qualityDisabledPeriod: 'Prohibited Collection Days',
  //   storageType: 'Material Storage Type'
  // },
  //主数据管理-物料
  material: {
    materialDataList: 'Material data list',
    productClassification: 'Product Classification',
    materialCategories: 'Material Categories',
    materialSubcategory: 'Material Subcategory',
    specifications: 'Specifications',
    IsQualityInspection: 'Whether quality inspection',
    qcScope: 'Quality Inspection Scope',
    materialDescription: 'Material Description',
    InventoryValidityDays: 'Inventory Validity Days',
    batchRules: 'Batch Rules',
    EnableBatchManagement: 'Enable Batch Management',
    BatchAttributeName: 'Batch Attribute Name',
    BatchAttributeCode: 'Batch Attribute Code',
    inputControl: 'Input Control',
    enable: 'Enable',
    warehousingRules: 'Warehousing Rules',
    shelfLifeDays: 'Shelf Life Days',
    shelfLifeProhibitedDays: 'Shelf Life Prohibited Days',
    materialStorageType: 'Material StorageType',
    enableExpirationManagement: 'Enable Expiration Management',
    shelfLifeFill: 'Please fill in the shelf life days',
    shelfLifeProhibited: 'Please fill in the shelf life prohibited days'
  },

  //装箱
  packing: {
    numberPackingSpecifications: 'Number of packing specifications',
    packagingMaterialNumber: 'Packing material code',
    packagingMaterialName: 'Packing material name',
    packingList: 'Packing list',
    deleteMeg: 'Are you sure you want to delete this boxed data?',
    PackingSpecifications: 'Packing specifications',
    codeRepeat: 'Code cannot be duplicated'
  },

  //物料BOM
  materialBom: {
    BOMNumber: 'BOM Number',
    BOMUse: 'BOM Use',
    IsItValid: 'Is Valid',
    WhetherToEnable: 'Whether To Enable',
    Version: 'Version',
    ActivationTime: 'Activation time',
    ExpirationTime: 'Expiration time',
    BOMList: 'BOM list'
  },

  //工艺路径
  processPath: {
    RoutingNumber: 'Routing Number',
    RoutingName: 'Routing Name',
    standardBeat: 'Standard beat',
    graphical: 'New (graphical)',
    ProcessPathList: 'Process path list',
    IsDue: 'Is Due',
    baseInfo: 'Basic information',
    tips: 'Note: Switching the BOM number will clear the loading data',
    isFirstProcessFeeding: 'First Process Feeding',
    BomMeg: 'BOM abnormality, please check BOM configuration!',
    FunctionConfiguration: 'Configuration',
    processName: 'process Name',
    AddProcess: 'Add process',
    IntroduceProcess: 'Introduce process',
    addStation: 'Add station',
    addWorkClothes: 'Add Work Clothes',
    BasicConfiguration: 'Basic configuration',
    WorkstationInformation: 'Workstation information',
    MaterialInput: 'Material input',
    MaterialOutput: 'Material output',
    WorkwearInformation: 'Workwear information',
    Workwearuse: 'Workwear use',
    QualityInspection: 'Quality inspection',
    IsCloseOrder: 'Is close order',
    binConfig: 'Location configuration',
    AddMaterial: 'Add material',
    BatchFill: 'Batch fill',
    BOMUsage: 'BOM usage',
    InvestmentMethod: 'Investment method',
    SourceType: 'Source type',
    SourceLocation: 'Source location',
    ConsumptionLocation: 'Consumption location',
    maximumFeeding: 'Maximum Number of feeds',
    FeedStock: 'Feed stock',
    DeductionRules: 'Deduction rules',
    LoadingStation: 'Loading station',
    consumptionMethod: 'Consumption method',
    SubpartScan: 'Subpart scan',
    MaterialWarehouse: 'Material warehouse',
    autoboxing: 'Whether automatically box',
    WhetherRetrace: 'Whether to retrace',
    labelTemplate: 'Label template',
    WhetherUseContainers: 'Whether Use Containers',
    QualityControl: 'Quality Control',
    StandstillControl: 'Standstill Control',
    outboundLabel: 'Outbound label',
    outboundMode: 'Outbound mode',
    controlDuration: 'Control duration',
    consumptionType: 'Consumption type',
    DesignatedWorkstation: 'Designated workstation',
    fileName: 'File name',
    fileType: 'File type',
    uploadName: 'Uploader',
    uploadTime: 'Upload time',
    isSubmit: 'Whether to submit',
    isPassProcess: 'Pass the station process',
    PleaseSelectRouting: 'Please enter the routing name',
    PleaseSelectMaterial: 'Please select material code',
    PleaseSelectPurpose: 'Please select purpose',
    PleaseSelectBOM: 'Please select BOM number',
    PleaseEnterStandardBeat: 'Please enter standard beat',
    Processlist: 'Process list',
    PleaseSelectLine: 'Please select a production line',
    DragIntoCanvas: '(Drag into canvas)',
    PleaseMatNoData: 'Select material data cannot be empty!',
    processConfig: 'Process config',
    processList: 'Process list',
    AddTooling: 'Add tooling'
  },
  // 工位配置
  stationConfig: {
    factoryModel: 'Factory Model',
    stationConfig: 'Station Configuration',
    stationName: 'Station Name',
    stationCode: 'Station Number',
    stationType: 'Station Type',
    storageConfig: 'Storage Location Configuration',
    warehouse: 'Warehouse',
    onlineWarehouse: 'Online Warehouse',
    offlineWarehouse: 'Offline Warehouse',
    associatedStorage: 'Associated Storage',
    outboundStorage: 'Outbound Storage',
    isolationStorage: 'Isolation Storage',
    scrapStorage: 'Scrap Storage',
    printerSettings: 'Printer Settings',
    printer: 'Printer',
    materialPullConfig: 'Material Pull Configuration',
    packagePull: 'Package Pull',
    toolPull: 'Tool Pull'
  },
  // 缺陷模式
  defectMode: {
    defectMode: 'Defect Mode',
    defectName: 'Defect Name',
    defectCode: 'Defect Number',
    defectModeList: 'Defect Mode List',
    processCode: 'Process Number'
  },
  /**
   * OEE-----------------------------------------------------------
   */
  // oee主数据配置
  oeeMaster: {
    shutdownType: 'Shutdown Type',
    shutdownReason: 'Shutdown Reason',
    addType: 'Add Type',
    stateColor: 'Status Color',
    isPlanned: 'Is It Planned',
    ratedBeat: 'Rated Beat',
    productionObjNum: 'Production Object Number',
    productionObjName: 'Production Object Name',
    productionObjType: 'Production Object Type'
  },
  // oee报表分析
  oeeReport: {
    statisticalAnalysis: 'Statistical Analysis',
    detailList: 'Detail List',
    shiftOee: 'Shift OEE Analysis',
    dailyAverage: 'Daily Average Beat',
    shiftBeat: 'Shift OEE Analysis',
    shiftYield: 'Shift Yield Analysis',
    stopReason: 'Analysis Of Shutdown Reasons',
    stopDetail: 'Detailed Of Shutdown Reasons',
    timeLossType: 'Time Loss Analysis(Shutdown Type)',
    timeLossReason: 'Time Loss Analysis(Shutdown Reason)',
    shutdownDuration: 'Shutdown Duration',
    reasonType: 'Reason Type',
    shutdowmDesc: 'Shutdown Description'
  },
  // oee数据记录
  oeeDataLog: {
    actualBeat: 'Actual Beat',
    outputTime: 'Output Time',
    isQualified: 'Is It Qualified',
    isOnePass: 'Is It Once Passed'
  },
  // oee看板及产量爬坡图
  oee: {
    qualityInfo: 'Quality Information',
    dataMonitoring: 'Data Monitoring',
    tomeLossRecord: 'Time Loss Record',
    tomeLossAnalysis: 'Time Loss Analysis',
    runningTime: 'Running Time',
    shutdownDuration: 'Shutdown Duration',
    about: 'About',
    timeRate: 'Time Operating Rate',
    performanceRate: 'Performance Operating Rate',
    qualityRate: 'Quality Qualification Rate',
    innerCircle: 'Inner Circle',
    outputStatistics: 'Output Statistics',
    unit: 'pcs',
    shiftTarget: 'Shift Target',
    shifyActual: 'Shift Actual',
    hourYield: 'Hour Yield',
    ngCount: 'NG Count',
    date: 'Date',
    durationEdit: 'Period Edit',
    shutdownTotal: 'Total Shutdown Period',
    shutdownDuration: 'Shutdown Period',
    timeTo: 'to',
    addShutdownStatus: 'Add Shutdown Status',
    descReson: 'Please enter the reason for the shutdown',
    notHaveTime: 'Remaining time is not enough',
    notFullyAllocated: 'The downtime has not been fully allocated, please continue!',
    timeExceed: 'The downtime has exceeded, please allocate it reasonably!',
    cannotEmpty: 'The downtime duration, downtime type, and downtime reason cannot be empty!',
    selectDuration: 'Please select the downtime duration',
    overTotalTime: 'Exceeded the total duration',
    timeTmpty: 'Time cannot be empty',
    qualifiedNum: 'Qualified Number',
    onceQualifiedNum: 'Once Qualified Number',
    scrapNum: 'Scrap Number'
  },
  /**
   * iiot中间件-----------------------------------------------------------
   */
  // iiot配置
  iiot: {
    connectName: 'Connection Name',
    connectList: 'OPC UA Connection List',
    connectUrl: 'Connect URL',
    vertify: 'Identity Verification',
    userName: 'User Name',
    connectTest: 'Test Connection',
    connectSuccess: 'Test connection successful',
    connectFail: 'Test connection failed',
    inputConnectName: 'Please input connection name',
    inputConnectUrl: 'Please input connect url',
    inputUserName: 'Please input user name',
    inputPassword: 'Please input password',
    opcua: 'OPC UA',
    params: 'Parameter',
    shutdownRecord: 'Shutdown Record',
    qualifiedNum: 'Qualified Number',
    unqualifiedNum: 'Unqualified Number',
    signalMode: 'Signal Mode',
    switchMode: 'Switch Mode',
    changeMode: 'Change Mode',
    countMode: 'Counting mode',
    shutdownSingal: 'Shutdown Signal',
    shutdownSignalList: 'Shutdown Signal List',
    mode: 'Mode',
    digitalSignal: 'Digital Signal',
    beatSignal: 'Beat Signal',
    qualifiedSignal: 'Qualified Signal',
    unqualifiedSignal: 'Unqualified Signal',
    signalThreshold: 'Signal Maintenance Threshold(s)',
    standardValue: 'Standard Value',
    productNum: 'Product Number',
    outputNum: 'Yield Number',
    designStaion: 'Designated Workstation',
    accordingDevice: 'According to the device signal',
    accordingStation: 'According to workstation',
    opcuaEmpty: 'OPC UA cannot be empty',
    processParamsConfig: 'Processing Parameters Configuration',
    processParamsName: 'Processing Parameter Name',
    setValue: 'Set Value',
    limit: 'Upper/Lower Limit Tag',
    codeValue: 'Barcode Value',
    whetherMainCode: 'Whether Main Code',
    configCodeEmpty: 'Processing parameter configuration or barcode value data cannot be empty!',
    paramsValid: 'The parameters in each row of the newly added processing parameter configuration are required and the quantity cannot be 0!',
    codeValid: 'The parameters for each row in the new barcode configuration are mandatory!',
    modelCode: 'moduleNo',
    objectNo: 'objectNo',
    objectName: 'objectName',
    objectType: 'objectType',
    messageModule: 'Message Module',
    deviceName: 'DeviceName'
  },
  /**
   * 计划管理-----------------------------------------------------------
   */
  // 计划管理
  planManage: {
    // // --------- 计划工单 ---------
    // orderCode: 'Order No.',
    // technologyCode: 'Process Number',
    // technologyName: 'Process Name',
    // customerNum: 'Customer Number',
    planStartTime: 'Planned Start Time',
    planEndTime: 'Planned End Time',
    // planOrderList: 'Planned Order List',
    // versionNum: 'Version Number',
    // orderStatus: 'Order Status',
    // productStatus: 'Production Status',
    // revoke: 'Revoke',
    // release: 'Release',
    // planMaterial: 'Plan Material Preparation',
    // completenessInspect: 'Completeness Inspect',
    // selectOne: 'Please choose one option',
    // selectAtLeastOne: 'Please select at least one option',
    // releaseSuccess: 'Release Successfully',
    // orderType: 'Order Type',
    // packageCode: 'Packaging Number',
    // newProcessPath: 'New Process Path',
    // processPath: 'Process Path',
    // processPathVersion: 'Process Path Version',
    // processPathName: 'Process Path Name',
    // firstProcess: 'Whether The First Process Feeding',
    // bySystem: 'System brought out',
    // selectProcess: 'Select Standard Process',
    // editProcess: 'Edit Standard Process',
    // editBom: 'Edit BOM',
    // afterProcess: 'Bring out after selecting the standard process ',
    // selectTechnology: 'Select Technology',
    // minFeeding: 'Minimum Feeding Quantity',
    // downloadTemplate: 'Download Template',

    // // 计划备料
    planNumber: 'Planned Quantity',
    // bomUse: 'BOM Consumption',
    // prepareNum: 'Quantity of prepared materials',

    // // 齐套性检查
    // rawMaterialWarehouse: 'Raw Material Warehouse',
    // inventoryQuantity: 'Inventory Quantity',
    // whetherMeetRequire: 'Whether Satisfy Production',
    // bomInfo: 'BOM Information',
    // packageInfo: 'Packing Materials Information',
    // packageSpecifications: 'Packing Specifications',

    // // --------- 工单报表 ---------
    orderDetail: 'Order Detail List',
    timeRange: 'Time Frame',
    orderPlanNum: 'Planned Quantity Of Orders',
    actualNum: 'Actual Production Quantity',
    actualStartTime: 'Actual Start Time',
    actualEndTime: 'Actual End Time',
    consumptionList: 'Consumption List',
    unqualifiedNum: 'Unqualified Quantity',
    processProgress: 'Process Progress',
    processCode: 'Process Number',
    processName: 'Process Name',
    outputTime: 'Output Time',
    completeRate: 'Process Completion Rate',
    useQuantity: 'Quantity Consumed',
    notStart: 'Not Start',
    inProgress: 'In Progress',
    completed: 'Completed',
    unknown: 'Unknown',
    mateConsumption: 'Material Consumption',
    planProductionTime: 'Planned Production Time',
    attendanceTime: 'Attendance Time ',
    attendanceNum: 'Attendance Quantity',
    overloadTime: 'Overload Time',
    overloadNum: 'Overload Quantity',
    deviceCapacity: 'Equipment Capacity',
    hours: 'Hours'
  },
  // 分层审核
  layerAudit: {
    scoreList: 'Score List',
    scoringCriteria: 'Scoring Criteria',
    scoreValue: 'Score Value',
    organManagement: 'Organization Management',
    synchronous: 'Synchronous',
    organName: 'Organization Name',
    workShopID: 'Workshop ID',
    workShopName: 'Workshop Name',
    workSectionID: 'Work Section ID',
    workSectionName: 'Work Section Name',
    lineID: 'Line ID',
    lineName: 'Line Name',
    whetherLogisticsArea: 'Whether Logistics Area',
    organizationalLoading: 'Organizational Loading...',
    inputOrganName: 'Please input organization name',
    syncSuccess: 'Synchronization Successful',
    confirmSynchronization: 'Confirm Synchronization',
    updateTime: 'Update Time',
    allUser: 'All User',
    syncUser: 'Synchronize User',
    userdataSynchronizing: 'User Data Synchronizing...',
    userRoles: 'User Roles',
    addRole: 'Add Role',
    roleName: 'Role Name',
    roleLevel: 'Role Level',
    roleStatus: 'Role Status',
    belongingFactory: 'Belonging Factory',
    belongingWorkshop: 'Belonging Workshop',
    belongingWorkSection: 'Belonging Work Section',
    belongingLine: 'Belonging Line',
    whetherInvalid: 'Whether Invalid',
    factoryLevel: 'Factory Level',
    workshopLevel: 'Workshop Level',
    workSectionLevel: 'Work Section Level',
    lineLevel: 'Line Level',
    active: 'Active',
    notActive: 'Not Active',
    belongingShift: 'Belonging Shift',
    shiftStartTime: 'Shift Start Time',
    shiftEndTime: 'Shift End Time',
    roleList: 'Role List',
    shiftList: 'Shift List'
  }
}
