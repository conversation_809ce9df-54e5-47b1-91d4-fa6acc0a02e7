<template>
    <div class="recursive-table-wrapper" :class="{ 'is-loading': loading }">
        <!-- 层级指示器 - 只在第一层级显示，或在用户强制设置showLevelIndicator时显示 -->
        <div class="level-indicator" v-if="level === 0 && showLevelIndicator">
            <el-tag :type="levelColor" effect="plain" size="small">{{ levelText }}</el-tag>
        </div>

        <!-- 数据表格 -->
        <el-table ref="tableRef" :data="data" :border="border" :stripe="stripe" :row-key="rowKey" :height="height"
            :table-layout="tableLayout" v-bind="$attrs" @expand-change="handleExpand" v-loading="loading">

            <!-- 展开列 -->
            <el-table-column type="expand" v-if="hasChildren">
                <template #default="{ row }">
                    <div v-loading="expandLoading[row[rowKey]]">
                        <!-- 递归调用自身，渲染下一级数据 -->
                        <recursive-table v-if="expandCache[row[rowKey]] && expandCache[row[rowKey]].length > 0"
                            :data="expandCache[row[rowKey]]" :columns="columns" :level="level + 1" :max-level="maxLevel"
                            :row-key="rowKey" :border="border" :stripe="stripe" :table-layout="tableLayout"
                            :expand-cache="expandCache" :expand-loading="expandLoading"
                            :operation-buttons="operationButtons" :has-children="hasChildren" :parent-id="row[rowKey]"
                            :load-children-method="loadChildrenMethod" @operation="handleOperation"
                            @data-loaded="handleChildrenLoaded" />
                        <div v-else-if="expandCache[row[rowKey]] && expandCache[row[rowKey]].length === 0"
                            class="empty-children">
                            <el-empty description="暂无数据" :image-size="60" />
                        </div>
                    </div>
                </template>
            </el-table-column>

            <!-- 动态列 -->
            <template v-for="column in columns" :key="column.prop">
                <el-table-column :prop="column.prop" :label="column.label" :width="column.width"
                    :min-width="column.minWidth" :align="column.align || 'center'" :formatter="column.formatter">
                    <template #default="scope" v-if="column.slot">
                        <slot :name="column.slot" :row="scope.row" :$index="scope.$index" />
                    </template>
                    <template #default="scope" v-else-if="column.render">
                        <component :is="column.render.component" v-bind="getComponentProps(column.render.props)"
                            :row="scope.row" @click="column.render.onClick && column.render.onClick(scope.row)">
                            {{ renderText(column.render, scope.row) }}
                        </component>
                    </template>
                </el-table-column>
            </template>

            <!-- 操作列 -->
            <el-table-column v-if="operationButtons && operationButtons.length > 0" label="操作" width="150" fixed="right"
                align="center">
                <template #default="scope">
                    <el-button v-for="button in operationButtons" :key="button.type" :type="button.type"
                        :size="button.size || 'small'" @click="onOperation(button.action, scope.row)">
                        <el-icon v-if="button.icon">
                            <component :is="resolveIconComponent(button.icon)" />
                        </el-icon>
                        <span v-if="button.text">{{ button.text }}</span>
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script>
import { ref, computed, nextTick } from 'vue'
import { ElMessage, ElEmpty } from 'element-plus'
// 导入操作按钮需要的图标组件
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

export default {
    name: 'RecursiveTable',
    // 注册所有ElementPlus图标组件
    components: ElementPlusIconsVue,
    props: {
        // 数据源
        data: {
            type: Array,
            default: () => []
        },
        // 表格列配置
        columns: {
            type: Array,
            default: () => []
        },
        // 当前层级 (从0开始)
        level: {
            type: Number,
            default: 0
        },
        // 最大支持的层级数 (默认10层，可以根据需要调整)
        maxLevel: {
            type: Number,
            default: 10
        },
        // 行数据的唯一标识
        rowKey: {
            type: String,
            default: 'id'
        },
        // 是否显示边框
        border: {
            type: Boolean,
            default: true
        },
        // 是否显示斑马纹
        stripe: {
            type: Boolean,
            default: true
        },
        // 表格高度
        height: {
            type: [String, Number],
            default: undefined
        },
        // 表格布局方式
        tableLayout: {
            type: String,
            default: 'fixed'
        },
        // 操作按钮配置
        operationButtons: {
            type: Array,
            default: () => []
        },
        // 加载子数据的方法
        loadChildrenMethod: {
            type: Function,
            required: true
        },
        // 是否有子节点 (用于控制是否显示展开列)
        hasChildren: {
            type: Boolean,
            default: true
        },
        // 父节点ID
        parentId: {
            type: [String, Number],
            default: null
        },
        // 展开缓存数据 (用于在父组件中共享展开状态)
        expandCache: {
            type: Object,
            default: () => ({})
        },
        // 展开加载状态 (用于在父组件中共享加载状态)
        expandLoading: {
            type: Object,
            default: () => ({})
        },
        // 是否显示层级指示器
        showLevelIndicator: {
            type: Boolean,
            default: true
        }
    },
    emits: ['operation', 'data-loaded', 'expand'],
    setup(props, { emit, expose }) {
        // 表格引用
        const tableRef = ref(null)

        // 加载状态
        const loading = ref(false)

        // 层级颜色映射
        const levelColorMap = [
            'primary',    // 第0级 (蓝色)
            'success',    // 第1级 (绿色)
            'warning',    // 第2级 (黄色)
            'danger',     // 第3级 (红色)
            'info'        // 第4级及以上 (灰色)
        ]

        // 计算当前层级的颜色
        const levelColor = computed(() => {
            // 如果层级超过预定义的颜色数量，则使用最后一个颜色
            return levelColorMap[Math.min(props.level, levelColorMap.length - 1)]
        })

        // 计算当前层级的文本
        const levelText = computed(() => {
            // 根据层级生成层级文本
            const levelLabels = ['一级展开', '二级展开', '三级展开', '四级展开', '五级展开', '六级展开', '七级展开', '八级展开', '九级展开', '十级展开']
            const level = props.level
            return level < levelLabels.length ? levelLabels[level] : `${level + 1}级展开`
        })

        // 处理操作按钮点击
        const onOperation = (action, row) => {
            emit('operation', { action, row, level: props.level })
        }

        // 处理展开/折叠
        const handleExpand = async (row, expanded) => {
            // 如果折叠，则不需要加载数据
            if (!expanded) {
                emit('expand', { row, expanded, level: props.level })
                return
            }

            // 如果已经有缓存数据，则不需要重新加载
            if (props.expandCache[row[props.rowKey]]) {
                emit('expand', { row, expanded, level: props.level })
                return
            }

            try {
                // 标记为加载中
                props.expandLoading[row[props.rowKey]] = true

                // 调用加载子数据的方法
                const children = await props.loadChildrenMethod(row, props.level)

                // 更新缓存
                props.expandCache[row[props.rowKey]] = children || []

                // 触发展开事件
                emit('expand', { row, expanded, level: props.level })

                // 触发数据加载完成事件
                emit('data-loaded', {
                    row,
                    children: props.expandCache[row[props.rowKey]],
                    level: props.level
                })
            } catch (error) {
                console.error('加载子数据失败:', error)
                props.expandCache[row[props.rowKey]] = []
                ElMessage.error('加载子数据失败')
            } finally {
                // 加载完成
                props.expandLoading[row[props.rowKey]] = false
            }
        }

        // 处理子数据加载完成
        const handleChildrenLoaded = (data) => {
            emit('data-loaded', data)
        }

        // 处理子组件操作
        const handleOperation = (data) => {
            emit('operation', data)
        }

        // 处理组件props，针对el-button组件特殊处理text属性
        const getComponentProps = (props) => {
            if (!props) return {}

            // 创建props的副本，避免修改原始对象
            const newProps = { ...props }

            // 特殊处理text属性，如果组件是el-button且text是字符串，则移除它
            if (newProps.text && typeof newProps.text === 'string') {
                delete newProps.text
            }

            return newProps
        }

        // 渲染组件文本内容
        const renderText = (render, row) => {
            if (!render) return '';

            // 处理函数形式的text
            if (render.text && typeof render.text === 'function') {
                return render.text(row);
            }

            // 处理字符串形式的text
            if (render.text) {
                return render.text;
            }

            // 从props中获取text
            if (render.props && render.props.text) {
                return render.props.text;
            }

            // 默认情况下，尝试从行数据中获取属性值
            if (render.prop && row) {
                return row[render.prop];
            }

            return '';
        }

        // 展开指定行
        const expandRow = (row) => {
            if (tableRef.value) {
                tableRef.value.toggleRowExpansion(row, true)
            }
        }

        // 折叠指定行
        const collapseRow = (row) => {
            if (tableRef.value) {
                tableRef.value.toggleRowExpansion(row, false)
            }
        }

        // 折叠所有行
        const collapseAllRows = () => {
            if (tableRef.value && props.data) {
                props.data.forEach(row => {
                    tableRef.value.toggleRowExpansion(row, false)
                })
            }
        }

        // 重新加载数据
        const reloadRow = async (row) => {
            try {
                props.expandLoading[row[props.rowKey]] = true

                // 调用加载子数据的方法
                const children = await props.loadChildrenMethod(row, props.level)

                // 更新缓存
                props.expandCache[row[props.rowKey]] = children || []

                // 触发数据加载完成事件
                emit('data-loaded', {
                    row,
                    children: props.expandCache[row[props.rowKey]],
                    level: props.level
                })

                return true
            } catch (error) {
                console.error('重新加载数据失败:', error)
                return false
            } finally {
                props.expandLoading[row[props.rowKey]] = false
            }
        }

        // 转换图标名称
        const resolveIconComponent = (iconName) => {
            // ElementPlus图标组件名称在注册时可能会被转换
            // 例如：Plus -> Plus, Edit -> Edit, Delete -> Delete
            // 如果图标名称无法直接匹配，可以在这里添加映射逻辑
            
            // 查找已注册的图标组件
            const iconComponents = Object.keys(ElementPlusIconsVue);
            
            // 尝试精确匹配
            if (iconComponents.includes(iconName)) {
                return iconName;
            }
            
            // 尝试不区分大小写匹配
            const lowerCaseIconName = iconName.toLowerCase();
            const matchedIcon = iconComponents.find(name => name.toLowerCase() === lowerCaseIconName);
            if (matchedIcon) {
                return matchedIcon;
            }
            
            // 返回原始名称，作为后备方案
            console.warn(`Icon "${iconName}" not found in Element Plus icons.`);
            return iconName;
        }

        // 对外暴露方法
        expose({
            expandRow,
            collapseRow,
            collapseAllRows,
            reloadRow
        })

        return {
            tableRef,
            loading,
            levelColor,
            levelText,
            handleExpand,
            handleChildrenLoaded,
            handleOperation,
            onOperation,
            getComponentProps,
            renderText,
            resolveIconComponent
        }
    }
}
</script>

<style scoped lang="scss">
.recursive-table-wrapper {
    width: 100%;
    position: relative;

    &.is-loading {
        min-height: 100px;
    }

    /* 层级指示器样式 */
    .level-indicator {
        margin-bottom: 10px;
        display: flex;
        align-items: center;

        .el-tag {
            margin-right: 8px;
            border-radius: 4px;
            font-weight: normal;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 0 10px;
        }
    }

    /* 空子节点提示 */
    .empty-children {
        padding: 20px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f8f8f8;
        border-radius: 4px;
        margin: 10px 0;
    }

    :deep(.el-table) {
        /* 表格样式优化 - 与原页面保持一致 */
        display: flex;
        flex-direction: column;

        /* 表格行层级样式 */
        .el-table__row {
            &:hover {
                background-color: #f5f7fa !important;
            }
        }

        /* 表格单元格内容垂直居中 */
        .el-table__cell {
            .cell {
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        /* 展开内容样式优化 */
        .el-table__expanded-cell {
            padding: 20px !important;

            /* 确保展开内容的容器有足够的空间 */
            &>div {
                width: 100%;
                overflow: visible;
                padding-bottom: 20px;
                /* 增加底部内边距避免内容被遮挡 */
            }
        }

        /* 展开图标样式优化 */
        .el-table__expand-icon {
            transition: transform 0.3s ease;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;

            >.el-icon {
                font-size: 16px !important;
                transform: scale(1.2);
            }

            &.el-table__expand-icon--expanded {
                transform: rotate(90deg);
            }
        }

        /* 优化嵌套表格的样式 */
        .el-table__expanded-cell .recursive-table-wrapper {
            margin-bottom: 20px;
            /* 增加底部边距，确保有足够空间 */
        }
    }
}
</style>