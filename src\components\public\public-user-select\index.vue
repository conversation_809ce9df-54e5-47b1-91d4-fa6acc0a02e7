<template>
  <el-select v-model="data" filterable remote :clearable="clearable" :multiple="props.multiple" reserve-keyword placeholder="请选择" :remote-method="remoteMethod" style="width: 240px">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup>
import { deepClone } from '@/common/utils'
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'
import { isLoaded, getOptionList } from './useOptioins'

const emits = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: [String, Number, Boolean, Object, Array],
  multiple: Boolean,
  clearable: Boolean
})
const data = computed({
  get: () => props.modelValue,
  set: (val) => {
    emits('update:modelValue', val)
  }
})

const options = ref([])
const optionList = getOptionList()
const remoteMethod = (query) => {
  if (query) {
    let timer = 100
    let list = []
    for (let i = 0; i < optionList.value.length; i++) {
      timer--
      if (timer <= 0) break
      if (optionList.value[i].label.includes(query) || optionList.value[i].value.includes(query)) {
        list.push(optionList.value[i])
      }
    }
    options.value = list
  } else {
    options.value = optionList.value.slice(0, 100)
  }
}

let timer = null
function setDefineOption(val) {
  if (timer) clearTimeout(timer)
  if (!val) return
  if (!isLoaded()) {
    timer = setTimeout(() => {
      setDefineOption(val)
    }, 200)
  } else {
    if (props.multiple) {
      if (val) {
        const fd = optionList.value.filter((item) => val.includes(item.value))
        if (fd) {
          options.value = [...deepClone(fd)]
        }
      }
    } else {
      const fd = optionList.value.find((item) => item.value === val)
      if (fd) {
        options.value = [deepClone(fd)]
      }
    }
  }
}

onMounted(() => {
  setDefineOption(data.value)
})

/**
 * 自定义选择器
 * 替换useOptions.js中的请求和数据处理即可
 */
</script>
