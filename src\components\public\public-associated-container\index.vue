<script setup>
import { fetchGetContainerData } from '@/api/modules/container'

defineOptions({
  name: 'PublicAssociatedContainer'
})
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const show = ref(false)

const formRef = ref(null)

const emit = defineEmits(['confirm'])

const form = ref({
  containerNo: ''
})

const rules = {
  containerNo: [{ required: true, message: '请输入容器编号', trigger: 'blur' }]
}

const openDialog = () => {
  show.value = true
}

const closeDialog = () => {
  clearData()
  show.value = false
}

const containerNoLoading = ref(false)
//是否已经校验过
const isPass = ref(false)
const onScan = async (value) => {
  try {
    const params = {
      page: 1,
      size: 9999
    }
    const data = {
      backNo: value
    }
    containerNoLoading.value = true
    const { list = [] } = await fetchGetContainerData(params, data)
    //如果有，并且查看是不是占用状态
    if (Array.isArray(list) && list.length) {
      const current = list[0]
      if (current.ret) {
        ElMessage.warning('该容器已被占用,请重新扫描')
        form.value.containerNo = ''
        isPass.value = false
        return
      }
      ElMessage.success('容器编号校验通过')
      form.value.containerNo = value
      isPass.value = true
    } else {
      ElMessage.warning('未查询到该容器信息')
      form.value.containerNo = ''
      isPass.value = false
    }
  } finally {
    containerNoLoading.value = false
  }
}

const confirmSubmit = async () => {
  await formRef.value.validate()
  if (!isPass.value) {
    ElMessage.warning('请先校验该容器编号')
    return
  }
  const code = form.value.containerNo
  emit('confirm', code)
  show.value = false
  clearData()
}

//重置
const clearData = () => {
  form.value.containerNo = ''
  isPass.value = false
}

defineExpose({
  openDialog
})
</script>

<template>
  <div>
    <el-dialog title="关联容器" v-model="show" width="500px" :close-on-click-modal="false">
      <el-form @submit.native.prevent :model="form" :rules="rules" ref="formRef" label-width="100px" label-position="top">
        <el-form-item label="容器编号" prop="containerNo">
          <base-scan v-model="form.containerNo" @on-scan="onScan" placeholder="请扫描/请输入" autofocus :loading="containerNoLoading" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-center items-center">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="confirmSubmit()">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss"></style>
