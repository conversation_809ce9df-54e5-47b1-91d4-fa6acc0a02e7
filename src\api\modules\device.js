// 设备
import request from '@/common/request.js'

// 1.设备台账
/*
 *  获取设备数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetDeviceList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/mdDevice/pages',
    method: 'POST',
    params: params,
    data: data
  })
}

/*
 *  新增设备数据列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddDeviceList = (data) => {
  return request({
    url: '/tpm_api/v1/mdDevice/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑设备数据列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateDeviceList = (data) => {
  return request({
    url: '/tpm_api/v1/mdDevice/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除设备数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelDeviceList = (ids) => {
  return request({
    url: `/tpm_api/v1/mdDevice/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  通过ID查询设备
 *  @param { id: Number }
 *  @returns { Promise }
 */
export const GetDeviceDetails = (id) => {
  return request({
    url: `/tpm_api/v1/mdDevice/${id}`,
    method: 'GET'
  })
}
/*
 *  获取文件列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetMdDeviceList = (params, data) => {
  return request({
    url: '/tpm_api/v1/mdDeviceAttachment/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增文件
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const AddmdDeviceAttachment = (data) => {
  return request({
    url: '/tpm_api/v1/mdDeviceAttachment/save',
    method: 'POST',
    data: data
  })
}
/*
 *  删除设备点检项
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelMdDeviceAttachment = (ids) => {
  return request({
    url: `/tpm_api/v1/mdDeviceAttachment/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  获取设备点检项列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetCheckItemList = (params, data) => {
  return request({
    url: '/tpm_api/v1/dcCheckItem/pages',
    method: 'POST',
    params: params,
    data: data
  })
}

/*
 *  新增设备点检项
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddDevCheckItem = (data) => {
  return request({
    url: '/tpm_api/v1/dcCheckItem/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑设备点检项
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateDevCheckItem = (data) => {
  return request({
    url: '/tpm_api/v1/dcCheckItem/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除设备点检项
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelDevCheckItem = (ids) => {
  return request({
    url: `/tpm_api/v1/dcCheckItem/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  获取一级保养列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetOlmMaintItemList = (params, data) => {
  return request({
    url: '/tpm_api/v1/olmMaintItem/pages',
    method: 'POST',
    params: params,
    data: data
  })
}

/*
 *  新增一级保养
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddOlmMaintItem = (data) => {
  return request({
    url: '/tpm_api/v1/olmMaintItem/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑一级保养
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateOlmMaintItem = (data) => {
  return request({
    url: '/tpm_api/v1/olmMaintItem/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除一级保养
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelOlmMaintItem = (ids) => {
  return request({
    url: `/tpm_api/v1/olmMaintItem/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  获取二级保养列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetTlmMaintItemList = (params, data) => {
  return request({
    url: '/tpm_api/v1/tlmMaintItem/pages',
    method: 'POST',
    params: params,
    data: data
  })
}

/*
 *  新增二级保养
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddTlmMaintItem = (data) => {
  return request({
    url: '/tpm_api/v1/tlmMaintItem/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑二级保养
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateTlmMaintItem = (data) => {
  return request({
    url: '/tpm_api/v1/tlmMaintItem/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除二级保养
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelTlmMaintItem = (ids) => {
  return request({
    url: `/tpm_api/v1/tlmMaintItem/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @name 查询开班点检（不分页）
 * @type { type } @param data
 * @type { type } @return Promise
 */
export const GetOpenCheckItemList = (data) => {
  return request({
    url: '/tpm_api/v1/openCheckItem/queryOpenCheckItemList',
    method: 'POST',
    data
  })
}

/**
 * @name 新增开班点检
 * @type { type } @param data
 * @type { type } @return Promise
 */
export const AddOpenCheckItem = (data) => {
  return request({
    url: '/tpm_api/v1/openCheckItem/save',
    method: 'POST',
    data
  })
}

/**
 * @name 编辑开班点检
 * @type { type } @param data
 * @type { type } @return Promise
 */
export const UpdateOpenCheckItem = (data) => {
  return request({
    url: '/tpm_api/v1/openCheckItem/update',
    method: 'PUT',
    data
  })
}

/**
 * @name 删除开班点检
 * @type { type } @param data
 * @type { type } @return Promise
 */
export const DelOpenCheckItem = (ids) => {
  return request({
    url: `/tpm_api/v1/openCheckItem/${ids}/delete`,
    method: 'DELETE'
  })
}

// 2.日常点检
/*
 *  获取点检列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetCheckOrderList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/dcCheckOrder/pages',
    method: 'POST',
    params: params,
    data: data
  })
}

/*
 *  获取点检列表
 *  @param { id }
 *  @returns { Promise }
 */
export const GetCheckOrderDetail = (id) => {
  return request({
    url: `/tpm_api/v1/dcCheckOrder/detail/${id}`,
    method: 'GET'
  })
}
// 3.一级保养
/*
 *  获取保养工单列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetOlmManitOrderList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/olmManitOrder/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  删除保养工单列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelOlmManitOrder = (ids) => {
  return request({
    url: `/tpm_api/v1/olmManitOrder/${ids}/delete`,
    method: 'DELETE'
  })
}
/*
 *  获取一级保养详情
 *  @param { id }
 *  @returns { Promise }
 */
export const OlmManitOrderDetailed = (id) => {
  return request({
    url: `/tpm_api/v1/olmManitOrder/detailed/${id}`,
    method: 'GET'
  })
}
/*
 *  一级保养详情工单信息编辑
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const OlmManitOrderUpdate = (data) => {
  return request({
    url: `/tpm_api/v1/olmManitOrder/update`,
    method: 'PUT',
    data
  })
}
/*
 *  一级保养详情保养项目编辑
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const OlmOrderItemUpdate = (data) => {
  return request({
    url: `/tpm_api/v1/olmOrderItem/batchUpdate`,
    method: 'PUT',
    data
  })
}
// 4.二级保养
/*
 *  获取保养工单列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetTlmManitOrderList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/tlmManitOrder/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  删除保养工单列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelTlmManitOrder = (ids) => {
  return request({
    url: `/tpm_api/v1/tlmManitOrder/${ids}/delete`,
    method: 'DELETE'
  })
}
/*
 *  获取二级保养详情
 *  @param { id }
 *  @returns { Promise }
 */
export const TlmManitOrderDetailed = (id) => {
  return request({
    url: `/tpm_api/v1/tlmManitOrder/detailed/${id}`,
    method: 'GET'
  })
}
/*
 *  二级保养详情工单信息编辑
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const TlmManitOrderUpdate = (data) => {
  return request({
    url: `/tpm_api/v1/tlmManitOrder/update`,
    method: 'PUT',
    data
  })
}
/*
 *  二级保养详情保养项目编辑
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const TlmOrderItemUpdate = (data) => {
  return request({
    url: `/tpm_api/v1/tlmOrderItem/batchUpdate`,
    method: 'PUT',
    data
  })
}
// 3.设备维修
/*
 *  获取维修工单列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetFmRepairOrderList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/fmRepairOrder/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  删除维修工单列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelFmRepairOrder = (ids) => {
  return request({
    url: `/tpm_api/v1/fmRepairOrder/${ids}/delete`,
    method: 'DELETE'
  })
}
/*
 *  获取维修工单详情
 *  @param { id }
 *  @returns { Promise }
 */
export const FmRepairOrderDetailed = (id) => {
  return request({
    url: `/tpm_api/v1/fmRepairOrder/${id}`,
    method: 'GET'
  })
}
/*
 *  二级保养详情保养项目编辑
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const FmRepairOrderUpdate = (data) => {
  return request({
    url: `/tpm_api/v1/fmRepairOrder/update`,
    method: 'PUT',
    data
  })
}
/*
 *  上传文件
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const uploadFile = (data) => {
  return request({
    url: '/tpm_api/upload/uploading',
    method: 'POST',
    data
  })
}

/*
 *  上传文件
 *  @param { data: File }
 *  @returns { Promise }
 */
export const uploadFileEasy = (file) => {
  const fd = new FormData()
  fd.append('files', file)
  return request({
    url: '/tpm_api/upload/uploading',
    method: 'POST',
    data: fd
  })
}

/**
 * @name 开班点检记录
 * @type { Object } @param params
 * @type { Object } @param data
 * @type { type } @return Promise
 */
export const GetOpenCheckOrderList = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/openCheckOrder/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/**
 * @name 开班点检记录详情
 * @type { Object } @param data
 * @type { type } @return Promise
 */
export const GetOpenCheckOrderItemList = (data) => {
  return request({
    url: '/tpm_api/v1/OpenOrderItem/queryOpenOrderItemList',
    method: 'POST',
    data
  })
}
