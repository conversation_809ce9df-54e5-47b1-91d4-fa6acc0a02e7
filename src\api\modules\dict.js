import request from '@/common/request.js'

/**
 * 字典分页列表
 * @param {Object} params
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 页大小
 * @param {Object} data - 查询条件
 */
export const GetDictPageList = (params = {}, data = {}) => {
  return request({
    url: '/api/v1/sysDict/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * 字典组列表
 * @param {Void}
 * @returns {Promise}
 */
export const GetDictGroupList = () => {
  return request({
    url: '/api/v1/sysDict/pages',
    method: 'POST',
    params: {
      size: 999999
    },
    data: {
      parentCode: ''
    }
  })
}

/**
 * 新增字典项
 * @param {Object} data - 字典项
 */
export const AddDict = (data = {}) => {
  return request({
    url: '/api/v1/sysDict/save',
    method: 'POST',
    data
  })
}

/**
 * 修改字典项
 * @param {Object} data - 字典项
 */
export const UpdateDict = (data = {}) => {
  return request({
    url: '/api/v1/sysDict/update',
    method: 'PUT',
    data
  })
}

/**
 * 删除字典项 id
 * @param {String} ids - 字典id 逗号分隔
 */
export const DeleteDict = (ids = '') => {
  return request({
    url: `/api/v1/sysDict/${ids}/delete`,
    method: 'DELETE'
  })
}
