import request from '@/common/request.js'

// 4M变化点配置
// 新增
export const ApiFourMChangeConfigAdd = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/add',
    method: 'POST',
    data
  })
}
// 编辑
export const ApiFourMChangeConfigEdit = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/edit',
    method: 'POST',
    data
  })
}
// 删除
export const ApiFourMChangeConfigDel = (params = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/del',
    method: 'DELETE',
    params
  })
}
// 分页
export const ApiFourMChangeConfigPage = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/pages',
    method: 'POST',
    data
  })
}
// 列表
export const ApiFourMChangeConfigList = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/queryList',
    method: 'POST',
    data
  })
}
// 详情
export const ApiFourMChangeConfigDetail = (params = {}) => {
  return request({
    url: '/pb_api/fourMChangeConfig/queryDetail',
    method: 'GET',
    params
  })
}

// 角色配置
// 查询
export const ApiFourMChangeRoleList = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeRole/queryRole',
    method: 'POST',
    data
  })
}
// 编辑
export const ApiFourMChangeRoleEdit = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeRole/edit',
    method: 'POST',
    data
  })
}

// 4M变化点执行配置
// 分页
export const ApiFourMChangeExecutePage = (data = {}) => {
  return request({
    url: '/pb_api/fourMChangeExecute/pages',
    method: 'POST',
    data
  })
}
// 操作记录
export const ApiFourMChangeExecuteLog = (params = {}) => {
    return request({
      url: '/pb_api/fourMConfirmRecord/queryRecord',
      method: 'GET',
      params
    })
  }
  