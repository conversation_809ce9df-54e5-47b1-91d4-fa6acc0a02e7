// 安灯
import request from '@/common/request.js'

// 1. 安灯主数据
/*
 *  获取安灯类型列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetAndonTypeList = (params = {}, data = {}) => {
  return request({
    url: '/andon_api/v1/andonClass/getSpeciesList',
    method: 'GET',
    params: params,
    data: data
  })
}

/* 
    增加安灯类型数据
*/
export const saveAndonData = (data = {}) => {
  return request({
    url: '/andon_api/v1/andonClass/save',
    method: 'POST',
    data: data
  })
}

/* 
    更新安灯类型数据
*/
export const updateAndonData = (data = {}) => {
  return request({
    url: '/andon_api/v1/andonClass/update',
    method: 'PUT',
    data: data
  })
}

/* 
    删除安灯类型数据
*/
export const delAndonData = (ids) => {
  return request({
    url: `/andon_api/v1/andonClass/${ids}/delete`,
    method: 'DELETE'
  })
}

/* 
  查询安灯内容下面的触发条件和规则
*/

export const GetAndonconditionList = (classId) => {
  return request({
    url: `/andon_api/v1/andonRule/getRuleList/${classId}`,
    method: 'GET'
  })
}

/* 
  批量新增安规则
*/

export const saveAndonconditionRuleBatch = (data) => {
  return request({
    url: `/andon_api/v1/andonRule/batchUpdate`,
    method: 'PUT',
    data
  })
}

/* 
    删除安灯类型数据
*/
export const delBatchAndonRulesData = (ids) => {
  return request({
    url: `/andon_api/v1/andonRule/${ids}/delete`,
    method: 'DELETE'
  })
}

/* 

 2. 安灯记录

*/
export const getAondonRecordList = (params = {}, data = {}) => {
  return request({
    url: `/andon_api/v1/andonOrder/pages`,
    method: 'POST',
    params: params,
    data: data
  })
}

/* 查询安灯记录详情 */
export const queryAondonRecordDetails = (id) => {
  return request({
    url: `/andon_api/v1/andonOrder/detail/${id}`,
    method: 'GET'
  })
}
