import { ref } from 'vue'
import { GetRoleOptions as apiRequst } from '@/api/modules/role'
const valueKey = 'value'
const labelKey = 'label'

const optionList = ref([])
let isload = false
let loading = false

function loadOption() {
  if (isload || loading) return
  loading = true
  apiRequst()
    .then((list) => {
      loading = false
      optionList.value = (list || []).map((i) => ({
        value: String(i[valueKey]),
        label: i[labelKey]
      }))
      isload = true
    })
    .catch(() => {
      loading = false
      isload = false
    })
}

export function isLoaded() {
  return isload
}

export function getOptionList() {
  loadOption()
  return optionList
}
