import request from '@/common/request'

//查询调拨单列表
export const GetTransferOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/getTransPage',
    method: 'POST',
    params,
    data
  })
}

//新建调拨单
export const AddTransferOrderItem = (data = {}) => {
  return request({
    // url: '/mes_api/v1/wmTransshipOrder/saveTransshipOrder',
    url: '/mes_api/v1/wmTransshipOrder/saveUpdateTransshipOrder',
    method: 'POST',
    data
  })
}

//查询调拨单详情列表
export const GetTransferOrderDetailList = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/getTransDetail',
    method: 'POST',
    params
  })
}

//提交调拨单
export const SubmitTransferOrder = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/submit',
    method: 'PUT',
    data
  })
}

//删除
export const DeleteTransferOrder = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/deleteByIds',
    method: 'PUT',
    data
  })
}

//同意调拨
export const AgreeTransferOrder = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipOrder/acceptTransfer',
    method: 'POST',
    data
  })
}
//查询调拨单待调入的库存
export const GetToInList = (params = {}) => {
  return request({
    url: '/mes_api/v1/wmTransshipItem/getToInItem',
    method: 'GET',
    params
  })
}
