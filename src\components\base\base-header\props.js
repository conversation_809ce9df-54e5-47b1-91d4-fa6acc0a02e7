// 参数
export default {
  // 是否显示展开收起按钮
  fold: {
    type: <PERSON>olean,
    default() {
      return false
    }
  },
  title: {
    type: String,
    default: ''
  },
  // 表单参数
  model: {
    type: Array,
    default: () => []
  },
  data: {
    type: Object,
    default: () => {}
  },
  autoQuery: {
    type: Boolean,
    default: true
  },
  // 标题宽度
  labelWidth: {
    type: String,
    default() {
      return ''
    }
  },
  // 自动排列 false时为block元素 占满整行
  autosize: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 表单大小
  size: {
    type: String,
    default() {
      return ''
    }
  },
  // 是否显示重置按钮 默认是
  showClear: {
    type: Boolean,
    default() {
      return true
    }
  },
  // 是否显示查询按钮 默认是
  showQuery: {
    type: Boolean,
    default() {
      return true
    }
  },
  // 是否清空列表置空，不查询
  isClear: {
    type: Boolean,
    default() {
      return false
    }
  }
}
