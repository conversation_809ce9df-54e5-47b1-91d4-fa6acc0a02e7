import { computed } from "vue"
import i18n from "@/i18n"
export default computed(() => {
  return {
    // 是否计划性停机
    stopInPlan: [
      { label: i18n.$t('base.yes'), value: '1' },
      { label: i18n.$t('base.no'), value: '0' }
    ],
    // 维度
    dimension: [
      {
        label: '工厂模型',
        value: 'Line'
      },
      {
        label: '生产对象',
        value: 'Device'
      }
    ],
    // 是否合格
    isOk: [
      { label: i18n.$t('base.yes'), value: 1 },
      { label: i18n.$t('base.no'), value: 0 }
    ],
    // 是否一次通过
    isOncePass: [
      { label: i18n.$t('base.yes'), value: 1 },
      { label: i18n.$t('base.no'), value: 0 }
    ]
  }
})
