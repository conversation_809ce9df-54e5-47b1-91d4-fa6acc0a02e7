import request from '@/common/request.js'
//分层审核

/**
 * @description:查询工厂审核层级树
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLyaLevelThree = (data = {}) => {
  return request({
    url: '/audit_api/lyaLevel/queryLyaLevelThree',
    method: 'POST',
    data
  })
}

/**
 * @description:新增审核层级
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLyaLevelItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaLevel/addLyaLevel',
    method: 'POST',
    data
  })
}

/**
 * @description:编辑审核层级
 * @param { Object }: data
 * @return { Promise }
 */
export const EditLyaLevelItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaLevel/updLyaLevel',
    method: 'PUT',
    data
  })
}

/**
 * @description:删除审核层级
 * @param { Object }: data
 * @return { Promise }
 */
export const DelLyaLevel = (data = {}) => {
  return request({
    url: '/audit_api/lyaLevel/delLyaLevel',
    method: 'DELETE',
    data
  })
}

/**
 * @description:查询工厂审核层级角色数据
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLyaLevelRoleData = (params = {}, data = {}) => {
  return request({
    url: '/audit_api/lyaConfig/configs',
    method: 'POSt',
    params,
    data
  })
}

/**
 * @description:新增审核层级角色
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLyaLevelRoleItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaConfig/insert',
    method: 'POST',
    data
  })
}

/**
 * @description:编辑审核层级角色
 * @param { Object }: data
 * @return { Promise }
 */
export const EditLyaLevelRoleItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaConfig/edit',
    method: 'PUT',
    data
  })
}

/**
 * @description:删除审核层级角色
 * @param { Object }: data
 * @return { Promise }
 */
export const DelLyaLevelRole = (id) => {
  return request({
    url: `/audit_api/lyaConfig/${id}`,
    method: 'DELETE'
  })
}

// 获取所有角色

/**
 * @description:获取所有角色
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAllRoles = (factoryId) => {
  return request({
    url: '/audit_api/lyaConfig/config_role',
    method: 'GET',
    showLoading: true,
    params: {
      factoryId
    }
  })
}

//审核周期

/**
 * @description:查询审核周期
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetAllAuditCircle = (params = {}, data = {}) => {
  return request({
    url: '/audit_api/lyaPeroid/all',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:新增审核周期
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const AddAllAuditCircleItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaPeroid/insert',
    method: 'POST',
    data
  })
}

/**
 * @description:编辑审核周期
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const editAllAuditCircleItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaPeroid/edit',
    method: 'PUT',
    data
  })
}

/**
 * @description:删除审核周期
 * @path { Object }: lpId
 * @return { Promise }
 */
export const DelAuthByIds = (lpId) => {
  return request({
    url: `/audit_api/lyaPeroid/${lpId}`,
    method: 'DELETE'
  })
}

/**
 * @description:查询所有工厂
 * @path { Object }: lpId
 * @return { Promise }
 */
export const GetAllFactory = () => {
  return request({
    url: '/audit_api/department/depart_tree',
    method: 'GET',
    params: {
      from: 'manage'
    }
  })
}

/**
 * @description:查询所有工厂层级树
 * @path { Object }: lpId
 * @return { Promise }
 */
export const GetAllFactoryTree = () => {
  return request({
    url: '/audit_api/department/departTree',
    method: 'GET'
  })
}

/**
 * @description:根据工厂获取层级
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLevelDataByFcCode = (factoryId) => {
  return request({
    url: '/audit_api/lyaLevel/getLyaLevels',
    method: 'GET',
    showLoading: true,
    params: {
      factoryId
    }
  })
}

/**
 * @description:根据工厂获取层级
 * @param { Object }: data
 * @return { Promise }
 */
export const GetRoleDataByLevel = (lvlIds) => {
  return request({
    url: '/audit_api/lyaConfig/lya_role',
    method: 'GET',
    params: {
      lvlIds
    }
  })
}

/**
 * @description:根据层级获取审核类型
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLpTypeByLevel = (lpLevel) => {
  return request({
    url: `/audit_api/lyaProblem/lp_type/${lpLevel}`,
    method: 'GET'
  })
}

/**
 * @description:根据类型获取审核标题
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLpTitleByType = (params) => {
  return request({
    url: `/audit_api/lyaProblem/lp_title`,
    method: 'GET',
    params
  })
}

// 查询工厂树形结构
export const GetFactoryCascaderData = (params = {}) => {
  return request({
    url: `/audit_api/department/queryDepartmentTree`,
    method: 'GET',
    params
  })
}

//审核项

/**
 * @description:查询所有层级审核项
 * @param { Object }: params
 * @return { Promise }
 */
export const GetAllLevelAuditData = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaProblem/manage`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:新增审核层级审核项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const AddAuditLevelItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaProblem/addProblem',
    method: 'POST',
    params: {
      stamp: 3
    },
    data
  })
}

/**
 * @description:编辑审核层级审核项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const EditAuditLevelItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaProblem/editPeroid',
    method: 'PUT',
    params: {
      stamp: 3
    },
    data
  })
}

/**
 * @description:删除审核项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const DelAllAuditLevelItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaProblem/is_active',
    method: 'PUT',
    data
  })
}

// 一键必审
/**
 * @description:一键必审
 * @param { Object }: params
 * @return { Promise }
 */
export const OneKeylyaProblemMustudit = (params = {}) => {
  return request({
    url: '/audit_api/lyaProblem/lyaProblemMustudit',
    method: 'PUT',
    params
  })
}

//差异管理
/**
 * @description:查询所有差异管理
 * @param { Object }: params
 * @return { Promise }
 */
export const GetAllAuditDiffData = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaRoll/listLyaRolls`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:新增差异项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const AddAuditDiffItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaProblem/addProblem',
    method: 'POST',
    data
  })
}

/**
 * @description:编辑差异项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const EditAuditDiffItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaRoll/updLyaRoll',
    method: 'PUT',
    data
  })
}

/**
 * @description:删除差异项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const DelAllAuditDiffItem = (data = {}) => {
  return request({
    url: '/audit_api/lyaProblem/is_active',
    method: 'PUT',
    data
  })
}

// 审核工单

/**
 * @description:获取审核工单
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditOrderData = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaOrder/order`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:删除审核项
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const DelAllAuditOrderItem = (loId) => {
  return request({
    url: `/audit_api/lyaOrder/delLyaOrder/${loId}`,
    method: 'DELETE'
  })
}

/**
 * @description:审核项日志记录
 * @return { Promise }
 */

// 查询审核项日志记录
export const GetAuditLogRecords = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaProblemLog`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:审核问题推送项
 * @return { Promise }
 */

// 查询审核问题推送项
export const GetAuditNotifyProblem = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaProblem/manage`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:产线关键点
 * @return { Promise }
 */

// 查询产线关键点
export const GetAuditLyaNotifyAttention = (params = {}, data = {}) => {
  return request({
    url: `/audit_api/lyaNotifyAttention`,
    method: 'POST',
    params,
    data
  })
}

// 新增产线关键点
export const AddAuditLyaNotifyAttentionItem = (data = {}) => {
  return request({
    url: `/audit_api/lyaNotifyAttention/insertLyaNotifyAttention`,
    method: 'POST',
    data
  })
}

// 编辑产线关键点
export const EditAuditLyaNotifyAttention = (data = {}) => {
  return request({
    url: `/audit_api/lyaNotifyAttention/updLyaNotifyAttention`,
    method: 'PUT',
    data
  })
}
// 删除产线关键点
export const DelAuditLyaNotifyAttention = (data = {}) => {
  return request({
    url: `/audit_api/lyaNotifyAttention/delAttention`,
    method: 'PUT',
    data
  })
}

// --------------------------- 分层审核主数据配置 ---------------------------
// 分数
/**
 * @description:分数列表
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditScoreList = (params = {}, data = {}) => {
  return request({
    url: '/audit_api/lyaScoreInfo/pageList',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description:分数新增
 * @param { Object }: data
 * @return { Promise }
 */
export const AddAuditScore = (data) => {
  return request({
    url: '/audit_api/lyaScoreInfo',
    method: 'POST',
    data
  })
}

/**
 * @description:分数编辑
 * @param { Object }: data
 * @return { Promise }
 */
export const EditAuditScore = (Id, data = {}) => {
  return request({
    url: `/audit_api/lyaScoreInfo/${Id}`,
    method: 'PUT',
    data
  })
}

/**
 * @description:分数删除
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteAuditScore = (Id) => {
  return request({
    url: `/audit_api/lyaScoreInfo/${Id}`,
    method: 'DELETE'
  })
}

// 角色
/**
 * @description:角色列表
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditRoleList = (params = {}) => {
  return request({
    url: '/audit_api/role',
    method: 'GET',
    params
  })
}

/**
 * @description:角色新增
 * @param { Object }: data
 * @return { Promise }
 */
export const AddAuditRole = (data) => {
  return request({
    url: '/audit_api/role',
    method: 'POST',
    data
  })
}

/**
 * @description:角色编辑
 * @param { Object }: data
 * @return { Promise }
 */
export const EditAuditRole = (data = {}) => {
  return request({
    url: '/audit_api/role',
    method: 'PUT',
    data
  })
}

/**
 * @description:角色删除
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteAuditRole = (Id) => {
  return request({
    url: `/audit_api/role/deleteRole/${Id}`,
    method: 'DELETE',
  })
}

// 班次
/**
 * @description:班次列表
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditShiftList = (params = {}) => {
  return request({
    url: '/audit_api/shift/all',
    method: 'GET',
    params
  })
}

/**
 * @description:班次新增
 * @param { Object }: data
 * @return { Promise }
 */
export const AddAuditShift = (data) => {
  return request({
    url: '/audit_api/shift',
    method: 'POST',
    data
  })
}

/**
 * @description:班次编辑
 * @param { Object }: data
 * @return { Promise }
 */
export const EditAuditShift = (data = {}) => {
  return request({
    url: '/audit_api/shift',
    method: 'PUT',
    data
  })
}

/**
 * @description:班次删除
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteAuditShift = (Id) => {
  return request({
    url: `/audit_api/shift/${Id}`,
    method: 'DELETE'
  })
}

// 机构管理
/**
 * @description:机构管理树形结构
 * @param { Object }: data
 * @return { Promise }
 */
export const GetOrganTree = () => {
  return request({
    url: '/audit_api/department/departTree',
    method: 'GET'
  })
}

/**
 * @description:机构管理-车间-根据工厂获取车间
 * @param { Object }: data
 * @return { Promise }
 */
export const GetShopByFcId = (Id) => {
  return request({
    url: `/audit_api/workshop?fcId=${Id}`,
    method: 'GET'
  })
}

/**
 * @description:机构管理-工厂-删除工厂
 * @param { Object }:
 * @return { Promise }
 */
export const DeleteFactory = (Id) => {
  return request({
    url: `/audit_api/factory/delteteFactoryById/${Id}`,
    method: 'DELETE'
  })
}

/**
 * @description:机构管理-车间-新增车间
 * @param { Object }:
 * @return { Promise }
 */
export const AddShop = (data) => {
  return request({
    url: '/audit_api/workshop',
    method: 'POST',
    data
  })
}

/**
 * @description:机构管理-车间-编辑车间
 * @param { Object }:
 * @return { Promise }
 */
export const EditShop = (data) => {
  return request({
    url: '/audit_api/workshop',
    method: 'PUT',
    data
  })
}

/**
 * @description:机构管理-车间-删除车间
 * @param { Object }:
 * @return { Promise }
 */
export const DeleteShop = (Id) => {
  return request({
    url: `/audit_api/workshop/${Id}`,
    method: 'DELETE'
  })
}

/**
 * @description:机构管理-根据车间获取工段
 * @param { Object }: data
 * @return { Promise }
 */
export const GetVsmByWorkId = (Id) => {
  return request({
    url: `/audit_api/vsm?workId=${Id}`,
    method: 'GET'
  })
}

/**
 * @description:机构管理-工段-新增工段
 * @param { Object }:
 * @return { Promise }
 */
export const AddVsm = (data) => {
  return request({
    url: '/audit_api/vsm',
    method: 'POST',
    data
  })
}

/**
 * @description:机构管理-工段-编辑工段
 * @param { Object }:
 * @return { Promise }
 */
export const EditVsm = (data) => {
  return request({
    url: '/audit_api/vsm',
    method: 'PUT',
    data
  })
}

/**
 * @description:机构管理-工段-删除工段
 * @param { Object }:
 * @return { Promise }
 */
export const DeleteVsm = (Id) => {
  return request({
    url: `/audit_api/vsm/${Id}`,
    method: 'DELETE'
  })
}

/**
 * @description:机构管理-根据工段获取产线
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLineByWorkId = (Id) => {
  return request({
    url: `/audit_api/line/lineList?vsmId=${Id}`,
    method: 'GET'
  })
}

/**
 * @description:机构管理-产线-新增产线
 * @param { Object }:
 * @return { Promise }
 */
export const AddLine = (data) => {
  return request({
    url: '/audit_api/line/addLine',
    method: 'POST',
    data
  })
}

/**
 * @description:机构管理-产线-编辑产线
 * @param { Object }:
 * @return { Promise }
 */
export const EditLine = (data) => {
  return request({
    url: '/audit_api/line/editLine',
    method: 'PUT',
    data
  })
}

/**
 * @description:机构管理-产线-删除产线
 * @param { Object }:
 * @return { Promise }
 */
export const DeleteLine = (Id) => {
  return request({
    url: `/audit_api/line/${Id}`,
    method: 'DELETE'
  })
}

// 用户管理
/**
 * @description:用户管理-获取分层审核用户列表
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditUserList = (params = {}) => {
  return request({
    url: '/audit_api/user/user_list',
    method: 'GET',
    params
  })
}

/**
 * @description:用户管理-编辑分层审核用户
 * @param { Object }: data
 * @return { Promise }
 */
export const EditAuditUser = (userId, params = {}) => {
  return request({
    url: `/audit_api/user/updateIsAction/${userId}`,
    method: 'PUT',
    params
  })
}

/**
 * @description:用户管理-删除分层审核用户
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteAuditUser = (userAccount) => {
  return request({
    url: `/audit_api/user/${userAccount}`,
    method: 'DELETE'
  })
}

/**
 * @description:用户管理-获取分层审核用户下绑定的角色
 * @param { Object }: data
 * @return { Promise }
 */
export const GetAuditUserRoles = (userAccount) => {
  return request({
    url: `/audit_api/user/user_role/${userAccount}`,
    method: 'GET'
  })
}

/**
 * @description:用户管理-分层审核用户绑定角色
 * @param { Object }: data
 * @return { Promise }
 */
export const AddAuditUserRoles = (data = {}) => {
  return request({
    url: '/audit_api/userRole/addUserRole',
    method: 'POST',
    data
  })
}

/**
 * @description:用户管理-分层审核用户角色编辑
 * @param { Object }: data
 * @return { Promise }
 */
export const EditAuditUserRoles = (Id, params = {}) => {
  return request({
    url: `/audit_api/userRole/updateIsInvalid/${Id}`,
    method: 'PUT',
    params
  })
}

/**
 * @description:用户管理-删除分层审核用户绑定角色
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteAuditUserRoles = (Id) => {
  return request({
    url: `/audit_api/userRole/${Id}`,
    method: 'DELETE'
  })
}

/**
 * @description:机构管理-获取最新组织架构
 * @param { Object }: data
 * @return { Promise }
 */
export const GetNewBaseOrgan = () => {
  return request({
    url: '/audit_api/department/syncDepartmentTree',
    method: 'GET'
  })
}

/**
 * @description:机构管理-同步最新组织架构
 * @param { Object }: data
 * @return { Promise }
 */
export const SyncBaseOrgan = (data = {}) => {
  return request({
    url: '/audit_api/department/saveOrUpdateDepartment',
    method: 'POST',
    data
  })
}

/**
 * @description:用户管理-同步用户
 * @param { Object }: data
 * @return { Promise }
 */
export const SyncBaseUser = () => {
  return request({
    url: '/audit_api/actionPlan/syncUser',
    method: 'POST'
  })
}

//查询审核工单-问题列表
export const GetAuditWorkOrderList = (params = {}) => {
  return request({
    url: '/audit_api/auditWorkOrder/list',
    method: 'GET',
    params
  })
}

//查询审核工单-问题详情
export const GetAuditWorkOrderDetail = (params, data) => {
  return request({
    url: `/audit_api/lyaOrderItem/queryLyaOrderItemList`,
    method: 'post',
    params,
    data
  })
}

//查询审核工单-审核详情
export const GetAuditWorkOrderAuditDetail = (loId) => {
  return request({
    url: `/audit_api/lyaOrder/orderItem/${loId}`,
    method: 'get'
  })
}
