<template>
  <div class="base-bpmn">
    <div class="base-bpmn-main" v-loading="loading">
      <slot :status="status" :editabled="editabled" :formDisabled="formDisabled" :bpmnId="bpmnId"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue'
import { ref, provide, readonly } from 'vue'
import { useUserStore } from '@/stores/user'
import { ApiGetActiviti } from '@/api/modules/bpmn'
/**
 * pageCode: 页面唯一标识 每个页面中的bpmn组件需要保证key值唯一
 * docCode: 单据id 该页面的单据id/表单数据的id
 * enable: 是否启用 是否启用bpmn组件, 一般根据权限或该单据的状态来控制是否启用
 * 启用时:
 *  根据当前流程状态控制编辑操作
 *  显示流程操作按钮
 * 未启时:
 *  不显示流程操作按钮
 */
const props = defineProps({
  pageCode: {
    type: String,
    required: true
  },
  docCode: {
    type: [String, Number],
    required: true
  },
  enable: {
    type: Boolean,
    default: true
  }
})
const userData = useUserStore()
// userData.userId

const loading = ref(false)
const bpmnId = ref('')
const status = ref('unknown')
const isClaim = ref(undefined)
const processList = ref([])
const processInstanceId = ref('')
const taskId = ref('')
const editabled = ref(false)
const formDisabled = computed(() => {
  return props.enable ? !editabled.value : false
})

const bpmn = computed(() => {
  return {
    pageCode: props.pageCode,
    docCode: props.docCode,
    enable: props.enable,
    status: status.value,
    isClaim: isClaim.value,
    processList: processList.value,
    processInstanceId: processInstanceId.value,
    taskId: taskId.value,
    editabled: editabled.value,
    formDisabled: formDisabled.value,
    setStatus: (value) => {
      status.value = value
    },
    reload: () => {
      load()
    }
  }
})

async function load() {
  try {
    loading.value = true
    if (!props.enable || !props.pageCode || !props.docCode || !userData.userId) {
      return
    }
    const instanceData = await ApiGetActiviti({
      docCode: props.docCode,
      pageCode: props.pageCode,
      userNumber: userData.username
    })
    if (!instanceData || !instanceData.ppType) {
      return
    }
    status.value = instanceData.ppType.code || 'unknown'
    isClaim.value = instanceData.isClaim
    processInstanceId.value = instanceData.processInstanceId
    taskId.value = instanceData.taskId
    processList.value = (instanceData.processList || []).filter((i) => i)
    editabled.value = instanceData.isEdit
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}

watch(
  () => props,
  (value) => {
    if (value) {
      load()
    }
  },
  {
    deep: true,
    immediate: true
  }
)
provide('bpmn', readonly(bpmn))
</script>
<style scoped>
.base-bpmn {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.base-bpmn-main {
  flex: 1;
}
</style>
