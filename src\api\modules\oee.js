import request from '@/common/request.js'
// OEE
// 1.停机类型
/*
 *  获取停机类型列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetOeeStopClass = (params = {}, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopClass/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增停机类型列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveOeeStopClass = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopClass/save',
    method: 'POST',
    data: data
  })
}
/*
 *  编辑停机类型列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateOeeStopClass = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopClass/update',
    method: 'PUT',
    data: data
  })
}
/*
 *  删除停机类型列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DeleteOeeStopClass = (ids) => {
  return request({
    url: `/oee_api/v1/oeeStopClass/${ids}/delete`,
    method: 'DELETE'
  })
}

// 2.停机类型原因
/*
 *  获取停机类型原因列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetOeeStopReason = (params = {}, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopReason/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增停机类型原因列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveOeeStopReason = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopReason/save',
    method: 'POST',
    data: data
  })
}
/*
 *  编辑停机类型原因列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateOeeStopReason = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopReason/update',
    method: 'PUT',
    data: data
  })
}
/*
 *  删除停机类型原因列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DeleteOeeStopReason = (ids) => {
  return request({
    url: `/oee_api/v1/oeeStopReason/${ids}/delete`,
    method: 'DELETE'
  })
}
// 3.额定节拍
/*
 *  获取节拍列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetOeeProCt = (params = {}, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProCt/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增节拍列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveOeeProCt = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProCt/save',
    method: 'POST',
    data: data
  })
}
/*
 *  编辑节拍列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateOeeProCt = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProCt/update',
    method: 'PUT',
    data: data
  })
}
/*
 *  删除节拍列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DeleteOeeProCt = (ids) => {
  return request({
    url: `/oee_api/v1/oeeProCt/${ids}/delete`,
    method: 'DELETE'
  })
}
// 4.班次时间
/*
 *  获取班次时间列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetSysShift = (params = {}, data = {}) => {
  return request({
    url: '/api/v1/sysShift/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增班次时间列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveSysShift = (data = {}) => {
  return request({
    url: '/api/v1/sysShift/save',
    method: 'POST',
    data: data
  })
}
/*
 *  编辑班次时间列表
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateSysShift = (data = {}) => {
  return request({
    url: '/api/v1/sysShift/update',
    method: 'PUT',
    data: data
  })
}
/*
 *  删除班次时间列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DeletSysShift = (ids) => {
  return request({
    url: `/api/v1/sysShift/${ids}/delete`,
    method: 'DELETE'
  })
}
/*
 *  根据实际获取班次
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const getShiftByTime = (params) => {
  return request({
    url: '/api/v1/sysShift/getShiftByTime',
    method: 'GET',
    params
  })
}
/*
 *  OEE停机时间损失记录
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetOeeStopAnalyse = (params = {}) => {
  return request({
    url: '/oee_api/v1/countBoard/getStopAnalyse',
    method: 'POST',
    params: params
  })
}

/*
 *  OEE质量信息
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetOeeStopAva = (params = {}) => {
  return request({
    url: '/oee_api/v1/countBoard/getOeeAva',
    method: 'POST',
    params: params
  })
}

/*
 *  OEE数据监控
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetOeeAnalyse = (params = {}) => {
  return request({
    url: '/oee_api/v1/countBoard/getOeeAnalyseByShift',
    method: 'POST',
    params: params
  })
}

/*
 *  OEE时间记录切割
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const stopTimeSplit = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopLog/stopTimeSplit',
    method: 'POST',
    data
  })
}

/*
 *  获取停机类型列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetHaltType = (params = {}, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopClass/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  获取停机原因列表
 *  @param { params, data: Object } classId
 *  @returns { Promise }
 */
export const GetHaltReason = (params = {}, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopReason/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 * 设备连接
 * 产险参数查询
 * @param { params: Object }
 * @returns { Promise }
 */
export const GetParamByLineCode = (params) => {
  return request({
    // url: '/oee_api/v1/oeeDeviceConn/getParamByLineCode',
    url: '/iiot_api/v1/iiotDeviceConn/getParamByCellCode',
    method: 'GET',
    params
  })
}

/*
 *  设备连接
 *  新增参数
 *  @data { data: Object }
 *  @returns { Promise }
 */
export const AddParamByLineCode = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotDeviceConn/save',
    method: 'POST',
    data
  })
}

/*
 *  产量爬坡图
 *  新增参数
 *  @data { data: Object }
 *  @returns { Promise }
 */
export const GetOutputData = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeOutputStat/getOutputList',
    method: 'POST',
    data
  })
}
