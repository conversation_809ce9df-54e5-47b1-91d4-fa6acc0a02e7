<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import router from '@/router'
import MenuWarp from './menu-warp.vue'
export default {
  components: { MenuWarp },

  setup() {
    const routeData = useRoute()
    const route = computed(() => {
      console.log(routeData.matched[0], 777)
      return routeData.matched[0]
    })
    const goHome = () => {
      router.push('/')
    }
    return {
      route,
      goHome
    }
  }
}
</script>
<template>
  <div class="silder">
    <div class="silder-logo" @click="goHome()">
      <div class="logo-text">
        <span class="text-primary">智能</span>
        <span class="text-secondary">制造</span>
      </div>
    </div>
    <div class="silder-title">{{ $t(route.meta.title) }}</div>
    <div class="silder-sub-title">{{ route.meta.subtitle }}</div>
    <div class="silder-menu"><MenuWarp :list="route.children" /></div>
  </div>
</template>
<style scoped lang="scss">
.silder {
  height: calc(100% - $base-margin * 2);
  @include box;
  @include flex-column;
}
.silder-logo {
  width: 85%;
  margin: 5px auto 25px auto;
  cursor: pointer;
  text-align: center;
}

.logo-text {
  font-size: 28px;
  font-weight: bold;
  letter-spacing: 2px;
  padding: 10px 0;
  
  .text-primary {
    color: #409EFF;
    margin-right: 2px;
  }
  
  .text-secondary {
    color: #303133;
  }
}

.silder-title {
  color: #333;
  font-size: 16px;
  line-height: 19px;
  font-weight: bold;
  margin-bottom: 5px;
  flex-shrink: 0;
}
.silder-sub-title {
  flex-shrink: 0;
  font-size: 12px;
  line-height: 19px;
  font-weight: bold;
  margin-bottom: 25px;
  //color: #d3d2d2;
  color: #e4e4e4;
}
.silder-menu {
  flex: 1;
  height: 0;
  width: 100%;
  overflow: hidden;
}
</style>
