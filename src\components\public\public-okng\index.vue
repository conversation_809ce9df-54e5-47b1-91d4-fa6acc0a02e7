<template>
  <strong v-if="data === true" style="color: lightgreen">OK</strong>
  <strong v-else-if="data === false" style="color: red">NG</strong>
  <strong v-else></strong>
</template>
<script setup>
import { ref, computed, defineProps, defineEmits, onMounted } from 'vue'

const emits = defineEmits(['update:modelValue'])
const props = defineProps({ modelValue: String })
const data = computed({
  get: () => props.modelValue,
  set: (val) => {
    emits('update:modelValue', val)
  }
})
</script>
