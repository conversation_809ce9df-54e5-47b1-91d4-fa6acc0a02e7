//
// export default function (app) {
//   // 自动注册全局组件
//   const modules = import.meta.glob('./public/*.vue')
//   for (const path in modules) {
//     modules[path]().then((mod) => {
//       let pathName = path.split('/')
//       pathName = pathName[pathName.length - 1].split('.')[0]
//       app.component(pathName, mod.default)
//     })
//   }
// }

// SvgIcon Svg图标组件
import SvgIcon from './base/svg-icon/index.vue'
import BasePage from './base/base-page/index.vue'
import BasePageFlex from './base/base-page-flex/index.vue'
import BaseTable from './base/base-table/index.vue'
import BaseHeader from './base/base-header/index.vue'
import BaseWarp from './base/base-warp/index.vue'
import BaseLabel from './base/base-label/index.vue'
import BaseFile from './base/base-file/index.vue'
import BaseRadioGroup from './base/base-radio-group/index.vue'
import BaseBpmn from './base/base-bpmn/index.vue'
import BaseBpmnControler from './base/base-bpmn-controler/index.vue'
import BaseEditabled from './base/base-editabled/index.vue'
import ElSelectDict from './base/el-select-dict/index.vue'
import BaseScan from './base/base-scan/index.vue'
import BaseSelectRemote from './base/base-select-remote/index.vue'
import BaseSelectPageRemote from './base/base-select-page-remote/index.vue'
import PublicPrinterButton from './public/public-printer-button/index.vue'
import PublicPrinterDialog from './public/public-printer-dialog/index.vue'
import PublicGlobalSearch from './public/public-global-search/index.vue'
import PublicPictureUpload from './public/public-picture-upload/index.vue'
import PublicFileUpload from './public/public-file-upload/index.vue'
import PublicUserSelect from './public/public-user-select/index.vue'
import PublicRoleSelect from './public/public-role-select/index.vue'
import PublicOkng from './public/public-okng/index.vue'
import PublicJsonView from './public/public-json-view/index.vue'
import RecursiveTable from './common/RecursiveTable/index.vue'

export default function (app) {
  app.component('SvgIcon', SvgIcon)
  app.component('BasePage', BasePage)
  app.component('BasePageFlex', BasePageFlex)
  app.component('BaseTable', BaseTable)
  app.component('BaseHeader', BaseHeader)
  app.component('BaseWarp', BaseWarp)
  app.component('BaseLabel', BaseLabel)
  app.component('BaseFile', BaseFile)
  app.component('BaseRadioGroup', BaseRadioGroup)
  app.component('BaseBpmn', BaseBpmn)
  app.component('BaseBpmnControler', BaseBpmnControler)
  app.component('BaseEditabled', BaseEditabled)
  app.component('ElSelectDict', ElSelectDict)
  app.component('BaseScan', BaseScan)
  app.component('BaseSelectRemote', BaseSelectRemote)
  app.component('BaseSelectPageRemote', BaseSelectPageRemote)
  app.component('PublicPrinterButton', PublicPrinterButton)
  app.component('PublicPrinterDialog', PublicPrinterDialog)
  app.component('PublicGlobalSearch', PublicGlobalSearch)
  app.component('PublicPictureUpload', PublicPictureUpload)
  app.component('PublicFileUpload', PublicFileUpload)
  app.component('PublicUserSelect', PublicUserSelect)
  app.component('PublicRoleSelect', PublicRoleSelect)
  app.component('PublicOkng', PublicOkng)
  app.component('PublicJsonView', PublicJsonView)
  app.component('RecursiveTable', RecursiveTable)
}
