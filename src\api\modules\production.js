import request from '@/common/request.js'


export function productionPlanMaster(data) {
    return request({
        url: '/mes_api/v1/productionPlanMaster/pages',
        method: 'POST',
        data
    })
}



export function saveProductionPlan(data) {
    return request({
        url: '/mes_api/v1/productionPlanMaster/save',
        method: 'post',
        data
    })
}

export function updateProductionPlan(data) {
    return request({
        url: '/mes_api/v1/productionPlanMaster/update',
        method: 'put',
        data
    })
}

export function deleteProductionPlan(data) {
    return request({
        url: '/mes_api/v1/productionPlanMaster/delete',
        method: 'delete',
        disassemble: false,
        data
    })
}
