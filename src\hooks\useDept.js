import { ref } from 'vue'
import { GetOrganTree } from '@/api/modules/organ'

const deptList = ref([])
const deptTree = ref([])

// 获取部门列表
const queryDeptList = async () => {
  const list = []
  const resp = await GetOrganTree()
  function dfor(l) {
    list.push(...l)
    l.forEach((i) => {
      if (Array.isArray(i.children) && i.children.length > 0) {
        dfor(i.children)
      }
    })
  }
  dfor(resp)
  deptList.value = list.map((i) => {
    return {
      ...i,
      children: undefined
    }
  })
}

/**
 * 获取部门名称
 * @param {string} id 部门id
 * @returns {string} 部门名称
 */
const getDeptNameById = (id) => {
  const fd = deptList.value.find((i) => i.value === id)
  if (fd) {
    return fd.label
  }
  return id
}

/**
 * 获取部门名称
 * @param {string} code 部门编码
 * @returns {string} 部门名称
 */
const getDeptNameByCode = (code) => {
  return code
}

queryDeptList()

export function useDept() {
  return {
    deptList,
    deptTree,
    getDeptNameByCode,
    getDeptNameById
  }
}
