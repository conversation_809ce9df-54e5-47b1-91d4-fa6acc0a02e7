import { ElMessageBox } from 'element-plus'
import { Base64 } from 'js-base64'
import moment from 'moment'
import i18n from '@/i18n'

/*
 *   深拷贝
 *   @params {Object|Array}
 *   @return {Object|Array}
 */
export function deepClone(obj) {
  const result = Array.isArray(obj) ? [] : {}
  for (const key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        result[key] = deepClone(obj[key]) //递归复制
      } else {
        result[key] = obj[key]
      }
    }
  }
  return result
}

/*
 *   确认框
 *   @params {String,String}
 *   @return {Boolean}
 *   异步确认操作框
 *   const booleanResult = await asynConfirm('是否确认删除?')
 *   booleanResult:  true: 确认  false: 取消
 */
export function asynConfirm(text = i18n.$t('message.confirm'), type = 'Warning') {
  return new Promise((resolve) => {
    ElMessageBox.confirm(text, i18n.$t('base.tips'), {
      confirmButtonText: i18n.$t('base.confirm'),
      cancelButtonText: i18n.$t('base.cancel'),
      type: type.toLowerCase()
    })
      .then(() => {
        resolve(true)
      })
      .catch(() => {
        resolve(false)
      })
  })
}

/*
 *   数据深度
 *   @params {Object}
 *   @return {Number}
 */

export const getDataDepth = (obj) => {
  let max = 0
  function traverse(node, depth) {
    if (typeof node === 'object') {
      for (let key in node) {
        let val = node[key]
        if (typeof val === 'object') {
          traverse(val, depth + 1)
        } else {
          max = Math.max(max, depth)
        }
      }
    }
  }
  traverse(obj, 0)
  return max
}

/*
 *   判断对象是否为空
 *   @params {Object}
 *   @return {Boolean}
 */
export const ifNullObject = (obj) => {
  return Object.keys(obj).length != 0
}

/*
 *   发送请求并处理数据
 *   @params {Function, Object}
 *   @return {Array}
 */
export const sendRequest = (request, data, body = {}) => {
  if (typeof request === 'function') {
    return new Promise((resolve) => {
      let array = []
      request({ page: 1, size: 999999 }, body)
        .then((res) => {
          if (res) {
            array = res.list.map((item) => {
              return {
                ...item,
                label: item[data.label],
                value: item[data.value]
              }
            })
            resolve(array || [])
          }
        })
        .catch(() => {
          resolve([])
        })
    })
  } else {
    return []
  }
}
/*
 *   发送字典表请求并处理数据
 *   @params {Function, Object}
 *   @return {Array}
 */
export const sendDictRequest = (request, params) => {
  if (typeof request === 'function') {
    return new Promise((resolve) => {
      let array = []
      request(params, {})
        .then((res) => {
          if (res) {
            array = res.map((item) => {
              return {
                ...item,
                label: item['dictName'],
                value: item['dictValue']
              }
            })
            resolve(array || [])
          }
        })
        .catch(() => {
          resolve([])
        })
    })
  } else {
    return []
  }
}
/*
 *   公共转成下拉选择数据格式方法
 *   @params {String}
 *   @return {String}
 */
export const getSpecificFormat = (arr, name, code) => {
  let list = arr.map((item) => {
    return {
      label: item[name],
      value: item[code],
      ...item
    }
  })
  return list
}

/*
 *  清空对象
 */
export const clearObject = (obj, asObj) => {
  for (let key in obj) {
    if (asObj && typeof asObj[key] != 'undefined') {
      obj[key] = asObj[key]
    } else if (typeof obj[key] == 'string') {
      obj[key] = ''
    } else if (typeof obj[key] == 'boolean') {
      obj[key] = false
    } else if (typeof obj[key] == 'number') {
      obj[key] = 0
    } else if (Array.isArray(obj[key])) {
      obj[key] = []
    }
  }
}
/*
 *   处理日期
 *   @params {String}
 *   @return {String}
 */
export const handleDate = (date, format = 'YYYY-MM-DD') => {
  return date ? moment(date).format(format) : ''
}

/* 自动排序(带分页) */
export const indexMethod = (index, page, pageSize) => {
  const i = index + (page - 1) * pageSize + 1
  return i < 10 ? '0' + i : i
}

/* 自动排序(不带分页) */
export const indexMethod_nPage = (index) => {
  let i = index + 1
  return i < 10 ? '0' + i : i
}

export const handleDictFormatter = (list = [], value = '') => {
  let result = list?.find((item) => item.value === value)
  return result ? result.label : value
}

/**
 * 获取元素距离0,0偏移量
 * @param {Element} el
 */
export const getNodePos = (el) => {
  let pos = {
    x: 0,
    y: 0
  }
  while (el) {
    pos.x += el.offsetLeft
    pos.y += el.offsetTop
    el = el.offsetParent
  }
  return pos
}

/**
 * randomData 随时数据
 * @param {*}
 * @example: randomData(1, 10) 生成1-10的随机数
 * @example: randomData([1, 2, 3]) 从数组中随机取一个
 */
export const randomData = (arg1, arg2) => {
  if (typeof arg1 === 'number' && typeof arg2 === 'number') {
    return Math.round(Math.random() * (arg2 - arg1) + arg1)
  } else if (Array.isArray(arg1)) {
    return arg1[Math.round(Math.random() * (arg1.length - 1))]
  }
}

/**
 * 生成UUID
 * @returns {String} UUID
 */
export const uuid = () => {
  let datetime = String(Number(new Date()))
  datetime = datetime.substring(5, datetime.length - 1)
  return `${datetime}xxxxxxx`.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 10) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * sleep
 * @param {Number} time 毫秒
 * @returns {Promise}
 */
export const sleep = (s) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, s)
  })
}

/**
 * @name: 递归过滤节点, 生成新的树结构
 * @param { Node[] } nodes 要过滤的节点
 * @param {node => boolean} predicate 过滤条件,符合条件的节点保留
 * @example: filterCreateTree([], (item) => item.type !== 3)
 * @return 过滤后的节点
 */
export const filterCreateTree = (nodes, predicate) => {
  // 如果已经没有节点了，结束递归
  if (!(nodes && nodes.length)) {
    return []
  }
  const newChildren = []
  for (const node of nodes) {
    if (predicate(node)) {
      // 如果节点符合条件，直接加入新的节点集
      newChildren.push(node)
      node.children = filterCreateTree(node.children, predicate)
    } else {
      // 如果当前节点不符合条件，递归过滤子节点，
      // 把符合条件的子节点提升上来，并入新节点集
      newChildren.push(...filterCreateTree(node.children, predicate))
    }
  }
  return newChildren
}

/**
 * @name: 检验对象的value是否为空
 * @param object
 * @return Boolean
 */
export const objectValueAllEmpty = (object) => {
  console.log(object, 77788)
  var isEmpty = true
  Object.keys(object).forEach(function (x) {
    if (object[x] === null || object[x] === '' || object[x] === 0 || object[x] === undefined) {
      isEmpty = false
    }
  })
  if (isEmpty) {
    //全有值
    return true
  }
  return false
}
// 分页函数
export const paginate = (array, page_size, page_number) => {
  --page_number // 因为数组索引从0开始
  return array.slice(page_number * page_size, (page_number + 1) * page_size)
}

// 对树形数据做处理
export const filterTree = (nodeList) => {
  return nodeList.map((item) => {
    item.value = item.staCode
    item.label = item.descrip
    if (item.children) {
      item.children = item.children.map((item2) => {
        item2['value'] = item2.sctCode
        item2['label'] = item2.descrip
        if (item2.children) {
          item2.children = item2.children.map((item3) => {
            item3['value'] = item3.binCode
            item3['label'] = item3.binCode
            return item3
          })
        } else {
          item2.children = []
        }
        return item2
      })
    } else {
      item.children = []
    }
    return item
  })
}

export const dataBase64 = {
  // 加密
  encode: function (data) {
    return Base64.encode(JSON.stringify(data))
  },
  // 解密
  decode: function (data) {
    return JSON.parse(Base64.decode(data))
  }
}

//全局去重方法
export const duplicatOriginByKeyWord = (list, keyWord) => {
  if (!list.length) return []
  const map = new Map()
  return list.filter((item) => {
    const key = item[keyWord]
    const hasKey = map.has(key)
    map.set(key, true)
    return !hasKey
  })
}
