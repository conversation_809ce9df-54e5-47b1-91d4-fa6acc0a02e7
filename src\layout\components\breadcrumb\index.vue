<template>
  <div class="breadcrumb">
    <div v-for="item in breadcrumbData" :key="item.path" :class="item.class" class="breadcrumb-item">
      <span v-if="item.meta.title">
        {{ $t(item.meta.title) }}
      </span>
      <span v-else>
        {{ item.name }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      breadcrumbData: []
    }
  },
  mounted() {},
  watch: {
    $route: {
      handler: 'setBreadcrumb',
      immediate: true
    }
  },
  methods: {
    setBreadcrumb() {
      this.breadcrumbData = this.$route.matched
        .filter((r) => r.path)
        .map((r) => {
          if (r.path === this.$route.path) {
            r['class'] = 'active'
          }
          return r
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb {
  @include box;
  margin: 5px 0;
  height: 20px;
  padding-top: 0;
  padding-bottom: 0;
  background-color: transparent;
  box-shadow: none;
  display: flex;
  align-items: center;
}

.breadcrumb-item {
  font-size: 14px;
  color: #666;

  &.active {
    color: $primary;
    font-weight: 600;
  }

  &::after {
    content: '/';
    margin-left: 5px;
    margin-right: 5px;
    display: inline-block;
    transform: scale(0.8);
  }

  &:last-child::after {
    content: '';
    display: none;
  }
}
</style>
