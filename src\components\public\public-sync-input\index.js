import { app } from '@/main.js'
import { createVNode, render } from 'vue'
import VueTemplate from './index.vue'

export default function ({ fields, title }) {
  if (!fields) throw new Error('fields is required')
  if (!Array.isArray(fields)) throw new Error('fields must be an array')

  return new Promise((resolve, reject) => {
    const node = document.createElement('div')
    document.getElementById('app').appendChild(node)

    const instance = createVNode(VueTemplate, {
      title: title,
      fields: fields,
      submit: function (data) {
        instance.component.proxy.$el.remove()
        node.remove()
        resolve(data)
      },
      cancel: function () {
        instance.component.proxy.$el.remove()
        node.remove()
        reject()
      }
    })

    instance.appContext = app._context
    render(instance, node)
  })
}
