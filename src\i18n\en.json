{"json": {"base": {"processName": "OpName", "processCode": "OpCode", "processType": "OpType", "operation": "Operation", "StationNumber": "Station number", "stationName": "Station name", "toolingNumber": "Material Number", "toolingName": "Material name"}}, "nei-bu-sheng-cheng": "Internally generated", "sheng-cheng-xin-biao-qian": "Generate new tags", "wai-bu-sheng-cheng": "External generation", "yan-yong-jiu-biao-qian": "Using old labels", "ben-xi-tong-sheng-cheng": "This system generates", "qing-xuan-ze": "Please choose", "qing-xuan-ze-gong-wei": "Please select workstation", "qing-xuan-ze-ku-wei-lei-xing": "Please select the type of storage location", "gong-wei-bian-hao": "Workstation number", "gong-wei-lei-xing": "Workstation type", "gong-wei-ming-cheng": "Workstation name", "gong-wei-pei-zhi": "Workstation configuration", "bao-cai-la-dong": "Packaging pull", "cang-ku": "Warehouse", "da-yin-ji": "printer", "da-yin-ji-she-zhi": "Printer settings", "gong-zhuang-la-dong": "Tool pulling", "guan-lian-ku-wei": "Associated storage location", "ku-wei-pei-zhi": "Warehouse location configuration", "qu-xiao": "cancel", "que-ren": "confirm", "wu-liao-la-dong-pei-zhi": "Material pull configuration", "zi-dong-dai-chu": "Automatically brought out", "gong-chang-mo-xing": "Factory model", "chan-xian": "Production line", "che-jian": "workshop", "gong-chang": "factory", "gong-duan": "workshop section", "gong-wei": "Workstation", "gai-jian-yan-xiang-yi-cun-zai": "The inspection item already exists", "que-ren-shan-chu-gai-shu-ju-ma": "Are you sure to delete this data?", "shan-chu-cheng-gong": "Delete successfully", "xin-zeng-cheng-gong": "New successfully added", "xin-zeng-jian-yan-xiang": "Add inspection items", "yin-yong-cheng-gong": "Reference successful", "yin-yong-jian-yan-xiang": "Reference inspection items", "gong-wei-xin-xi-bu-neng-wei-kong": "Workstation information cannot be empty", "gong-zhuang-bian-hao-bu-neng-wei-kong-qing-xuan-ze": "The tooling number cannot be empty, please select!", "gong-zhuang-bian-hao-xiao-hao-lei-xing-bu-neng-wei-kong-qing-xuan-ze": "Tool number, consumption type cannot be empty, please select!", "gong-zhuang-xin-xi-bu-neng-wei-kong": "The tooling information cannot be empty", "bian-ji": "edit", "chuang-jian-shi-jian": "Creation time", "gong-xu-bian-hao": "Process number", "que-xian-bian-hao": "Defect number", "que-xian-mo-shi": "Defect mode", "que-xian-mo-shi-lie-biao": "Defect Mode List", "shan-chu": "delete", "xin-zeng": "Add", "xu-hao": "Serial number", "qing-shu-ru-que-xian-bian-hao": "Please enter the defect number", "qing-shu-ru-que-xian-mo-shi": "Please enter defect mode", "xin-zeng-que-xian-mo-shi": "Add defect mode", "qing-shu-ru": "Please enter", "bian-ji-cheng-gong": "Edited successfully", "bian-ji-que-xian-mo-shi": "Edit defect mode", "gong-xu": "working procedure", "que-ren-shan-chu-gai-ku-wei-ma": "Are you sure to delete this storage location?", "qing-shu-ru-ku-wei-hao": "Please enter the warehouse location number", "qing-xuan-ze-yao-da-yin-de-ku-wei": "Please select the warehouse location to print", "qing-xuan-ze-yao-shan-chu-de-ku-wei": "Please select the storage location to delete", "que-ren-shan-chu-xuan-zhong-de-ku-wei-ma": "Are you sure to delete the selected storage location?", "da-yin": "Print", "dao-ru-ku-wei": "Import storage location", "pi-liang-chuang-jian": "Batch creation", "xia-zai-mo-ban": "Download Template", "xin-zeng-ku-wei": "Add new storage location", "fen-qu": "partition", "ku-qu": "Warehouse area", "ku-wei-hao": "Warehouse location number", "ku-wei-cun-fang-lei-xing": "Storage location type", "ku-wei-lei-xing": "Location type", "zui-da-ti-ji-m": "Maximum volume m ³", "chu-ku-jin-yong": "Outbound disabled", "cun-fang-gui-ze": "Storage rules", "qing-xuan-ze-zuo-ce-jie-dian": "Please select the left node", "ru-ku-jin-yong": "Storage disabled", "zui-da-shu-liang": "Maximum quantity", "zui-da-tuo-pan-shu-liang": "Maximum number of pallets", "zui-da-xiang-shu-liang": "Maximum number of boxes", "zui-da-zhong-liang-kg": "Maximum weight kg", "gai-cang-ku-xia-cun-zai-zi-ji-shu-ju": "There is subset data in this warehouse,", "qing-shu-ru-ku-qu-fen-qu-ming-cheng": "Please enter the name of the storage area/partition", "gai-ku-qu-xia-cun-zai-zi-ji-shu-ju": "There is subset data in this storage area,", "que-ren-shan-chu-gai-cang-ku-ma": "Are you sure to delete this warehouse?", "que-ren-shan-chu-gai-fen-qu-ma": "Are you sure to delete this partition?", "que-ren-shan-chu-gai-ku-qu-ma": "Are you sure to delete this storage area?", "xin-zeng-cang-ku": "Add warehouse", "qi-yong-zi-dong-pei-huo": "Enable automatic distribution", "cang-ku-bian-hao": "Warehouse Number", "cang-ku-ming-cheng": "Warehouse name", "guan-lian-gong-chang": "Related factories", "qi-yong-chu-wei-guan-li": "Enable storage management", "qing-shu-ru-bian-hao": "Please enter the number", "qing-shu-ru-ming-cheng": "Please enter a name", "ji-ben-xin-xi": "essential information", "ti-shi": "Tips", "wu-liao-bian-ma-bu-neng-wei-kong-qing-tian-xie": "Material code cannot be empty, please fill in", "wu-liao-qi-yong-ru-ku-zhi-jian-qing-xuan-ze-zhi-jian-fan-wei": "Enable incoming quality inspection for materials. Please select the scope of quality inspection", "ru-ku-pei-zhi": "Inventory configuration", "chu-ku-pei-zhi": "Outbound configuration", "wu-liao-shu-xing": "Material properties", "ye-wu-lei-xing": "Business type", "ku-qu-bian-hao": "Warehouse area number", "ku-qu-ming-cheng": "Warehouse area name", "suo-shu-cang-ku": "Belonging warehouse", "xin-zeng-ku-qu": "Add new storage area", "cun-chu-lei-xing": "Storage type", "jiao-se": "role", "qing-xuan-ze-cun-chu-lei-xing": "Please select storage type", "qing-xuan-ze-jiao-se": "Please select a role", "shi-fou-qi-yong": "Is it enabled", "zhi-chi-de-chu-ku-ye-wu": "Supported outbound business", "zhi-chi-de-ru-ku-ye-wu": "Supported inbound business", "fen-qu-bian-hao": "Partition number", "fen-qu-bian-hao-bu-neng-zhong-fu": "Partition numbers cannot be duplicated", "fen-qu-ming-cheng": "Partition Name", "qing-tian-xie-bi-tian-xiang": "Please fill in the required fields", "suo-shu-ku-qu": "Belonging to the warehouse area", "tian-jia": "add to", "xin-zeng-fen-qu": "Add partition", "suo-shu-qu-yu": "Belonging region", "ku-wei-bian-ma": "Warehouse location code", "ku-wei-wei-zhi-shu-xing": "Location attribute of storage location", "xiang-dao": "tunnel", "ceng": "layer", "jin-shen": "De<PERSON><PERSON>", "lie": "column", "pai": "Row", "hu-lve-bao-zhuang": "Ignore packaging", "hu-lve-sn": "Ignore SN", "zuo-ye-shu-xing": "Homework attributes", "fou": "no", "hun-fang-gui-ze": "Mixing rules", "jian-huo-fang-shi": "Picking method", "shi": "yes", "xian-zhi": "limit", "xiu-gai-cang-ku": "Modify warehouse", "xiu-gai-cheng-gong": "Modified successfully", "guan-lian-he-zuo-huo-ban": "Related partners", "xiu-gai-ku-qu": "Modify the storage area", "xiu-gai-fen-qu": "Modify partition", "bian-ji-ku-wei": "Edit storage location", "bao-zhuang-cai-liao-ming-cheng": "Packaging Material Name", "bei-zhu": "remarks", "cao-zuo-cheng-gong": "Operation successful", "kong-xian": "free", "rong-qi-bian-hao": "Container number", "rong-qi-lei-xing": "Container type", "rong-qi-qing-dan": "Container List", "shi-fou-que-ren-shan-chu": "Are you sure to delete?", "zhan-yong": "occupy", "zhuang-tai": "state", "qing-shu-ru-bao-zhuang-cai-liao-ming-cheng": "Please enter the name of the packaging material", "qing-xuan-ze-cang-ku": "Please select warehouse", "qing-shu-ru-bei-zhu": "Please enter a note", "qing-xuan-ze-rong-qi-lei-xing": "Please select container type", "zhan-yong-xiang-qing": "Occupation Details", "gen-zong-hao": "Tracking number", "gen-zong-hao-lei-xing": "Tracking number type", "guan-bi": "close", "pi-ci-hao": "Batch number", "shu-liang": "quantity", "que-ding-shan-chu-gai-tiao-jiao-huo-bao-gao-ma": "Are you sure to delete this delivery report?", "bian-geng-hou-shou-pi-chan-pin-pan-ding": "Determination of the first batch of products after the change", "ce-shi-chan-pin-pan-ding": "Test product judgment", "chuang-jian-ren-yuan": "Create personnel", "jiao-huo-bao-gao-qing-dan": "Delivery Report Checklist", "pi-liang-pan-ding": "Batch judgment", "ppap-pan-ding": "PPAP determination", "rang-bu-chan-pin-pan-ding": "Concession product judgment", "wu-liao-bian-hao": "Material Number", "jiao-huo-bao-gao-lei-xing-bi-xuan": "Delivery report type required!", "jiao-huo-bao-gao-pei-zhi-lei-xing-wei-ppap-shi-deng-ji-bi-xuan": "When the delivery report configuration type is PPAP, the level must be selected", "xiu-gai-cheng-gong-0": "Modified successfully!", "ji-chu-xin-xi": "Basic Information", "jiao-huo-bao-gao-pei-zhi": "Delivery report configuration", "xi-tong-dai-chu": "System brought out", "ppap-deng-ji": "PPAP level", "jiao-huo-mo-ban": "Delivery Template", "que-ding-yi-chu-zhe-tiao-shu-ju-ma": "Are you sure to remove this data?", "que-ding-shan-chu-gai-tiao-shu-ju-ma": "Are you sure to delete this data?", "wen-dang-lie-biao": "Document List", "wen-jian-ban-ben-xin-xi": "file version information", "wen-jian-bian-hao": "Document Number", "wen-jian-lei-xing": "file type", "wen-jian-ming": "file name", "xiang-qing": "details", "lu-ru-ren": "Input person", "shang-chuan-shi-jian": "Upload time", "shi-xiao-shi-jian": "Expiration time", "wen-jian-miao-shu": "Document Description", "bao-cun-cheng-gong": "Saved successfully", "geng-xin-cheng-gong": "Update successful", "qing-dian-ji-shang-chuan-wen-jian": "Please click to upload the file", "qing-shang-chuan-wen-jian": "Please upload the file", "qing-xuan-ze-fa-bu-fan-wei": "Please select the publishing scope", "shang-chuan-shi-bai": "Upload failed", "shang-chuan-wen-jian-shu-liang-chao-chu-xian-zhi-qing-shan-chu-xian-you-wen-jian-hou-zai-shang-chuan": "The number of uploaded files exceeds the limit. Please delete the existing files before uploading again!", "shi-fou-xu-yao-shi-xiao-shi-jian": "Do you need an expiration time", "wen-jian-ming-cheng": "File Name", "bao-chi-dang-qian-ban-ben": "Maintain the current version", "que-ding": "determine", "sheng-ji-ban-ben": "Upgrade version", "shi-fou-geng-xin-ban-ben": "Do you want to update the version", "zhi-chi-de-ge-shi-pngjpgpdf": "Supported formats: PNG, JPG, PDF", "fu-jian": "enclosure", "fa-bu-fan-wei": "Release scope", "fan-hui": "return", "zan-wu-wen-jian": "No files available at the moment", "chu-ku-ce-lve": "Outbound strategy", "shi-fou-qiang-zhi": "Is it mandatory", "shi-yong-tiao-jian": "Applicable conditions", "wu-liao-lei-xing": "Material type", "you-xian-ji": "priority", "chuang-jian-ren": "Creator", "huo-pin-shu-xing": "Product attributes", "ke-li-du": "Particle size", "ku-wei-fan-wei": "Location range", "zhou-zhuan-gui-ze": "Turnover rules", "ce-lve-ming-cheng": "Strategy Name", "chan-pin-deng-ji": "Product grade", "qing-shu-ru-tian": "Please enter the day", "qing-xuan-ze-fen-qu": "Please select partition", "qing-xuan-ze-ku-qu": "Please select the warehouse area", "mu-di-cang-ku": "Destination Warehouse", "fa-huo-cang-ku": "Delivery Warehouse", "fa-huo-ku-qu": "Delivery staBin", "tou-liao-lan-jie-kong-zhi": "Material Interception Control", "fan-xiu-ci-shu-xian-zhi": "Repair Frequency Limit", "xian-zhi-ci-shu": "Limit the number of times", "ti-jiao": "Submit", "tian": "day", "xiao-qi": "Validity period", "zhi-ding-fen-qu": "Specify partition", "zhi-ding-ku-qu": "Designated warehouse area", "shang-jia-ce-lve": "Listing strategy", "xun-zhi-suan-fa": "Addressing algorithm", "chu-ku-jin-yong-biao-shi": "Prohibited outbound label", "que-ding-shan-chu-gai-tiao-ku-wei-shu-ju-ma": "Are you sure to delete this storage location data?", "ru-ku-jin-yong-biao-shi": "Storage disabled identification", "shu-ju-lie-biao": "Data List", "zui-da-ti-ji": "Maximum volume", "zui-da-zhong-liang": "Maximum weight", "bu-huo-ce-lve": "Restocking strategy", "cao-zuo-cheng-gong-0": "Operation successful!", "ku-wei": "Warehouse location", "biao-ge-shu-ju-bu-neng-wei-kong-huo-zhe-0": "Table data cannot be empty or 0!", "bu-huo-mu-de-di": "Restocking destination", "mu-de-ku-qu": "Destination warehouse area", "que-ding-yi-chu-zhe-tiao-wu-liao-ming-xi-ma": "Are you sure to remove this material detail?", "wu-liao-pei-zhi-bu-neng-wei-kong": "Material configuration cannot be empty!", "wu-liao-yi-cun-zai": "The material already exists!", "bu-huo-ku-qu": "Replenishment warehouse area", "mu-de-ku-wei": "Destination storage location", "qing-xuan-ze-ku-wei": "Please select a storage location", "wu-liao-pei-zhi": "Material configuration", "zui-da-ku-cun": "Maximum inventory", "zui-xiao-ku-cun": "Minimum inventory", "fa-bu": "release", "fa-bu-cheng-gong": "Published successfully", "gong-yi-bian-hao": "Process number", "ji-hua-bei-liao": "Plan material preparation", "ji-hua-gong-dan-lie-biao": "Planned work order list", "qi-tao-xing-jian-cha": "Conformity check", "qing-xuan-ze-yi-xiang": "Please choose one option", "que-ding-che-hui-gai-tiao-ji-hua-dan-zhuang-tai-ma": "Are you sure to withdraw the status of this plan?", "que-ding-shan-chu-gai-tiao-ji-hua-dan-shu-ju-ma": "Are you sure to delete this plan data?", "zhi-shao-xuan-ze-yi-xiang": "Select at least one option", "ban-ben-hao": "Version number", "che-hui": "withdraw", "yi-jian-guan-bi": "One click close", "chong-xin-kai-qi": "Reopen", "kai-qi": "Open", "gong-dan-bian-hao": "Work Order Number", "gong-dan-zhuang-tai": "Work order status", "gong-yi-ming-cheng": "Process name", "ji-hua-jie-shu-shi-jian": "Planned end time", "ji-hua-kai-shi-shi-jian": "Planned start time", "ji-hua-shu-liang": "Planned quantity", "ke-hu-hao": "Customer Number", "sheng-chan-zhuang-tai": "Production status", "wu-liao-bian-ma": "Material code", "bao-zhuang-cai-liao-bian-hao": "Packaging Material Number", "bian-ji-bom": "Edit BOM", "bian-ji-gong-yi-lu-jing": "Edit process path", "gong-dan-lei-xing": "Work order type", "gong-yi-lu-jing": "Process path", "qing-xian-xuan-ze-wu-liao": "Please select the material first", "xin-gong-yi-lu-jing": "New process path", "xuan-ze-biao-zhun-gong-yi": "Select standard process", "zhuang-xiang-gui-ge": "Packing specifications", "gong-yi-lu-jing-ban-ben": "Process path version", "gong-yi-lu-jing-ming-cheng": "Process path name", "shi-fou-shou-gong-xu-tou-liao": "Is the first process feeding required", "xuan-ze-biao-zhun-gong-yi-hou-dai-chu": "After selecting the standard process, bring it out", "gong-dan-bian-hao-0": "Work Order Number:", "ji-hua-bei-liao-cheng-gong": "Planned material preparation successful", "ji-hua-jie-shu-shi-jian-0": "Planned end time:", "ji-hua-kai-shi-shi-jian-0": "Planned start time:", "ji-hua-shu-liang-0": "Planned quantity:", "wu-liao-bian-hao-0": "Material Number:", "wu-liao-ming-cheng": "Material Name:", "bei-liao-shu-liang": "Quantity of prepared materials", "bom-yong-liang": "BOM usage", "dan-wei": "Company", "lei-xing": "type", "dao-ru-bom": "Import BOM", "dao-ru-cheng-gong": "Import successful", "lai-yuan-ku-wei": "Source location", "lai-yuan-lei-xing": "Source type", "que-ding-shan-chu-ma": "Are you sure to delete?", "tou-ru-fang-shi": "Investment method", "wen-jian-ge-shi-cuo-wu-qing-shang-chuan-excel-wen-jian": "File format error, please upload Excel file", "xiao-hao-ku-wei": "Consuming storage locations", "yi-chu": "remove", "yuan-liao-cang-ku": "Raw material warehouse", "zui-da-tou-liao-shu": "Maximum feeding quantity", "zui-di-tou-liao-shu": "Minimum feeding quantity", "ban-ben": "edition", "kou-jian-gui-ze": "Deduction rules", "qing-xuan-ze-gong-xu": "Please select the process", "shang-liao-gong-wei-zhi-ding": "Designated loading station", "xiao-hao-fang-shi": "Consumption method", "xuan-ze-gong-yi": "Select process", "zi-ling-jian-sao-miao": "Scanning of sub components", "gong-yi-lu-xian-bian-hao": "Process route number", "gong-yi-lu-xian-ming-cheng": "Process route name", "bom-bian-hao": "BOM Number", "bu-man-zu": "dissatisfaction", "man-zu": "satisfy", "bao-cai-xin-xi": "Packaging material information", "bom-xin-xi": "BOM Information", "ku-cun-shu-liang": "Inventory quantity", "shi-fou-man-zu-sheng-chan": "Whether it meets the production requirements", "yuan-cai-liao-cang": "Raw material warehouse"}