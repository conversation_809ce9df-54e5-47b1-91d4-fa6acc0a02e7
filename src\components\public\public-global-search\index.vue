<template>
  <div class="public-global-search">
    <el-input class="out-input" v-model="searchVal" placeholder="搜索" readonly @click="openDialog">
      <template #prepend>
        <svg-icon name="search" size="16" class="primary" />
      </template>
    </el-input>
    <el-dialog v-model="dialogVisible" width="600" :show-close="false" @close="searchVal = ''">
      <el-input ref="innerInputRef" class="inner-input" v-model="searchVal" placeholder="搜索" @input="goSearch">
        <template #prepend>
          <svg-icon name="search" size="16" class="primary" />
        </template>
      </el-input>
      <div>
        <ul class="result-list" v-if="resultList.length">
          <li class="result-item" v-for="(item, index) in resultList" :key="index + item.path" @click="resultClick(item)">
            <p class="result-item-title">{{ $t(item.meta.title) }}</p>
            <p class="result-item-subtitle">
              <span>{{ item.path }}</span>
            </p>
          </li>
        </ul>
        <ul class="result-list" v-else>
          <el-empty description="暂无数据" />
        </ul>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, nextTick, getCurrentInstance } from 'vue'
import router from '@/router'
const { proxy } = getCurrentInstance()

const searchVal = ref('')
const innerInputRef = ref(null)
const dialogVisible = ref(false)
const options = router.options.routes
const resultList = ref([])

function openDialog() {
  dialogVisible.value = true
  resultList.value = []
  nextTick(() => {
    setTimeout(() => {
      innerInputRef.value.focus()
    }, 500)
  })
}

function goSearch() {
  if (searchVal.value) {
    debounce(search, 300)()
  }
}

function search() {
  if (!searchVal.value) {
    resultList.value = []
    return
  }
  const result = []
  search(options)
  resultList.value = result
  function search(list = []) {
    for (const item of list) {
      if (item.meta) {
        if (item.meta.parent) {
          continue
        }
        if (isHit(proxy.$t(item.meta.title), searchVal.value) || isHit(item.name, searchVal.value) || isHit(item.path, searchVal.value)) {
          result.push(item)
          console.log(item.meta)
        }
      }
      if (item.children) {
        search(item.children)
      }
    }
  }
  function isHit(text1, text2) {
    return text1.toLowerCase().includes(text2.toLowerCase())
  }
}

function resultClick(item) {
  router.push(item.path)
  dialogVisible.value = false
}

// 防抖
function debounce(fn, delay) {
  let timer = null
  return function () {
    clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}
</script>
<style scoped lang="scss">
.public-global-search {
  :deep(.el-input.out-input) {
    width: 200px;
    box-sizing: border-box;
    position: relative;
    * {
      cursor: pointer;
    }
    .el-input__wrapper {
      background-color: rgba(50, 118, 245, 0.1);
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
      box-shadow: none;
    }
    .el-input-group__prepend {
      padding: 0 0 0 15px;
      background-color: rgba(50, 118, 245, 0.1);
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
      box-shadow: none;
    }
    .el-input__inner::placeholder {
      color: #999;
    }
    &:hover {
      border-radius: 20px;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 20px;
        border: 2px solid #3276f5;
        cursor: pointer;
        pointer-events: none;
      }
    }
  }
  :deep(.el-dialog) {
    .el-dialog__header {
      padding: 0;
    }
    .el-dialog__body {
      padding: 15px;
    }
    .el-input.inner-input {
      width: 100%;
      height: 45px;
      border: 2px solid #3276f5;
      border-radius: 8px;

      .el-input__wrapper {
        font-size: 20px;
        background-color: rgba(50, 118, 245, 0.1);
        box-shadow: none;
      }
      .el-input-group__prepend {
        padding: 0 0 0 15px;
        background-color: rgba(50, 118, 245, 0.1);
        box-shadow: none;
      }
    }
  }
}
.result-list {
  margin-top: 20px;
  min-height: 40vh;
  max-height: 60vh;
  overflow-y: auto;
}
.result-item {
  padding: 8px 10px;
  margin: 10px 0;
  border-radius: 8px;
  box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  opacity: 0.9;
  .result-item-title {
    color: #333;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .result-item-subtitle {
    color: #666;
    font-size: 12px;
    span {
      margin-right: 20px;
    }
  }
  &:hover {
    opacity: 1;
    box-shadow: 0 0 8px 1px rgba(0, 0, 0, 0.1);
    background-color: #3276f5;
    .result-item-title {
      color: white;
    }
    .result-item-subtitle {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
