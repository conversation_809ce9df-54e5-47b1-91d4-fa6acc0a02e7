<script setup>
import { ref, watch } from 'vue'
defineEmits(() => ['node-click', 'check'])
const props = defineProps({
  option: {
    type: Object
  },
  filterText: {
    type: String,
    default: ''
  },
  specailStyle: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  autoHeight: {
    type: <PERSON><PERSON>an,
    default: false
  }
})

const slots = defineSlots()
const filterNode = (value, data) => {
  // (value ? data[props.option.props.label].includes(value) : true)
  console.log(data, props.option, 'zhi')
  const labelValue = data[props.option.props.label]
  console.log(labelValue, 'kkkkk')
  if (value) {
    return labelValue.includes(value)
  } else {
    return true
  }
}

const RefTree = ref()
watch(
  () => props.filterText,
  (val) => {
    if (!RefTree.value) return
    RefTree.value.filter(val)
    console.log(RefTree.value, 'wqwq')
  }
)
defineExpose({ RefTree })
</script>
<template>
  <el-tree ref="RefTree" class="pulic-tree" :class="[{ 'tree-style': specailStyle }, { 'auto-height': props.autoHeight }]" v-if="option" v-bind="option" :filter-node-method="filterNode">
    <template #default="{ data, node }">
      <div class="icon-warp" v-if="data.icon">
        <SvgIcon :name="data.icon" size="18" color="white" />
      </div>
      <div class="text-warp">
        <slot v-if="slots.body" name="body" :data="data"></slot>
        <span v-else :style="data.style || undefined">{{ data.label }}</span>
      </div>
      <div class="operator">
        <slot name="operator" :data="data" :node="node"></slot>
      </div>
    </template>
  </el-tree>
</template>

<style lang="scss" scoped>
.el-tree.pulic-tree {
  :deep(.el-tree-node__content) {
    height: 50px;
    line-height: 50px;
    box-sizing: border-box;
    border-bottom: 1px solid $border;

    .icon-warp {
      width: 30px;
      height: 30px;
      margin-right: 8px;
      flex-shrink: 0;
      border-radius: 50%;
      background-color: $primary;
      display: flex;
      @include flex-center;
    }

    .text-warp {
      font-size: 14px;
      color: #666;
    }
  }

  :deep(.is-current) {
    > .el-tree-node__content {
      background-color: $primary !important;

      .text-warp {
        color: #fff !important;
      }

      .icon-warp {
        background-color: change-color($primary, $green: 80) !important;
      }
    }
  }

  .operator {
    margin: 0 10px;
    display: flex;
    flex-direction: row-reverse;
    flex: 1;
    flex-basis: 0;
  }
}

.tree-style {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  background: #fff;
  padding: 10px;
  box-shadow: 0px 7px 9px 0px rgba(171, 171, 171, 0.25);
}

.auto-height {
  :deep(.el-tree-node__content) {
    height: auto !important;
    line-height: 1.5 !important;
  }
}
</style>
