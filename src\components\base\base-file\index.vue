<script setup>
import { ref } from 'vue'
import { uploadFile } from '@/api/modules/device'
const props = defineProps({
  title: {
    type: String,
    default: '文件上传'
  }
})

// 文件配置
const fileList = ref([])
const loading = ref(false)
// 事件
const emit = defineEmits(['queryFile'])

// 获取文件列表
// eslint-disable-next-line
const getFiles = () => {}

// 文件上传
const httpRequest = (e) => {
  let fd = new FormData()
  fd.append('files', e.file)
  loading.value = true
  uploadFile(fd)
    .then((res) => {
      emit('queryFile', { file: res, fileList: fileList.value })
    })
    .catch(() => {})
    .finally(() => {
      loading.value = false
    })
}
</script>

<template>
  <div class="base-file">
    <!-- 上传文件 -->
    <div class="upload-file">
      <el-upload class="avatar-uploader" action="#" multiple v-model:file-list="fileList" :show-file-list="false" :http-request="httpRequest">
        <el-button type="primary" :loading="loading">{{ props.title }}</el-button>
      </el-upload>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
