<template>
  <slot></slot>
</template>

<script setup>
import { inject, provide, ref, watchEffect } from 'vue'
import { formContextKey } from 'element-plus'
// 通过模拟 el-form 提供的 formContextKey 可以用来禁用该组件中所有已注入表单禁用控制的组件。但无法穿透到el-form组件内 因为el-form提供的依赖会覆盖模拟的
// 遇到 el-form 标签的话 还是需要手动设置 disabled 属性

/**
 * 如果改组件存在 <base-bpmn> 内部的话会自动取当前流程的是否可编辑状态。 props.disabled 的优先级更高
 */

const bpmn = inject('bpmn')
const props = defineProps({
  disabled: {
    type: [undefined, Boolean],
    default: undefined
  }
})
const disabled = ref(props.disabled)
const formContext = ref({ disabled: disabled })

watchEffect(() => {
  if (typeof props.disabled === 'boolean') {
    disabled.value = props.disabled
  } else {
    if (bpmn && bpmn.value && bpmn.value.enable) {
      disabled.value = bpmn.value.formDisabled
    } else {
      disabled.value = false
    }
  }
})

provide(formContextKey, formContext.value)
</script>
<style scoped></style>
