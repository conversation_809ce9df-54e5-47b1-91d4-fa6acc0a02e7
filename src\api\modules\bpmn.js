import { showLoading } from '@/common/loading-mask'
import _request from '@/common/request.js'
// Bpmn
// BPMN服务接口配置
const serviceConf = {
  messageKey: 'message', //消息键名
  statusKey: 'status', //状态键名
  successStatus: 100 //状态成功键名
}

function request(d) {
  return _request({ ...serviceConf, ...d })
}

/**
 * @description:保存模型
 * @param { Object }: data
 * @return { Promise }
 */
export const ApiSaveModel = (params = {}) => {
  return request({
    url: '/bpmn_api/admin/bm/save_model',
    method: 'POST',
    params
  })
}

/**
 * @description:模型分页列表
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiModelPage = (params = {}) => {
  return request({
    url: '/bpmn_api/admin/bm/model_page',
    method: 'GET',
    params
  })
}

/**
 * @description:模型详情查询
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiGetDetailBpmnXml = (id) => {
  return request({
    url: `/bpmn_api/admin/bm/detail_bpmn_xml/${id}`,
    method: 'GET',
    showLoading: true
  })
}

/**
 * @description:模型详情保存
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiSaveDetailBpmnXml = (id, data) => {
  return request({
    url: `/bpmn_api/admin/bm/save_bpmn/${id}`,
    method: 'POST',
    showLoading: true,
    data
  })
}

/**
 * @description:模型部署
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiDeployModel = (id, data) => {
  return request({
    url: `/bpmn_api/admin/bm/deploy/${id}`,
    method: 'POST',
    showLoading: true,
    data
  })
}

/**
 * @description:已部署流程查询
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiDeployPage = ({ key, name, pageNum, pageSize }) => {
  return request({
    url: `/bpmn_api/admin/bm/pd_page`,
    method: 'GET',
    showLoading: true,
    params: { key, name, pageNum, pageSize }
  })
}

/**
 * @description:拾取任务
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiClaimTask = ({ taskId, accountName }) => {
  return request({
    url: `/bpmn_api/admin/activiti/claim_task`,
    method: 'GET',
    showLoading: true,
    params: { taskId, accountName }
  })
}

/**
 * @description:完成任务
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiCompleteTask = ({ taskId, remark }) => {
  return request({
    url: `/bpmn_api/admin/activiti/complete_task`,
    method: 'GET',
    showLoading: true,
    params: { taskId, remark }
  })
}

/**
 * @description:驳回任务
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiBackTask = ({ taskId, remark }) => {
  return request({
    url: `/bpmn_api/admin/activiti/back_task`,
    method: 'GET',
    showLoading: true,
    params: { taskId, remark }
  })
}

/**
 * @description:审批记录
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiActivitiHistory = ({ processInstanceId }) => {
  return request({
    url: `/bpmn_api/admin/activiti/history_list/${processInstanceId}`,
    method: 'GET',
    showLoading: true,
    params: { processInstanceId }
  })
}

/**
 * @description:审批记录-该单据所有流程
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiActivitiHistoryAll = ({ pageCode, docCode }) => {
  return request({
    url: `/bpmn_api/admin/activiti//page/history_all`,
    method: 'GET',
    showLoading: true,
    params: { pageCode, docCode }
  })
}

/**
 * @description:模型删除
 * @param { Object }: params
 * @return { Promise }
 * 为什么用GET
 */
export const ApiDelModel = (id) => {
  return request({
    url: `/bpmn_api/admin/bm/del/${id}`,
    method: 'GET',
    showLoading: true
  })
}

/**
 * @description:根据页面编码，获取当前页面可以发起的流程
 * @param { Object }: params
 * @return { Promise }
 */
export const ApiGetActiviti = ({ docCode, pageCode, userNumber }) => {
  return request({
    url: `/bpmn_api/admin/activiti/page_act`,
    method: 'GET',
    showLoading: true,
    params: { docCode, pageCode, userNumber }
  })
}

/**
 * @description:订单发起流程
 * @param { Object }: params
 * @return { Promise }
 * 为什么用GET
 */
export const ApiActivitiStart = ({ docCode, modelKey, pageCode, userNumber }) => {
  return request({
    url: `/bpmn_api/admin/activiti/start`,
    method: 'GET',
    showLoading: true,
    params: { docCode, modelKey, pageCode, userNumber }
  })
}
