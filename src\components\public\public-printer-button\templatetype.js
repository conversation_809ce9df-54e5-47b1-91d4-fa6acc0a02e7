const options = [
  {
    label: '自定义',
    value: '0'
  },
  {
    label: '箱标签',
    value: '1'
  },
  {
    label: '拣货单',
    value: '2'
  },
  {
    label: '送货单',
    value: '3'
  },
  {
    label: '库位标签',
    value: '4'
  },
  {
    label: '托盘码',
    value: '5'
  }
]

function findLabel(value) {
  const fd = options.find((item) => item.value === value)
  if (fd) {
    return fd.label
  }
  return value
}

function findItem(value) {
  return options.find((item) => item.value === value)
}

export { options, findLabel, findItem }
