import request from '@/common/request.js'

import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

//导出
export async function exportEXCEL(params, url, fileName = '', isPost = true, isMethod = 'post') {
  const resp = await requestExcel(params, url, isPost, isMethod)
  const blob = new Blob([resp], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  })
  const objectUrl = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = objectUrl
  a.download = fileName
  document.body.appendChild(a)
  a.click()
  a.remove()
  URL.revokeObjectURL(objectUrl)
}

export function requestExcel(data, url, isPost = true, isMethod = 'post') {
  return request({
    url,
    method: isMethod,
    data: !isPost ? undefined : data,
    params: isPost ? undefined : data,
    responseType: 'blob',
    origin: true,
    showLoading: true,
    skip: true,
    headers: {
      'content-type': 'application/json',
      'X-Auth-Token': useUserStore().token
    }
  })
}

//不需要参数的导入
export async function importExcel(url, isCover = false, refresh, e) {
  const formData = new FormData()
  formData.append('file', e.file)
  formData.append('isCover', isCover)
  return request({
    url,
    method: 'post',
    data: formData,
    showLoading: true
  }).then(() => {
    ElMessage.success('导入成功')
    refresh()
  })
}

//不需要参数的导入，且需要把返回值返回回去
export async function importExcelReturn(url, isCover = false, refresh, e) {
  const formData = new FormData()
  formData.append('file', e.file)
  formData.append('isCover', isCover)
  return request({
    url,
    method: 'post',
    data: formData,
    showLoading: true
  }).then((resp) => {
    ElMessage.success('导入成功')
    refresh(resp)
  })
}

//路径上带参数的导入
export async function importParamsExcel(url, isCover = false, refresh, params = {}, e) {
  const formData = new FormData()
  formData.append('file', e.file)
  formData.append('isCover', isCover)
  return request({
    url,
    method: 'post',
    data: formData,
    params: params,
    showLoading: true
  }).then(() => {
    ElMessage.success('导入成功')
    refresh()
  })
}

export function useExportExcel() {
  const exportExcel = exportEXCEL
  return {
    exportExcel,
    importExcel,
    importExcelReturn,
    importParamsExcel
  }
}
