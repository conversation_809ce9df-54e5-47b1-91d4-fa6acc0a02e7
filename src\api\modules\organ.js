import request from '@/common/request.js'

/*
 *   获取组织架构树
 *   @param { void }
 *   @returns { Promise }
 */
export const GetOrganTree = () => {
  return request({
    url: '/api/v1/dept/options',
    method: 'GET'
  })
}

/*
 *   获取组织架构详情
 *   @param { id }
 *   @returns { Promise }
 */
export const GetOrganDetail = (id) => {
  return request({
    url: `/api/v1/dept/${id}/form`,
    method: 'GET'
  })
}

/*
 *   修改组织架构
 *   @param { dept }
 *   @returns { Promise }
 */
export const UpdateOrganById = (data) => {
  return request({
    url: `/api/v1/dept/${data.deptId}/update`,
    method: 'PUT',
    data
  })
}

/*
 *   新增组织架构
 *   @param { dept }
 *   @returns { Promise }
 */
export const AddOrgan = (data) => {
  return request({
    url: `/api/v1/dept/save`,
    method: 'POST',
    data
  })
}

/*
 *   删除组织架构
 *   @param { Array } deptIds "1,2,3"
 *   @returns { Promise }
 */
export const DelOrganByIds = (deptIds) => {
  return request({
    url: `  /api/v1/dept/${deptIds}/delete`,
    method: 'DELETE'
  })
}
