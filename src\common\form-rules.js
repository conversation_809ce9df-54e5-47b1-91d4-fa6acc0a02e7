import i18n from '@/i18n'
import { computed } from 'vue'
// 组织架构
export const RuleDeptSubmit = computed(() => {
  return {
    deptType: [{ required: true, message: i18n.$t('organ.selectOrganType'), trigger: 'blur' }],
    deptName: [{ required: true, message: i18n.$t('organ.inputOrganName'), trigger: 'blur' }]
  }
})

// 用户新增
export const RuleUserSubmit = computed(() => {
  return {
    accountName: [{ required: true, message: i18n.$t('form.inputAccount'), trigger: 'blur' }],
    age: [{ required: true, message: i18n.$t('form.inputAge'), trigger: 'blur' }],
    userName: [{ required: true, message: i18n.$t('form.inputName'), trigger: 'blur' }],
    sex: [{ required: true, message: i18n.$t('form.selectSex'), trigger: 'change' }],
    mobile: [{ required: false, message: i18n.$t('form.inputPhone'), trigger: 'blur' }],
    identityId: [{ required: false, message: i18n.$t('form.inputIdentityId'), trigger: 'blur' }],
    staffCode: [{ required: true, message: i18n.$t('form.inputEmployeeId'), trigger: 'blur' }],
    email: [
      { required: true, message: i18n.$t('form.inputEmail'), trigger: 'blur' },
      { type: 'email', message: i18n.$t('form.emailValidate'), trigger: ['blur', 'change'] }
    ],
    idCard: [
      { required: false, message: i18n.$t('form.inputID'), trigger: 'blur' },
      { min: 18, max: 18, message: i18n.$t('form.IDValidate'), trigger: 'blur' }
    ],
    jobName: [{ required: true, message: i18n.$t('form.inputJob'), trigger: 'blur' }],
    companyId: [{ required: false, message: i18n.$t('form.selectCompany'), trigger: 'change' }],
    wxAccount: [{ required: false, message: i18n.$t('form.inputWx'), trigger: 'blur' }],
    userType: [{ required: true, message: i18n.$t('form.selectUserType'), trigger: 'change' }],
    cooperateCode: [{ required: true, message: i18n.$t('form.selectPartner'), trigger: 'change' }]
  }
})

// 角色新增
export const RuleRoleSubmit = computed(() => {
  return {
    roleName: [{ required: true, message: i18n.$t('role.inputName'), trigger: 'blur' }],
    roleCode: [{ required: true, message: i18n.$t('role.inputCode'), trigger: 'blur' }]
  }
})

// 权限新增
export const RuleAuthSubmit = computed(() => {
  return {
    menuType: [{ required: true, message: i18n.$t('permission.selectType'), trigger: 'change' }],
    menuName: [{ required: true, message: i18n.$t('permission.inputName'), trigger: 'blur' }],
    menuUrl: [{ required: true, message: i18n.$t('permission.inputRoute'), trigger: 'blur' }],
    menuCode: [{ required: true, message: i18n.$t('permission.inputCode'), trigger: 'blur' }]
  }
})

// 设备管理校验
// 设备台账新增+详情
// 头部表单
export const RuleLedgerHeaderSubmit = {
  devCode: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
  devName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  devStatus: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
  assetsNo: [{ required: true, message: '请输入资产编码', trigger: 'blur' }],
  assetsDesc: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
  assetsClass: [{ required: true, message: '请输入资产类型', trigger: 'blur' }],
  classCode: [{ required: true, message: '请输入设备种类编码', trigger: 'blur' }],
  className: [{ required: true, message: '请输入设备种类名称', trigger: 'blur' }],
  respPersonNo: [{ required: true, message: '请选择责任人工号', trigger: 'change' }],
  yearOfManu: [{ required: true, message: '请选择出厂年份', trigger: 'change' }],
  arriveAt: [{ required: true, message: '请选择进厂日期', trigger: 'change' }],
  serviceLife: [{ required: true, message: '请输入使用年限', trigger: 'blur' }],
  fcCode: [{ required: true, message: '请选择进所属工厂', trigger: 'change' }],
  shopCode: [{ required: true, message: '请选择进所属车间', trigger: 'change' }],
  vsmCode: [{ required: true, message: '请选择进所属工段', trigger: 'change' }],
  lineCode: [{ required: true, message: '请选择进所属产线', trigger: 'change' }],
  supplierName: [{ required: true, message: '请输入所属供应商', trigger: 'blur' }],
  supplierAddress: [{ required: true, message: '请输入供应商地址', trigger: 'blur' }],
  supplierPhone: [{ required: true, message: '请输入供应商联系方式', trigger: 'blur' }],
  roleList: [{ required: true, message: '请选择角色', trigger: 'change' }]
}
// 设备点检项
export const RuleDevCheckItemSubmit = {
  checkIndex: [{ required: true, message: '请输入点检序号', trigger: 'blur' }],
  checkType: [{ required: true, message: '请选择检测类型', trigger: 'change' }],
  checkPart: [{ required: true, message: '请输入点检部位', trigger: 'blur' }],
  checkDesc: [{ required: true, message: '请输入点检要求', trigger: 'blur' }],
  checkProject: [{ required: true, message: '请选择开关班', trigger: 'change' }]
}
// 一级保养 二级保养
export const RulEupkeepSubmit = {
  manitName: [{ required: true, message: '请输入保养项目', trigger: 'blur' }],
  manitDesc: [{ required: true, message: '请输入保养项目描述', trigger: 'blur' }],
  manitType: [{ required: true, message: '请选择保养类型', trigger: 'change' }],
  manitMethod: [{ required: true, message: '请选择保养方法', trigger: 'change' }],
  firstManitTime: [{ required: true, message: '请选择首次保养日期', trigger: 'change' }],
  manitWeek: [{ required: true, message: '请选择保养周期', trigger: 'change' }],
  firstMaintAt: [{ required: true, message: '请选择首次保养时间', trigger: 'change' }]
}

// 修改密码
export const RulePasswordSubmit = {
  password: [{ required: true, message: '请输入新密码', trigger: 'blur' }],
  comfirmPassWord: [{ required: true, message: '请输入确认新密码', trigger: 'blur' }]
}

// 工厂模型心中 修改
export const RuleFactorySubmit = computed(() => {
  return {
    code: [{ required: true, message: i18n.$t('form.inputCode'), trigger: 'blur' }],
    name: [{ required: true, message: i18n.$t('form.inputName'), trigger: 'blur' }],
    typeCode: [{ required: true, message: i18n.$t('form.selectType'), trigger: 'change' }]
  }
})
// 工厂模型 工厂，车间，工段，产线，设备
export const RuleFactory = computed(() => {
  return {
    fcCode: [{ required: true, message: i18n.$t('form.selectFactory'), trigger: 'change' }],
    shopCode: [{ required: true, message: i18n.$t('form.selectWorkShop'), trigger: 'change' }],
    vsmCode: [{ required: true, message: i18n.$t('form.selectSection'), trigger: 'change' }],
    lineCode: [{ required: true, message: i18n.$t('form.selectLine'), trigger: 'change' }],
    devCode: [{ required: true, message: i18n.$t('form.selectEquipment'), trigger: 'change' }]
  }
})
// 一级保养
export const RuleEupkeep = {
  ...RuleFactory.value
}
// 备件
export const RuleSpare = {
  spCode: [{ required: true, message: '请输入备件编号', trigger: 'blur' }],
  spName: [{ required: true, message: '请输入备件名称', trigger: 'blur' }],
  model: [{ required: true, message: '请输入备件型号', trigger: 'blur' }]
}
// 安灯类型
export const RuleAndonTypeSubmit = {
  name: [{ required: true, message: '请输入安灯类型名称', trigger: 'blur' }]
}
// 安灯触发条件
export const RuleAndonConditionSubmit = {
  name: [{ required: true, message: '请输入安灯触发条件名称', trigger: 'blur' }]
}
// 安灯类型内容
export const RuleAndonContentSubmit = {
  name: [{ required: true, message: '请输入安灯类型内容名称', trigger: 'blur' }]
}

// 安灯触发条件规则
export const andonRuleSubmit = {
  level: [{ required: true, message: '请选择安灯级别', trigger: 'blur' }],
  notifyRoles: [{ required: true, message: '请选择安灯通知对象', trigger: 'blur' }],
  respTimeLimit: [{ required: true, message: '请输入安灯响应时效', trigger: 'blur' }],
  solveTimeLimit: [{ required: true, message: '请输入安灯处理时效', trigger: 'blur' }]
}

// OEE 数据记录  停机记录
export const RuleStoplogDialog = computed(() => {
  return {
    cellType: [{ required: true, message: i18n.$t('form.selectProductObjectType'), trigger: 'change' }],

    // 工厂维度
    fcCode: [{ required: true, message: i18n.$t('form.selectFactory'), trigger: 'change' }],
    shopCode: [{ required: true, message: i18n.$t('form.selectWorkShop'), trigger: 'change' }],
    vsmCode: [{ required: true, message: i18n.$t('form.selectSection'), trigger: 'change' }],
    lineCode: [{ required: true, message: i18n.$t('form.selectLine'), trigger: 'change' }],

    // 生产对象
    cellCode: [{ required: true, message: i18n.$t('form.inputProductObjectNum'), trigger: 'blur' }],
    cellName: [{ required: true, message: i18n.$t('form.inputProductObjectName'), trigger: 'blur' }],

    stopClassId: [{ required: true, message: i18n.$t('form.selectShutdownType'), trigger: 'change' }],
    stopReasonId: [{ required: true, message: i18n.$t('form.selectShutdownReason'), trigger: 'change' }],
    isPlanned: [{ required: true, message: i18n.$t('form.selectIsPlanned'), trigger: 'change' }],
    startAt: [{ required: true, message: i18n.$t('form.selectStartTime'), trigger: 'change' }],
    endAt: [{ required: true, message: i18n.$t('form.selectEndTime'), trigger: 'change' }],
    stopDesc: [{ required: true, message: i18n.$t('form.inputShutdownDesc'), trigger: 'blur' }]
  }
})

// OEE 数据记录  产量记录
export const RulePrologDialog = computed(() => {
  return {
    cellType: [{ required: true, message: i18n.$t('form.selectProductObjectType'), trigger: 'change' }],

    // 工厂维度
    fcCode: [{ required: true, message: i18n.$t('form.selectFactory'), trigger: 'change' }],
    shopCode: [{ required: true, message: i18n.$t('form.selectWorkShop'), trigger: 'change' }],
    vsmCode: [{ required: true, message: i18n.$t('form.selectSection'), trigger: 'change' }],
    lineCode: [{ required: true, message: i18n.$t('form.selectLine'), trigger: 'change' }],

    // 生产对象
    cellCode: [{ required: true, message: i18n.$t('form.inputProductObjectNum'), trigger: 'blur' }],
    cellName: [{ required: true, message: i18n.$t('form.inputProductObjectName'), trigger: 'blur' }],

    proCode: [{ required: true, message: i18n.$t('form.inputProductNum'), trigger: 'blur' }],
    number: [{ required: true, message: i18n.$t('form.inputNumber'), trigger: 'blur' }],
    toCt: [{ required: true, message: i18n.$t('form.inputActualBeat'), trigger: 'blur' }],
    proOutTime: [{ required: true, message: i18n.$t('form.selectOutputTime'), trigger: 'change' }],
    isOk: [{ required: true, message: i18n.$t('form.selectQualified'), trigger: 'change' }],
    isPass: [{ required: true, message: i18n.$t('form.selectOnePass'), trigger: 'change' }]
  }
})

// 额定节拍
export const RuleBeatSubmit = computed(() => {
  return {
    ...RuleFactory.value,
    proCode: [{ required: true, message: i18n.$t('form.inputProductNum'), trigger: 'blur' }],
    proName: [{ required: true, message: i18n.$t('form.inputProductName'), trigger: 'blur' }],
    cellType: [{ required: true, message: i18n.$t('form.selectProductObjectType'), trigger: 'change' }],
    cellCode: [{ required: true, message: i18n.$t('form.inputProductObjectNum'), trigger: 'blur' }],
    cellName: [{ required: true, message: i18n.$t('form.inputProductObjectName'), trigger: 'blur' }],
    ratedCt: [{ required: true, message: i18n.$t('form.inputRatedBeat'), trigger: 'blur' }]
  }
})
// 班次
export const RuleShiftSubmit = computed(() => {
  return {
    fcCode: [{ required: true, message: i18n.$t('shift.selectFactory'), trigger: 'change' }],
    shiftCode: [{ required: true, message: i18n.$t('shift.inputCode'), trigger: 'blur' }],
    shiftName: [{ required: true, message: i18n.$t('shift.inputName'), trigger: 'blur' }],
    startTime: [{ required: true, message: i18n.$t('shift.selectStart'), trigger: 'change' }],
    endTime: [{ required: true, message: i18n.$t('shift.selectEnd'), trigger: 'change' }]
  }
})

// 装箱
export const RulePackageSubmit = computed(() => {
  return {
    matCode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    quant: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    pkgCode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

// 库位
export const RuleWarehouseSubmit = {
  binCode: [{ required: true, message: '请输入库位号', trigger: 'blur' }],
  maxVolume: [{ required: false, message: '请输入最大体积', trigger: 'blur' }],
  maxWeight: [{ required: false, message: '请输入最大重量', trigger: 'blur' }]
  // inBlock: [{ required: true, message: '请选择出库禁用标识', trigger: 'change' }],
  // outBlock: [{ required: true, message: '请选择入库禁用标识', trigger: 'change' }]
}
// 工位库位
export const RuleAddStationBinSubmit = {
  levelJson: [{ required: true, message: '请选择工位', trigger: 'change' }],
  binCode: [{ required: true, message: '请选择库位', trigger: 'change' }],
  binType: [{ required: true, message: '请选择库位类型', trigger: 'change' }]
}

// 计划管理
// 计划工单
export const RulePlanOrderSubmit = {
  matCode: [{ required: true, message: '请选择物料编号', trigger: 'change' }],
  matName: [{ required: true, message: '请选择物料名称', trigger: 'change' }],
  routingNo: [{ required: true, message: '请选择工艺路径', trigger: 'change' }],
  planQuant: [{ required: true, message: '请输入计划数量', trigger: 'blur' }],
  orderType: [{ required: false, message: '请选择工单类型', trigger: 'change' }],
  planStartAt: [{ required: true, message: '请选择计划开始时间', trigger: 'blur' }],
  planEndAt: [{ required: true, message: '请选择计划结束时间', trigger: 'blur' }]
}

// 主数据
// 物料
export const RuleMaterialSubmit = computed(() => {
  return {
    matCode: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    matName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    productionClass: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    unit: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    reqQc: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    qcScope: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    materialBatchs: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: ['change', 'blur'] }]
    // storageType: [{ required: true, message: '请选择', trigger: 'change' }]
  }
})
// 工序
export const RuleProcessSubmit = {
  opCode: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
  opName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
  opType: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
  whCode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }]
}

// 检验项目
export const RuleInspectSubmit = computed(() => {
  return {
    reviewItem: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    processStandard: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    resultType: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    reviewValue: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    errorValue: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }]
  }
})

// 库存冻结解冻
export const RuleFreezeSubmit = {
  // devCode: [{ required: true, message: '请输入冻结单号', trigger: 'blur' }],
  // devName: [{ required: true, message: '请输入解冻单号', trigger: 'blur' }],
  notes: [{ required: false, message: '请输入备注', trigger: 'blur' }]
}

// 字典管理
export const RuleDictSubmit = computed(() => {
  return {
    dictCode: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    dictValue: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    dictName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }]
  }
})
//采购单
export const RulePurchaseSubmit = {
  noType: [{ required: true, message: '请选择', trigger: 'change' }],
  supplierCode: [{ required: true, message: '请输入', trigger: 'blur' }],
  supplierName: [{ required: true, message: '请输入', trigger: 'blur' }],
  whCode: [{ required: true, message: '请选择', trigger: 'change' }],
  reqArrDate: [{ required: true, message: '请选择', trigger: 'change' }]
}

// 仓库提交
export const RuleStorageSubmit = {
  whCode: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  descrip: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  plantCode: [{ required: true, message: '请选择工厂', trigger: 'change' }]
}

// 仓库库区提交
export const RuleStorageAreaSubmit = {
  staCode: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  descrip: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  stockRole: [{ required: true, message: '请选择角色', trigger: 'change' }],
  storageType: [{ required: true, message: '请选择存储类型', trigger: 'change' }]
}

// 仓库库位提交
export const RuleStorageBinSubmit = {
  whCode: [{ required: true, message: '请选择仓库', trigger: 'change' }],
  staCode: [{ required: true, message: '请选择库区', trigger: 'change' }],
  sctCode: [{ required: true, message: '请选择分区', trigger: 'change' }],
  binCode: [{ required: true, message: '请输入编号', trigger: 'blur' }],
  binType: [{ required: true, message: '请选择类型', trigger: 'change' }],
  // maxVolume: [{ required: true, message: '请输入最大体积', trigger: 'blur' }],
  // maxWeight: [{ required: true, message: '请输入最大重量', trigger: 'blur' }],
  mixstkControl: [{ required: true, message: '请选择混放规则', trigger: 'change' }],
  stkSuType: [{ required: true, message: '请选择库位存放类型', trigger: 'change' }],
  stkOutType: [{ required: true, message: '请选择拣货方式', trigger: 'change' }],
  maxQuant: [{ required: true, message: '请输入最大数量', trigger: 'blur' }],
  maxBoxCount: [{ required: true, message: '请输入最大箱数量', trigger: 'blur' }],
  maxPalletCount: [{ required: true, message: '请输入最大托盘数量', trigger: 'blur' }]
}
export const RuleOutboundSubmit = {
  bussType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  whCode: [{ required: true, message: '请选择出库仓库', trigger: 'change' }],
  reqTime: [{ required: true, message: '请选择需求时间', trigger: 'change' }],
  planOutTime: [{ required: true, message: '请选择计划发货时间', trigger: 'change' }],
  stkoutProcess: [{ required: true, message: '请选择出库流程配置', trigger: 'blur' }]
}
// 退货出库
export const RuleReturnOutboundSubmit = {
  bussType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  whCode: [{ required: true, message: '请选择仓库', trigger: 'change' }]
}

export const RuleTransferSubmit = {
  whCodeOut: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
  whCodeIn: [{ required: true, message: '请选择收货仓库', trigger: 'change' }],
  transType: [{ required: true, message: '请选择调拨方式', trigger: 'change' }],
  planTimeOut: [{ required: true, message: '请选择计划发货时间', trigger: 'change' }],
  planTimeIn: [{ required: true, message: '请选择计划收货时间', trigger: 'change' }],
  bussTypeIn: [{ required: true, message: '请选择入库业务类型', trigger: 'change' }],
  bussTypeOut: [{ required: true, message: '出库业务类型', trigger: 'change' }]
}
//成品发货新增
export const RuleWmSaleoutOrderSubmit = {
  bussType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  whCode: [{ required: true, message: '请选择仓库', trigger: 'change' }],
  reqArrDate: [{ required: true, message: '请选择预计到货时间', trigger: 'blur' }]
}

//合作伙伴新增
export const RulePartnerSubmit = computed(() => {
  return {
    partnerCode: [{ required: true, message: i18n.$t('partner.inputCode'), trigger: 'blur' }],
    partnerName: [{ required: true, message: i18n.$t('partner.inputName'), trigger: 'blur' }],
    contactName: [{ required: true, message: i18n.$t('partner.inputContact'), trigger: 'blur' }],
    contactPhone: [{ required: true, message: i18n.$t('partner.inputPhone'), trigger: 'blur' }],
    address: [{ required: true, message: i18n.$t('partner.inputAddress'), trigger: 'blur' }]
  }
})

//产品委外
export const RuleWmSOutSource = {
  supplierCode: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  whsCodeOut: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
  whsCodePd: [{ required: true, message: '请选择委外仓库', trigger: 'change' }],
  planOutDate: [{ required: true, message: '请选择计划发货时间', trigger: 'change' }],
  planRtnDate: [{ required: true, message: '请选择计划收货时间', trigger: 'change' }]
}
//道序委外
export const RuleWmSDaoxuOutSource = {
  supplierCode: [{ required: true, message: '请选择供应商', trigger: 'change' }],
  whsCodeOut: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
  whsCodePd: [{ required: true, message: '请选择委外仓库', trigger: 'change' }],
  planOutDate: [{ required: true, message: '请选择计划发货时间', trigger: 'change' }],
  planRtnDate: [{ required: true, message: '请选择计划收货时间', trigger: 'change' }],
  pdNo: [{ required: true, message: '请选择工单', trigger: 'change' }],
  routingNo: [{ required: true, message: '请输入工艺路径编号', trigger: 'input' }],
  outpdOps: [{ required: true, message: '请选择委外工序', trigger: 'change' }],
  outpdOpBef: [{ required: true, message: '请选择委出工序', trigger: 'change' }]
  // outpdOpAft: [{ required: true, message: '请选择委回工序', trigger: 'change' }]
}

//库存调整单新增
export const RuleInventoryAdjustmentSubmit = {
  bussTypeIn: [{ required: true, message: '请选择入库业务类型', trigger: 'change' }],
  bussTypeOut: [{ required: true, message: '请选择出库业务类型', trigger: 'change' }],
  whCode: [{ required: true, message: '请选择出库', trigger: 'change' }]
  // phyinvNo: [{ required: true, message: '请输入上游单号', trigger: 'blur' }],
}

//容器管理新增
export const RuleContainerSubmit = computed(() => {
  return {
    whCode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    pkgMat: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    pkgClass: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }]
  }
})

//交货报告配置
export const RuleDeliverySubmit = {
  // deliveryName: [{ required: true, message: '请输入交货报告名称', trigger: 'change' }],
  matCode: [{ required: true, message: '请选择物料编号', trigger: 'change' }],
  matName: [{ required: true, message: '请输入物料名称', trigger: 'input' }]
}

// 出库策略校验规则

export const RuleOutboundStrategySubmit = {
  stgyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  bussType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  stkOutType: [{ required: true, message: '请选择颗粒度', trigger: 'change' }],
  dealType: [{ required: true, message: '请选择是否强制', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }],
  stageSta: [{ required: true, message: '请选择目的库区', trigger: 'change' }],
  replenishSta: [{ required: true, message: '请选择补货库区', trigger: 'change' }],
  minShelfLife: [{ required: true, message: '请输入效期', trigger: 'blur' }],
  turnoverRule: [{ required: true, message: '请选择周转规则', trigger: 'change' }]
}

// 上架策略校验规则

export const RuleShelfStrategySubmit = {
  stgyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  bussType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  dealType: [{ required: true, message: '请选择是否强制', trigger: 'change' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
}

// 补货策略校验规则
export const replenishmentRuleSubmit = {
  stgyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  stageSta: [{ required: true, message: '请选择目的库区', trigger: 'change' }],
  replenishSta: [{ required: true, message: '请选择补货库区', trigger: 'change' }],
  // minShelfLife: [{ required: true, message: '请输入效期', trigger: 'blur' }],
  turnoverRule: [{ required: true, message: '请选择周转规则', trigger: 'change' }],
  minShelfLife: [{ required: true, message: '请输入效期', trigger: 'blur' }]
}

// 补货策略校验规则2
export const replenishmentRuleSubmitTwo = {
  stgyName: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
  whsCode: [{ required: true, message: '请选择目的仓库', trigger: 'change' }],
  whsCodeOut: [{ required: true, message: '请选择发货仓库', trigger: 'change' }],
  replenishSta: [{ required: true, message: '请选择发货库区', trigger: 'change' }],
  turnoverRule: [{ required: true, message: '请选择周转规则', trigger: 'change' }],
  minShelfLife: [{ required: true, message: '请输入效期', trigger: 'blur' }]
}

//文档管理
export const RuleFileSubmit = {
  docuCode: [{ required: true, message: '请输入文档编号', trigger: 'blur' }],
  docuName: [{ required: true, message: '请输入文档名称', trigger: 'blur' }],
  docuType: [{ required: true, message: '请选择文档类型', trigger: 'change' }]
}
//OPC UA连接
export const RuleIiotSettingSubmitOne = computed(() => {
  return {
    connName: [{ required: true, message: i18n.$t('iiot.inputConnectName'), trigger: 'blur' }],
    connUrl: [{ required: true, message: i18n.$t('iiot.inputConnectUrl'), trigger: 'blur' }]
  }
})

//OPC UA连接
export const RuleIiotSettingSubmitTwo = computed(() => {
  return {
    connName: [{ required: true, message: i18n.$t('iiot.inputConnectName'), trigger: 'blur' }],
    connUrl: [{ required: true, message: i18n.$t('iiot.inputConnectUrl'), trigger: 'blur' }],
    userName: [{ required: true, message: i18n.$t('iiot.inputUserName'), trigger: 'blur' }],
    passward: [{ required: true, message: i18n.$t('iiot.inputPassword'), trigger: 'blur' }]
  }
})

//通知对象(邮件)
export const RuleIiotEmailSubmit = computed(() => {
  return {
    notifyName: [{ required: true, message: '请输入对象名称', trigger: 'blur' }],
    smtpHost: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
    sender: [{ required: true, message: '请输入账号', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
    receiver: [{ required: true, message: '请输入收件人', trigger: 'blur' }],
    ccList: [{ required: true, message: '请输入抄送人', trigger: 'blur' }],
    subject: [{ required: true, message: '请输入主题', trigger: 'blur' }]
  }
})
//通知对象(HTTP)
export const RuleIiotHttpSubmit = computed(() => {
  return {
    notifyName: [{ required: true, message: '请输入对象名称', trigger: 'blur' }],
    url: [{ required: true, message: '请输入服务器地址', trigger: 'blur' }],
    method: [{ required: true, message: '请输入请求方法', trigger: 'blur' }]
    // header: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  }
})
// 库存转换新增
export const inventoryConversionRules = {
  whCode: [{ required: true, message: '请选择仓库', trigger: 'change' }]
  // reason: [{ required: true, message: '请选择仓库转换原因', trigger: 'change' }]
}

//设备连接
export const IiotDeviceInfoRules = computed(() => {
  return {
    devCode: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
    devName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
    devConn: [{ required: true, message: '请输入OPC UA', trigger: 'blur' }]
  }
})

// 采购质量-检验配置
// 检验项目
export const RuleInspectConfig = computed(() => {
  return {
    qcItem: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    resultType: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    toplimit: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    lowerlimit: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    stardard: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }]
  }
})

//叫料配置
export const RuleCallMaterialSetting = computed(() => {
  return {
    lineList: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    // callMatTime: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'change' }],
    pullMode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    // miniRemainTime: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    whsCode: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

//审核周期
export const RuleAuditCircleSubmit = computed(() => {
  return {
    lpRoleId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'change' }],
    lpStartDate: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    lpEndDate: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    factoryId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

// 分层审核 分数
export const RuleAuditScore = computed(() => {
  return {
    lsiStandard: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    lsiScore: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }]
  }
})

// 分层审核 角色
export const RuleAuditRole = computed(() => {
  return {
    roleName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    roleCode: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    roleLevel: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    isActive: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

// 分层审核 班次
export const RuleAuditShift = computed(() => {
  return {
    factoryId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    sfName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    sfCode: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    startTime: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    endTime: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

// 分层审核 机构管理
export const RuleAuditOrganization = computed(() => {
  return {
    shopName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    vsmName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    lineName: [{ required: true, message: i18n.$t('form.pleaseInput'), trigger: 'blur' }],
    isVirtual: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})

// 分层审核 机构管理
export const RuleAuditUserRole = computed(() => {
  return {
    roleId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    factoryId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    shopId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }],
    vsmId: [{ required: true, message: i18n.$t('form.pleaseSelect'), trigger: 'blur' }]
  }
})


// BPMN 模型新增修改
export const RuleBpmnSubmit = {
  key: [{ required: true, message: '请输入编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
}