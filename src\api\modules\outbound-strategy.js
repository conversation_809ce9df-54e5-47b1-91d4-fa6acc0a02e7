import request from '@/common/request.js'

//出库策略分页列表
export function getOutboundStrategyList(params, data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvout/pages',
    method: 'POST',
    params,
    data
  })
}
// 出库策略新增
export function addOutboundStrategy(data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvout/save',
    method: 'POST',
    data
  })
}

// 出库策略编辑
export function editOutboundStrategy(data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvout/update',
    method: 'PUT',
    data
  })
}

// 出库策略删除
export function deleteOutboundStrategy(ids) {
  return request({
    url: `/mes_api/v1/wmStrategyInvout/${ids}/delete`,
    method: 'DELETE'
  })
}

//入库策略分页查询
export function fetchInStrategyList(params, data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvInto/pages',
    method: 'POST',
    params,
    data
  })
}

//入库策略新增
export function addInboundStrategy(data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvInto/save',
    method: 'POST',
    data
  })
}

//入库策略编辑
export function editInboundStrategy(data) {
  return request({
    url: '/mes_api/v1/wmStrategyInvInto/update',
    method: 'PUT',
    data
  })
}

//入库策略删除
export function deleteInboundStrategy(ids) {
  return request({
    url: `/mes_api/v1/wmStrategyInvInto/${ids}/delete`,
    method: 'DELETE'
  })
}
