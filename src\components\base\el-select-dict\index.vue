<script setup>
import { defineProps, defineEmits, ref, computed, watch } from 'vue'
// eslint-disable-next-line no-unused-vars
import { Loading, ArrowDown } from '@element-plus/icons-vue'
import { GetDictByCode } from '@/api/modules/mes'

const emit = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  modelValue: {
    type: String,
    required: true,
    default: ''
  },
  dictType: {
    type: String,
    required: true,
    default: ''
  },
  filter: {
    type: Function,
    default: null
  }
})
const selfValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
    emit('change', val)
  }
})
const options = ref([])
const filterOptions = computed(() => {
  return props.filter ? props.filter(options.value) : options.value
})
const isload = ref(false)
const loading = ref(false)
const getOptionsByDictType = async () => {
  // console.log(isload.value, loading.value, props.dictType, 111)
  try {
    if (isload.value) return
    if (loading.value) return
    if (!props.dictType) return
    loading.value = true
    options.value = await GetDictByCode(props.dictType)
    console.log(options.value, 222)
    isload.value = true
  } catch (e) {
    isload.value = false
  } finally {
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
}
const jsonProps = computed(() => JSON.parse(JSON.stringify(props)))
watch(
  () => jsonProps.value,
  (nval) => {
    if (nval && nval.modelValue) {
      getOptionsByDictType()
    }
  },
  { deep: true, immediate: true }
)
</script>
<template>
  <el-select
    v-model="selfValue"
    class="el-select-dict"
    :class="{ 'is-loading': loading }"
    :suffix-icon="loading ? Loading : ArrowDown"
    v-bind="$attrs"
    :placeholder="$t('qing-xuan-ze')"
    @visible-change="getOptionsByDictType"
    @click="getOptionsByDictType"
  >
    <el-option v-for="item in filterOptions" :key="item.dictId" :value="item.dictValue" :label="item.dictName" />
  </el-select>
</template>
<style lang="scss">
.el-select-dict {
  min-width: 130px;
}
.el-select-dict.is-loading .el-icon svg {
  color: $primary !important;
  animation: rotate 1s linear infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
</style>
