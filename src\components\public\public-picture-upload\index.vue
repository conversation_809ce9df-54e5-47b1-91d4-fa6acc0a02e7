<template>
  <div class="public-picture-upload">
    <el-image v-if="modelValue" style="width: 100%; height: 100%" :src="modelValue" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2" :preview-src-list="[modelValue]" :initial-index="4" fit="cover" />
    <div v-else class="upload-add" @click="upload()">
      <span v-if="props.disabled">No Image</span>
      <svg-icon name="add" v-else />
    </div>
    <el-popconfirm title="是否确认删除?" @confirm="modelValue = ''">
      <template #reference>
        <svg-icon v-if="modelValue && !props.disabled" class="delete" name="delete" size="30" />
      </template>
    </el-popconfirm>
  </div>
</template>
<script setup>
import { computed } from 'vue'
import { uploadFileEasy } from '@/api/modules/device'
import { ElMessage } from 'element-plus'

const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    emits('change', val)
  }
})

function upload() {
  if (props.disabled) return
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e) => {
    if (e.target.files.length === 0) return
    const file = e.target.files[0]
    console.log(file)
    uploadFileEasy(file).then((res) => {
      modelValue.value = res[0].filePath
      ElMessage.success('上传成功')
    })
    // clear
    e.target.value = ''
    input.remove()
  }
  input.click()
}
</script>
<style scoped lang="scss">
.public-picture-upload {
  width: 100%;
  height: 100%;
  position: relative;
  &:hover .delete {
    display: block;
  }
}

.delete {
  display: none;
  color: red;
  position: absolute;
  top: 0px;
  right: 5px;
  cursor: pointer;
  padding: 3px;
  background-color: #ff00002e;
  border-radius: 4px;
  &:hover {
    background-color: #ff000063;
  }
}

.upload-add {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #c0c4cc;
  font-size: 20px;
  font-weight: 500;
  border: 1px dashed #c0c4cc;
  border-radius: 5px;
  cursor: pointer;
  svg {
    width: 50px;
    height: 50px;
  }
}

.el-image {
  background: #eee;
  border-radius: 6px;
}
</style>
