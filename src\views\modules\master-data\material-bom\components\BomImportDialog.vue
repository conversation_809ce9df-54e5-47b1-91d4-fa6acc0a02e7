<template>
    <el-dialog title="导入BOM数据" v-model="dialogVisible" @opened="handleDialogOpened" width="500px"
        :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close>
        <div class="import-dialog">
            <div class="tips">
                <p>提示：</p>
                <p>1. 请先下载模板，按照模板格式填写数据</p>
                <p>2. 仅支持xls、xlsx格式的文件</p>
                <p>3. 文件大小不能超过5MB</p>
            </div>

            <el-form :model="formData" label-width="100px" class="import-form">
                <el-form-item label="Sheet名称" required>
                    <el-input v-model="formData.sheetName" placeholder="请输入Sheet名称" />
                </el-form-item>
            </el-form>

            <div class="upload-area">
                <el-upload ref="uploadRef" class="upload-excel" :action="uploadAction" :show-file-list="true" :limit="1"
                    :before-upload="beforeUpload" :on-error="handleUploadError" :http-request="customUpload"
                    accept=".xls,.xlsx">
                    <el-button type="primary" :loading="isLoading">
                        {{ isLoading ? '上传中...' : '选择文件' }}
                    </el-button>
                </el-upload>
            </div>

            <!-- 导入预览和确认区域 -->
            <div v-if="previewData" class="preview-area">
                <div class="preview-info">
                    <p><strong>文件选择成功！</strong></p>
                    <p>文件名：{{ previewData.fileName }}</p>
                    <p>文件大小：{{ previewData.fileSize }}</p>
                    <p style="color: #606266;">点击"确认导入"开始处理文件</p>
                    <p v-if="previewData.errors && previewData.errors.length > 0" style="color: #F56C6C;">
                        警告：{{ previewData.errors.join('，') }}
                    </p>
                </div>
                <div class="preview-actions">
                    <el-button @click="cancelImport">取消</el-button>
                    <el-button type="primary" @click="confirmImport" :loading="isConfirming">
                        {{ isConfirming ? '导入中...' : '确认导入' }}
                    </el-button>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 导入失败详情弹窗 -->
    <el-dialog title="导入失败详情" v-model="errorDialogVisible" width="600px">
        <div class="error-dialog-content">
            <div class="error-summary">
                <p>导入失败摘要：{{ errorSummary }}</p>
            </div>
            <div class="error-details" v-if="errorDetails.length > 0">
                <h4>详细错误信息：</h4>
                <el-table :data="errorDetails" style="width: 100%">
                    <el-table-column prop="bomNo" label="BOM编号" />
                    <el-table-column prop="matCode" label="物料编码" />
                    <el-table-column prop="error" label="错误信息" />
                </el-table>
            </div>
        </div>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="errorDialogVisible = false">关闭</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { importBomByExcel } from '@/api/modules/mes'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    loading: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:visible', 'update:loading', 'success', 'close'])

// 本地状态
const dialogVisible = ref(props.visible)
const isLoading = ref(props.loading)
const uploadRef = ref(null)
const uploadAction = ref('') // 不使用action，使用自定义上传
const formData = reactive({
    sheetName: ''
})

// 错误弹窗相关状态
const errorDialogVisible = ref(false)
const errorSummary = ref('')
const errorDetails = ref([])

// 预览确认相关状态
const previewData = ref(null)
const isConfirming = ref(false)
const currentFile = ref(null)

// 监听props变化
watch(() => props.visible, (val) => {
    dialogVisible.value = val
})

watch(() => props.loading, (val) => {
    isLoading.value = val
})

// 监听本地状态变化
watch(dialogVisible, (val) => {
    emit('update:visible', val)
})

watch(isLoading, (val) => {
    emit('update:loading', val)
})

// 对话框打开时的处理
const handleDialogOpened = () => {
    // 重置表单数据
    formData.sheetName = ''
    previewData.value = null
    currentFile.value = null
}

// 上传前验证
const beforeUpload = (file) => {
    if (!formData.sheetName) {
        ElMessage.error('请输入Sheet名称')
        return false
    }

    const isExcel = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ].includes(file.type)

    if (!isExcel) {
        ElMessage.error('请上传Excel文件')
        return false
    }

    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
        ElMessage.error('文件大小不能超过5MB')
        return false
    }

    isLoading.value = true
    return true
}

// 自定义上传方法 - 仅用于文件选择，不实际上传
const customUpload = (options) => {
    const { file } = options
    currentFile.value = file

    // 文件选择成功，直接显示预览区域，不调用API
    handleFileSelected(file)
}

// 处理文件选择
const handleFileSelected = (file) => {
    isLoading.value = false

    // 模拟预览数据，显示文件信息
    previewData.value = {
        fileName: file.name,
        fileSize: (file.size / 1024 / 1024).toFixed(2) + 'MB',
        totalRows: '待确认', // 实际行数需要后端解析
        errors: []
    }
}

// 确认导入
const confirmImport = async () => {
    if (!currentFile.value) {
        ElMessage.error('请先选择文件')
        return
    }

    isConfirming.value = true

    try {
        // 创建 FormData进行实际导入
        const formDataToSend = new FormData()
        formDataToSend.append('file', currentFile.value)
        formDataToSend.append('sheetName', formData.sheetName)

        // 调用API进行实际导入
        const response = await importBomByExcel(formDataToSend)
        console.log(response);

        handleImportSuccess(response)
    } catch (error) {
        handleImportError(error)
    }
}

// 取消导入
const cancelImport = () => {
    previewData.value = null
    currentFile.value = null
    if (uploadRef.value) {
        uploadRef.value.clearFiles()
    }
}

// 导入成功处理
const handleImportSuccess = (response) => {
    isConfirming.value = false

    if (response && response.code === '200' && response.data) {
        ElMessage.success('导入成功')
        emit('success')
        handleClose()
    } else {
        ElMessage.error('导入失败')
    }
}

// 导入失败处理
const handleImportError = (error) => {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
    isConfirming.value = false
}

// 上传失败处理
const handleUploadError = (error) => {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
    isLoading.value = false
}

// 显示错误详情弹窗
const showErrorDialog = (errors) => {
    errorSummary.value = `共 ${errors.length} 条记录导入失败`
    errorDetails.value = errors.map(item => ({
        bomNo: item.bomNo || '',
        matCode: item.matCode || '',
        error: item.error || item.message || '未知错误'
    }))

    // 显示错误弹窗
    errorDialogVisible.value = true
}

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false
    emit('close')
}

// 重置表单
const reset = () => {
    formData.sheetName = ''
    previewData.value = null
    currentFile.value = null
    if (uploadRef.value) {
        uploadRef.value.clearFiles()
    }
}

// 暴露方法给父组件
defineExpose({
    reset,
    showErrorDialog
})
</script>

<style scoped lang="scss">
.import-dialog {
    padding: 0 20px;

    .tips {
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
        line-height: 1.8;
    }

    .import-form {
        margin-bottom: 20px;
    }

    .upload-area {
        display: flex;
        justify-content: center;
        padding: 20px 0;

        .upload-excel {
            width: 100%;
            text-align: center;
        }
    }
}

.dialog-footer {
    text-align: right;
}

/* 预览区域样式 */
.preview-area {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f8f9fa;

    .preview-info {
        margin-bottom: 15px;

        p {
            margin: 5px 0;
            font-size: 14px;
        }
    }

    .preview-actions {
        text-align: right;

        .el-button+.el-button {
            margin-left: 10px;
        }
    }
}

:deep(.el-upload-dragger) {
    width: 100%;
}

/* 错误弹窗样式 */
.error-dialog-content {
    padding: 0 10px;

    .error-summary {
        margin-bottom: 15px;
        padding: 10px;
        background-color: #fef0f0;
        border-radius: 4px;
        color: #f56c6c;
    }

    .error-details {
        h4 {
            margin-bottom: 10px;
            font-weight: 500;
        }
    }
}
</style>