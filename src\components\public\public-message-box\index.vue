<script setup>
import { ref, reactive } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'
defineProps({
  title: {
    type: String,
    default: ''
  },
  onConfirm: {
    type: Function,
    default: null
  },
  btns: {
    type: Array,
    default: () => []
  }
})

// 对话框配置项
const dialogVisible = ref(true)
const dialogOptions = reactive({
  title: '提示',
  width: '25%',
  closeOnClickModal: false,
  alignCenter: true
})

// 关闭
const close = () => {
  dialogVisible.value = false
}
</script>

<template>
  <div class="public-message-box">
    <el-dialog v-model="dialogVisible" v-bind="dialogOptions">
      <div class="public-message-box-content">
        <div class="icon-card">
          <el-icon><WarningFilled color="#e2973e" /></el-icon>
          <span>{{ title }}</span>
        </div>
      </div>
      <template #footer>
        <span>
          <el-button v-for="(but, index) in btns" :key="index" :type="but.type" @click="but.onclick(close)">
            {{ but.text }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.public-message-box {
  :deep(.el-dialog__body) {
    padding: 0 20px;
    box-sizing: border-box;
  }
  &-content {
    margin: 20px 0;
    .icon-card {
      display: flex;
      align-items: center;
      font-size: 18px;
      .el-icon {
        margin-right: 10px;
      }
      span {
        font-weight: 700;
      }
    }
  }
}
</style>
