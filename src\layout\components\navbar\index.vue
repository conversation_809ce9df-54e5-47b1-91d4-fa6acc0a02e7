<script setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { asynConfirm } from '@/common/utils'
import router from '@/router'
import { useLangueStore } from '@/stores/langue'
import { useI18n } from 'vue-i18n'
const userData = useUserStore()
const route = useRoute()
const { proxy } = getCurrentInstance()

const langueStore = useLangueStore()
const currentLangue = computed({
  get: () => {
    return langueStore.langue === 'zh' ? 'zh' : 'en'
  },
  set: (val) => {
    return val
  }
})
const { locale } = useI18n()
const changeLangue = (e) => {
  locale.value = e
  langueStore.setLangue(e)
}

const langueList = [
  {
    t: 'base.zh',
    val: 'zh'
  },
  {
    t: 'base.en',
    val: 'en'
  }
]
/*
 *   返回上一页
 *   @param {Number} step 返回的步数
 *   @return {void}
 */
const goBack = () => {
  router.back()
}
/*
 *   是否展示返回按钮
 *   @return {Boolean}
 *   @description 通过路由配置meta.back来控制是否展示返回按钮
 */
const IsShowBack = computed(() => {
  return route.meta.back
})

/*
 *   页面名称
 *   @return {String}
 *   @description 展示路由配置meta中名称
 */
const PageTitle = computed(() => {
  return proxy.$t(route.meta.title)
})

/*
 *   用户名称
 *   @return {String}
 */
const UserName = computed(() => {
  return userData.username
})

/*
 *   打开个人中心
 *   @param {data}
 *   @return {viod}
 */
const goPerson = () => {
  router.push({
    path: '/user-center/person'
  })
}

/**
 * 返回首页
 * @return {void}
 */
const goHome = () => {
  router.push('/')
}

/*
 *   退出登录
 */
const goLogout = async () => {
  if (!(await asynConfirm('确定退出登录吗？'))) return
  useUserStore().logout()
  langueStore.resetLangue()
  router.push({
    path: '/login'
  })
}
</script>
<template>
  <div class="navbar">
    <div class="navbar-back-icon" :class="{ show: IsShowBack }" @click="goBack()">
      <SvgIcon name="back" size="20" hover class="primary" />
    </div>
    <span class="navbar-page-title">{{ PageTitle }}</span>
    <div class="navbar-body">
      <public-global-search />
    </div>
    <div class="navbar-operate">
      <div class="navbar-operate-item">
        <span class="navbar-operate-item-title" style="flex-shrink: 0">{{ $t('base.langue') }}:</span>
        <el-select v-model="currentLangue" @change="changeLangue" style="width: 80px; margin-left: 10px">
          <el-option v-for="item in langueList" :key="item.val" :label="$t(item.t)" :value="item.val" />
        </el-select>
      </div>
      <div class="navbar-operate-item">
        <SvgIcon name="navbar-0004" size="30" class="primary" />
        <!-- popover -->
        <el-popover trigger="click" placement="top" :width="160">
          <ul class="download-ul">
            <li>
              <a class="download-a" href="http://47.100.31.137:8090/static/uprint_win_x64.exe" download="uprint_win_x64.exe">打印程序下载</a>
            </li>
            <!-- qrcode -->
            <el-popover trigger="hover" placement="right" :width="170">
              <div style="text-align: center">
                <img src="@/assets/imgs/wms-app-download.png" alt="wms" style="width: 145px; height: 145px" />
              </div>
              <template #reference>
                <li>
                  <a class="download-a" href="http://47.100.31.137:8090/static/wms.apk" download="wms.apk">WMS APP下载</a>
                </li>
              </template>
            </el-popover>
          </ul>
          <template #reference>
            <span class="navbar-operate-item-title">{{ $t('base.resourceDownload') }}</span>
          </template>
        </el-popover>
      </div>
      <div class="navbar-operate-item" @click="goPerson()">
        <SvgIcon name="navbar-0001" size="30" class="primary" />
        <span class="navbar-operate-item-title">{{ UserName }}</span>
      </div>
      <div class="navbar-operate-item" @click="goHome()">
        <SvgIcon name="navbar-0003" size="30" class="primary" />
        <span class="navbar-operate-item-title">{{ $t('base.backHome') }}</span>
      </div>
      <div class="navbar-operate-item" @click="goLogout()">
        <SvgIcon name="navbar-0002" size="30" class="primary" />
        <span class="navbar-operate-item-title">{{ $t('base.exit') }}</span>
      </div>
    </div>
  </div>
</template>
<style scoped lang="scss">
.navbar {
  @include box;
  @include flex-row;
  @include flex-center;
  width: calc(100% - $base-margin * 2);
  padding: 10px 13px;
  box-sizing: border-box;
  flex-shrink: 0;
}
.navbar-page-title {
  font-size: 16px;
  font-weight: 800;
  flex-shrink: 0;
}
.navbar-body {
  flex: 1;
  text-align: left;
  margin-left: 20px;
}
.navbar-operate {
  @include flex-row;
  @include flex-center;
  flex-shrink: 0;
  margin-left: auto;
  .navbar-operate-item {
    @include flex-row;
    @include flex-center;
    margin-left: 20px;
    transform: all 300ms;
    cursor: pointer;
    .navbar-operate-item-title {
      margin-left: 5px;
      font-size: 14px;
      outline: none;
    }
    &:hover {
      transform: scale(1.05);
    }
  }
}

// back
.navbar-back-icon {
  color: $primary;
  width: 0px;
  height: 100%;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s;
  overflow: hidden;
  cursor: pointer;

  &.show {
    width: 20px;
    margin-right: 5px;
    margin-left: 5px;
  }
  &:hover {
    transform: translateX(-5px);
  }
}

.download-ul {
  li {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    &:first-child {
      padding-top: 0;
    }
    &:hover {
      color: $primary;
    }
  }
}

.download-a {
  color: #333;
  text-decoration: none;
  &:hover {
    color: $primary;
    text-decoration: underline;
  }
}
</style>
