import request from '@/common/request.js'

/*
 *  获取备件数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetSparePart = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/mdSparePart/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
/*
 *  新增备件
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddSparePart = (data) => {
  return request({
    url: '/tpm_api/v1/mdSparePart/save',
    method: 'POST',
    data: data
  })
}
/*
 *  编辑备件
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateSparePart = (data) => {
  return request({
    url: '/tpm_api/v1/mdSparePart/update',
    method: 'PUT',
    data: data
  })
}
/*
 *  删除设备数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelSparePart = (ids) => {
  return request({
    url: `/tpm_api/v1/mdSparePart/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  获取备件申领记录列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetfmOrderSpart = (params = {}, data = {}) => {
  return request({
    url: '/tpm_api/v1/fmOrderSpart/pages',
    method: 'POST',
    params: params,
    data: data
  })
}
