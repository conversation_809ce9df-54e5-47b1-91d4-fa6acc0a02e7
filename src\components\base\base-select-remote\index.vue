<script setup>
// eslint-disable-next-line no-unused-vars
import { computed, ref, watch } from 'vue'
import { Loading, ArrowDown } from '@element-plus/icons-vue'
import uniqBy from 'lodash/uniqBy'
import debounce from 'lodash/debounce'
import { ifNullObject } from '@/common/utils'

const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  // 数据
  modelValue: {
    type: [String, Array, null],
    required: true,
    default: '' || [] || null
  },
  // 请求
  request: {
    type: Function,
    required: true,
    default: () => null
  },
  // 主要请求参数
  paramsKey: {
    type: String,
    required: true,
    default: ''
  },
  // 其他请求参数
  otherParams: {
    type: Object,
    default: () => ({})
  },
  // 选择项 Value
  optionValue: {
    type: String,
    required: true,
    default: ''
  },
  // 选择项 Label
  optionLabel: {
    type: String,
    required: true,
    default: ''
  },
  // 定义 el-option 模板
  optionTemplate: {
    type: Function,
    default: null
  },
  // 默认查询
  isDefaultQuery: {
    type: Boolean,
    default: true
  }
})

const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    /**
     * @param { String | Array | Number }: val
     * @param { Array }: options.value
     * @param { Object }: options.value
     */
    emits('change', val, options.value, options.value.find((item) => item[props.optionValue] === val) || null)
  }
})

// el-option 配置项
const selectOptions = computed(() => ({
  filterable: true,
  remote: true,
  reserveKeyword: true,
  clearable: true,
  remoteShowSuffix: true
}))

// loading 加载
const loading = ref(false)
const isload = ref(false)
// el-option 数据
const options = ref([])

// 远程搜索
const remoteMethod = (value) => {
  if (value) {
    callback(value)
  }
}
const callback = debounce(
  function (code) {
    getOptionsByRequest(code)
  },
  100,
  { trailing: true }
)

// 监听 el-select 变化
const visibleChange = (value) => {
  if (value) {
    getOptionsByRequest(props.modelValue)
  }
}

// 请求
const getOptionsByRequest = async (value = '') => {
  try {
    if (typeof props.request === 'function') {
      if (!props.paramsKey) return
      // if (loading.value) return
      loading.value = true
      let params = {
        [props.paramsKey]: value
      }
      if (ifNullObject(props.otherParams)) {
        params = { ...params, ...props.otherParams }
      }
      const result = await props.request({ size: 50 }, params)
      const list = result.list.map((item) => {
        return {
          ...item,
          label: item[props.optionLabel],
          value: item[props.optionValue]
        }
      })
      options.value = uniqBy(list, (item) => item[props.optionValue])
      isload.value = true
    }
  } catch (e) {
    console.log(e)
    options.value = []
  } finally {
    setTimeout(() => {
      loading.value = false
    }, 500)
  }
}
const clearFn = () => {
  modelValue.value = ''
}

watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      if (shouldQuery(val)) {
        getOptionsByRequest(val)
      }
    }
  },
  { immediate: props.isDefaultQuery }
)

function shouldQuery(val) {
  if (!isload.value) return true
  if (options.value.length === 0) return true
  if (!options.value.find((item) => item[props.optionValue] === val)) return true
  return false
}
</script>

<template>
  <el-select
    v-model="modelValue"
    class="el-select-remote"
    :class="{ 'is-loading': loading }"
    :loading="loading"
    v-bind="($attrs, selectOptions)"
    :suffix-icon="loading ? Loading : ArrowDown"
    :placeholder="$t('form.pleaseInputKeyWord')"
    :remote-method="remoteMethod"
    @clear="clearFn"
    @visible-change="visibleChange"
  >
    <template v-if="$slots.default">
      <slot name="default" :options="options" />
    </template>
    <template v-else>
      <el-option v-for="(item, index) in options" :key="index" :value="item.value" :label="item.label">
        <span v-if="optionTemplate" v-html="optionTemplate(item)"></span>
      </el-option>
    </template>
  </el-select>
</template>

<style lang="scss" scoped>
.el-select-remote.is-loading .el-icon svg {
  color: $primary !important;
  animation: rotate 1s linear infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
</style>
