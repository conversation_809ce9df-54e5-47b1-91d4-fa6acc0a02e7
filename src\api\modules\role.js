import request from '@/common/request.js'

/*
 *  获取角色下拉列表
 *  @param { void }
 *  @returns { Promise }
 */
export const GetRoleOptions = () => {
  return request({
    url: '/api/v1/roles/options',
    method: 'GET'
  })
}

/*
 *  删除用户角色  删除该用户的某些角色
 *  @param { userId: Int, roleIds: String,String,String }
 *  @returns { Promise }
 */
export const DelUserRoles = (userId, roleIds) => {
  return request({
    url: `/api/v1/user/${userId}/deleteRoles`,
    method: 'DELETE',
    params: {
      roleIds
    }
  })
}

/*
 *  新增角色
 *  @param { data }
 *  @returns { Promise }
 */
export const AddRole = (data) => {
  return request({
    url: `/api/v1/roles/save`,
    method: 'POST',
    data
  })
}

/*
 *  角色详情
 *  @param { id }
 *  @returns { Promise }
 */
export const GetRoleDetailById = (id) => {
  return request({
    url: `/api/v1/roles/${id}/info`,
    method: 'GET'
  })
}

/*
 *  修改角色
 *  @param { data }
 *  @returns { Promise }
 */
export const EditRole = (data) => {
  return request({
    url: `/api/v1/roles/${data.roleId}/update`,
    method: 'PUT',
    data
  })
}

/*
 *  删除角色
 *  @param { ids }
 *  @returns { Promise }
 */
export const DelRoleByIds = (ids) => {
  return request({
    url: `/api/v1/roles/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  获取角色的权限
 *  @param { id }
 *  @returns { Promise }
 */
export const GetRoleAuth = (roleId) => {
  return request({
    url: `/api/v1/roles/${roleId}/menuIds`,
    method: 'GET'
  })
}

/*
 *  修改角色的权限
 *  @param { roleId, authIds }
 *  @returns { Promise }
 */
export const UpdateRoleAuth = (roleId, authIds) => {
  return request({
    url: `/api/v1/roles/${roleId}/menus`,
    method: 'PUT',
    params: {
      menuIds: authIds
    }
  })
}
