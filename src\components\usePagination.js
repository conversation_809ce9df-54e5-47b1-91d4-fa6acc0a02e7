import { reactive } from 'vue'

function usePagination() {
  const pageInfo = reactive({
    page: 1,
    size: 10,
    total: 0
  })

  function resetPageIndex() {
    pageInfo.page = 1
  }

  function doLocalPage(showList, originList) {
    showList.value = originList.value.slice((pageInfo.page - 1) * pageInfo.size, pageInfo.page * pageInfo.size)
    pageInfo.total = originList.value.length
  }

  return {
    pageInfo,
    resetPageIndex,
    doLocalPage
  }
}

export default usePagination
