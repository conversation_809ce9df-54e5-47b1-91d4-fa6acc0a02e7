<template>
  <div class="base-bpmn-controler" v-if="bpmn && bpmn.status !== 'unknown'">
    <!-- status:nothing 无流程 -->
    <span v-if="bpmn.status === 'nothing'">未配置流程</span>

    <!-- status:unopened 已配置流程，但未开启 -->
    <template v-else-if="bpmn.status === 'unopened'">
      <!-- &nbsp;<svg-icon name="process" size="16" />&nbsp; -->
      <el-button type="primary" v-for="process in bpmn.processList" :key="process.modelId" @click="handleStartProcess(process)">发起&gt;&gt;{{ process.name }}</el-button>
    </template>

    <!-- status:ongoing 流程进行中 -->
    <template v-else-if="bpmn.status === 'ongoing'">
      <!-- 进行中, 当前账号没有需要操作的项 -->
    </template>

    <!-- status:need_approve 流程进行中，需要当前用户审批 -->
    <template v-else-if="bpmn.status === 'need_approve'">
      <template v-if="bpmn.isClaim === false">
        <!-- 未拾取任务 -->
        <el-button type="primary" @click="handleClaimTask()">拾取任务</el-button>
      </template>
      <template v-else>
        <!-- 已拾取 -->
        <el-button type="primary" @click="handleAgree()">同意</el-button>
        <el-button type="primary" @click="handleReject()">驳回</el-button>
      </template>
    </template>

    <!-- status:end 流程结束 -->
    <span v-else-if="bpmn.status === 'end'">已结束</span>
    <el-button type="text" @click="handleApproveRecord()">审批记录</el-button>

    <!-- 部署历史 -->
    <approve-record ref="approveRecordRef" />
  </div>
</template>
<script setup>
import { inject, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { ApiActivitiStart, ApiClaimTask, ApiCompleteTask, ApiBackTask } from '@/api/modules/bpmn'
import { asynConfirm } from '@/common/utils'
import { ElMessage, ElMessageBox } from 'element-plus'
import ApproveRecord from './approve-record.vue'

const approveRecordRef = ref(null)
const userData = useUserStore()
const bpmn = inject('bpmn')

if (!bpmn) {
  throw new Error('<base-bpmn-controler> must be used inside <base-bpmn>')
}

// 发起流程
async function handleStartProcess(process) {
  if (await asynConfirm(`是否确定要发起【${process.name}】？`)) {
    const code = await ApiActivitiStart({
      modelKey: process.modelKey,
      docCode: bpmn.value.docCode,
      pageCode: bpmn.value.pageCode,
      userNumber: userData.username
    })
    ElMessage.success(`发起成功，流程编号：${code}`)
    bpmn.value.reload()
  }
}

// 拾取任务
async function handleClaimTask() {
  if (await asynConfirm(`是否确定要拾取任务？`)) {
    await ApiClaimTask({
      taskId: bpmn.value.taskId,
      accountName: userData.username
    })
    ElMessage.success(`拾取成功`)
    bpmn.value.reload()
  }
}

// 同意
async function handleAgree() {
  ElMessageBox.prompt('填写意见', '同意', {
    confirmButtonText: '同意',
    cancelButtonText: '取消',
    inputType: 'textarea'
  })
    .then(async ({ value }) => {
      await ApiCompleteTask({
        taskId: bpmn.value.taskId,
        remark: value
      })
      ElMessage.success(`操作成功`)
      bpmn.value.reload()
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}

// 驳回
function handleReject() {
  ElMessageBox.prompt('请填写驳回原因', '驳回', {
    confirmButtonText: '驳回',
    cancelButtonText: '取消',
    inputType: 'textarea'
  })
    .then(async ({ value }) => {
      if (!value) {
        ElMessage.error('请填写驳回原因')
        return
      }
      await ApiBackTask({
        taskId: bpmn.value.taskId,
        remark: value
      })
      ElMessage.success(`操作成功`)
      bpmn.value.reload()
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消'
      })
    })
}

// 审批记录
async function handleApproveRecord() {
  approveRecordRef.value.openDialog({ pageCode: bpmn.value.pageCode, docCode: bpmn.value.docCode })
}
</script>
<style lang="scss" scoped>
.base-bpmn-controler {
  padding: 0 10px;
  display: inline-block;
  align-items: center;
  justify-content: center;
}
</style>
