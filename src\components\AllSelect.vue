<template>
  <!-- 所有下拉组件 -->
  <el-select
    ref="select"
    :loading="loading"
    :model-value="inputValue"
    :remote-method="remoteMethod"
    filterable
    placeholder="请选择"
    remote
    clearable
    :value-key="keyId"
    @focus="remoteMethod('')"
    @change="dealValue($event)"
    @visible-change="visibleChange"
    :disabled="disabled"
    :multiple="multiple"
    :validate-event="false"
  >
    <template v-if="!$slots.default">
      <el-option v-for="item in dropList" :key="item[keyId]" :label="item[keyName]" :value="item"></el-option>
    </template>
    <template v-else>
      <el-option v-for="item in dropList" :key="item[keyId]" :label="item[keyName]" :value="item">
        <slot :item="item"></slot>
      </el-option>
    </template>
  </el-select>
</template>
<script>
import { ElMessage } from 'element-plus'
import { defineComponent } from 'vue'
import _ from 'lodash'

export default defineComponent({
  name: 'AllSelect',
  props: {
    keyName: {
      type: String,
      required: true
    },
    api: {
      type: Function,
      required: true
    },
    apiParams: {
      type: Object,
      default: () => {}
    },
    inputValue: {
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    //默认带出第一个选项
    defaultCreated: {
      type: Boolean,
      default: false
    },
    fix: {
      type: Boolean,
      default: false
    },
    autoFocus: {
      type: Boolean,
      default: false
    },
    keyId: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    uniqKeyName: {
      type: String,
      default: ''
    },
    realBody: {
      type: String,
      default: ''
    }
  },
  emits: ['update:inputValue', 'valueChange'],
  data() {
    return {
      loading: false,
      dropList: [],
      visibleShow: false
    }
  },
  created() {
    if (this.defaultCreated) {
      this.remoteMethod().then(() => {
        this.$emit('update:inputValue', this.dropList[0][this.keyName])
        this.dealValue(this.dropList[0])
      })
    }
  },
  mounted() {
    if (this.autoFocus) {
      this.$refs.select.focus()
    }
  },
  methods: {
    visibleChange(v) {
      // console.log(v);
      this.visibleShow = v
    },
    remoteMethod: _.debounce(function (q) {
      if (!this.visibleShow) {
        return
      }
      // console.log('do');
      this.loading = true
      // let qFun
      // if (this.realBody) {
      //   qFun = {
      //     [this.keyName]: q,
      //     pageSize: 100,
      //     [this.realBody]: {
      //       ...this.apiParams,
      //       [this.keyName]: q
      //     },
      //     ...this.apiParams
      //   }
      // } else {
      //   qFun = {
      //     [this.keyName]: q,
      //     pageSize: 100,
      //     ...this.apiParams
      //   }
      // }
      return this.api({ pageSize: 100 }, { [this.keyName]: q, ...this.apiParams })
        .then((resp) => {
          console.log(resp)
          if (resp instanceof Array) {
            this.dropList = resp
            if (this.uniqKeyName) {
              this.dropList = _.uniqBy(this.dropList, this.uniqKeyName)
            }
            //增加结果根据字典排序
            this.dropList.sort((a, b) => {
              return a[this.keyName].localeCompare(b[this.keyName])
            })
            return
          }
          if (resp.records) {
            if (resp.records.length === 0) {
              ElMessage.warning('没有查询到数据')
            }
            this.dropList = resp.records
            if (this.uniqKeyName) {
              this.dropList = _.uniqBy(this.dropList, this.uniqKeyName)
            }
            //增加结果根据字典排序
            this.dropList.sort((a, b) => {
              return a[this.keyName].localeCompare(b[this.keyName])
            })
          }
          if (resp.list) {
            if (resp.list.length === 0) {
              ElMessage.warning('没有查询到数据')
            }
            this.dropList = resp.list
            if (this.uniqKeyName) {
              this.dropList = _.uniqBy(this.dropList, this.uniqKeyName)
            }
            //  console.log(this.keyName)
            //增加结果根据字典排序
            this.dropList.sort((a, b) => {
              // console.log(a[this.keyName], b[this.keyName])
              return a[this.keyName].localeCompare(b[this.keyName])
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
    }, 0),
    dealValue(v) {
      if (typeof v === 'object' && !this.multiple) {
        this.$emit('update:inputValue', v[this.keyName])
        this.$emit('valueChange', v)
        return
      } else if (typeof v === 'object' && this.multiple) {
        this.$emit('update:inputValue', v)
        this.$emit('valueChange', v)
        return
      }
      if (this.multiple) {
        this.$emit('update:inputValue', [])
        this.$emit('valueChange', [])
      } else {
        this.$emit('update:inputValue', '')
        this.$emit('valueChange', {})
      }
    }
  }
})
</script>
