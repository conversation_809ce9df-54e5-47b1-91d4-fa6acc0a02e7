import request from '@/common/request.js'

//查询调整单列表
export const fetchGetAllotList = (params = {}, data = {}) => {
    return request({
        url: '/mes_api/v1/wmAdjinvOrder/pages',
        method: 'POST',
        params,
        data
    })
}

// 查询调整单明细
export const fetchGetAllotDetail = (params = {}, data = {}) => {
    return request({
        url: '/mes_api/v1/wmAdjinvItem/pages',
        method: 'POST',
        params,
        data
    })
}

// 调整单删除
export const fetchDeleteAllot = (ids) => {
    return request({
        url: `/mes_api/v1/wmAdjinvOrder/${ids}/delete`,
        method: 'DELETE',
    })
}

// 调整单新增
export const fetchAddAllot = (data = {}) => {
    return request({
        url: '/mes_api/v1/wmAdjinvOrder/save',
        method: 'POST',
        data
    })
}
// 调整单修改
export const fetchUpdateAllot = (data = {}) => {
    return request({
        url: '/mes_api/v1/wmAdjinvOrder/update',
        method: 'PUT',
        data
    })
}

// 调整单确认调整
export const fetchConfirmAllot = (data) => {
    return request({
        url: `/mes_api/v1/wmAdjinvOrder/confirm`,
        method: 'POST',
        data
    })
}

// 调整单关闭
export const fetchCloseAllot = (params) => {
    return request({
        url: `/mes_api/v1/wmAdjinvOrder/close`,
        method: 'POST',
        params
    })
}