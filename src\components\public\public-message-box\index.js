import { app } from '@/main.js'
import { createVNode, render } from 'vue'
import VueTemplate from './index.vue'

export default function (option) {
  let node = document.getElementById('public-message-box-container')
  if (!node) {
    node = document.createElement('div', { id: 'public-message-box-container' })
    document.getElementById('app').appendChild(node)
  }
  let instance = createVNode(VueTemplate, {
    name: 'public-message-box',
    title: option.title,
    btns: option.btns
  })
  instance.appContext = app._context
  render(instance, node)
  return instance.el
}
