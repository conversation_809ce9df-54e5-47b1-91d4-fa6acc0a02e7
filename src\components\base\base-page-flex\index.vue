<script setup>
import { ref } from 'vue'

const props = defineProps({
  direction: {
    type: String,
    default: 'column'
  }
})
const style = ref({
  display: 'flex',
  flexDirection: props.direction
})
</script>

<template>
  <div class="base-page-flex" :style="style">
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.base-page-flex {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
