import request from '@/common/request.js'

// 主数据配置

/*
 * 装箱主数据
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
//查询
export const getPackageList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matPackageMaster/pages',
    method: 'POST',
    params,
    data
  })
}

//新增
export const AddPackageItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/matPackageMaster/save',
    method: 'POST',
    data
  })
}

//编辑
export const editPackageItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/matPackageMaster/update',
    method: 'PUT',
    data
  })
}

// 删除
export const delPackageItem = (data) => {
  return request({
    url: `/mes_api/v1/matPackageMaster/${data.ids}/delete`,
    method: 'DELETE'
  })
}

/*
 * 库位主数据
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
//查询
export const getBinList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/pages',
    method: 'POST',
    params,
    data
  })
}

//新增
export const AddBinItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/save',
    method: 'POST',
    data
  })
}

//编辑
export const editBinItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/update',
    method: 'PUT',
    data
  })
}

// 删除
export const delBinItem = (data) => {
  return request({
    url: `/mes_api/v1/wmStorageBin/${data.ids}/delete`,
    method: 'DELETE'
  })
}

/*
 * 工位库位主数据
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
//
export const getStationBinList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/pages',
    method: 'POST',
    params,
    data
  })
}

//新增
export const AddStationBinItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/save',
    method: 'POST',
    data
  })
}

//编辑
export const editStationBinItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/update',
    method: 'PUT',
    data
  })
}

// 删除
export const delStationBinItem = (data) => {
  return request({
    url: `/mes_api/v1/proStationBin/${data.ids}/delete`,
    method: 'DELETE'
  })
}

/*
 * 物料主数据
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
//
export const getMatList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/pages',
    method: 'POST',
    params,
    data
  })
}

export const getproRouteList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proRouting/pages',
    method: 'POST',
    params,
    data
  })
}

//获取字典数据getDictTypeList
export const getDictTypeList = (params = {}) => {
  // return request({
  //   url: '/mes_api/v1/dict/findChildren',
  //   method: 'GET',
  //   params
  // })
  return request({
    url: '/api/v1/sysDict/findChildren',
    method: 'GET',
    params
  })
}

// 1.物料 ------------------------------------------------
/*
 *  获取物料主数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetMaterialMaster = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  新增物料主数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddMaterialMaster = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑物料主数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateMaterialMaster = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除物料主数据
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelMaterialMaster = (ids) => {
  return request({
    url: `/mes_api/v1/matMaterialMaster/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 查询物料主数据详情
 * @param { Object }: params
 * @return { Promise }
 */
export const GetMaterialMasterDetail = (params) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/queryDetailByMatCode',
    method: 'GET',
    params
  })
}

/**
 * @description: 查询物料主数据配置批次
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetMatMaterialBatch = (params = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialBatch/getMatBatch',
    method: 'GET',
    params
  })
}
/**
 * @description: 更新物料主数据配置批次
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const matMaterialBatchBatchUpdate = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialBatch/batchUpdate',
    method: 'POST',
    data
  })
}

// 2.物料BOM ------------------------------------------------
/*
 *  获取物料BOM主数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetMaterialBom = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialBom/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  新增物料BOM主数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddMaterialBom = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialBom/save',
    method: 'POST',
    data: data
  })
}

// 新增物料BOM配置数据
export const AddMaterialBomSaveInfo = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialBom/saveInfo',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑物料BOM主数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateMaterialBom = (data) => {
  return request({
    url: '/mes_api/v1/matMaterialBom/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除物料BOM主数据
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelMaterialBom = (ids) => {
  return request({
    url: `/mes_api/v1/matMaterialBom/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  通过id查询物料BOM详情
 *  @param { id: Number }
 *  @returns { Promise }
 */
export const GetMaterialBomDetails = (id) => {
  return request({
    url: `/mes_api/v1/matMaterialBom/${id}`,
    method: 'GET'
  })
}

/*
 *  获取物料BOM配件(子项)主数据列表
 *  @param { id: Number }
 *  @returns { Promise }
 */
export const GetMaterialBomItem = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialBomItem/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  删除物料BOM配件(子项)主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelmatMaterialBomItem = (ids) => {
  return request({
    url: `/mes_api/v1/matMaterialBomItem/${ids}/delete`,
    method: 'DELETE'
  })
}

// 3.工艺路径 ------------------------------------------------
/*
 *  获取工艺路径主数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetProcessPath = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proRouting/pages',
    method: 'POST',
    params,
    data
  })
}

//根据工单编号查询工艺路径详情
export const fetchGetProcessDetails = (params) => {
  return request({
    url: '/mes_api/v1/planOrder/info',
    method: 'get',
    params
  })
}

/*
 *  新增工艺路径数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const AddProcessPathSave = (data) => {
  return request({
    url: '/mes_api/v1/proRouting/save',
    method: 'POST',
    data: data
  })
}

/*
 *  编辑工艺路径数据
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const UpdateProcessPath = (data) => {
  return request({
    url: '/mes_api/v1/proRouting/update',
    method: 'PUT',
    data: data
  })
}

/*
 *  删除工艺路径主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelProcessPath = (ids) => {
  return request({
    url: `/mes_api/v1/proRouting/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  查询工艺路径配置详情
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetProcessPathConfigInfo = (params) => {
  return request({
    url: '/mes_api/v1/proRouting/info',
    method: 'GET',
    params
  })
}

/*
 *  保存工艺路径配置
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveInfoProcessPath = (data) => {
  return request({
    url: '/mes_api/v1/proRouting/saveInfo',
    method: 'POST',
    data
  })
}

/*
 *  保存工艺路径配置Pro 不影响标准工序
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const SaveInfoProcessPathPro = (data) => {
  return request({
    url: '/mes_api/v1/graphicalConfig/saveProRoute',
    method: 'POST',
    data
  })
}

// 4.工序 ------------------------------------------------
/*
 *  获取工序主数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetProcessMasterdata = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/opBaseDispose/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  保存工序配置
 *  @param { data: Object }
 *  @returns { Promise }
 */
export const saveInfoProcess = (data) => {
  return request({
    url: '/mes_api/v1/opBaseDispose/saveInfo',
    method: 'POST',
    data
  })
}

/*
 *  获取上一道工序出站的物料列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const getParentOutMats = (params) => {
  return request({
    url: '/mes_api/v1/opBaseDispose/getParentOutMats',
    method: 'GET',
    params
  })
}

/*
 *  查询工序配置详情
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetProcessConfigInfo = (params) => {
  return request({
    url: '/mes_api/v1/opBaseDispose/info',
    method: 'GET',
    params
  })
}

/*
 *  删除工序主数据
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelProcessMasterdata = (ids) => {
  return request({
    url: `/mes_api/v1/opBaseDispose/${ids}/delete`,
    method: 'DELETE'
  })
}

// 5.下一位工序 ------------------------------------------------
/*
 *  获取工序主数据列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetNextProcessdata = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/opBackCode/pages',
    method: 'POST',
    params,
    data
  })
}

// 6.上料 ------------------------------------------------
/*
 *  删除上料主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelFeedingMasterdata = (ids) => {
  return request({
    url: `/mes_api/v1/opFeedMeterial/${ids}/delete`,
    method: 'DELETE'
  })
}

// 计划管理配置

/*
 *  获取计划单列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const getPlanOrderList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/pages',
    method: 'POST',
    params,
    data
  })
}

// 新增
export const addPlanOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/save',
    method: 'POST',
    data
  })
}

//更新
export const editPlanOrderItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/update',
    method: 'PUT',
    data
  })
}

// 获取齐套性检查表格
export const getCheckAlignmentList = (data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/completeSet',
    method: 'POST',
    data
  })
}

// 计划备料
export const fetchPlanPrepareMat = (data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/prepareMat',
    method: 'POST',
    data
  })
}

//删除
export const delPlanOrderItem = (data) => {
  return request({
    url: `/mes_api/v1/planOrder/${data.ids}/delete`,
    method: 'DELETE'
  })
}
//发布
export const publishOrder = (data) => {
  return request({
    url: `/mes_api/v1/planOrder/publishOrder`,
    method: 'POST',
    data
  })
}
/*
 *  工单状态撤回
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const revokeOrderStatus = (data) => {
  return request({
    url: '/mes_api/v1/planOrder/recallOrder',
    method: 'PUT',
    data
  })
}

/*
 *  获取字典
 *  @param { params: Object }
 *  @returns { Promise }
 */
export const GetDictData = (params) => {
  return request({
    url: '/api/v1/sysDict/findChildren',
    method: 'GET',
    params
  })
}

/**
 *  查询字典项
 *  @param { code }
 *  @returns { Promise }
 */
export const GetDictByCode = (value = '') => {
  return request({
    url: '/api/v1/sysDict/findChildren',
    method: 'GET',
    params: { value }
  })
}

/*
 *  删除在制品主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelWorkInProgress = (ids) => {
  return request({
    url: `/mes_api/v1/opFeedWip/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  删除物料扣减清单主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelMeterialDeduction = (ids) => {
  return request({
    url: `/mes_api/v1/opConsumeMeterial/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  删除在制品扣减清单主数据列表
 *  @param { ids: String }
 *  @returns { Promise }
 */
export const DelWorkInProgressDeduction = (ids) => {
  return request({
    url: `/mes_api/v1/opConsumeWip/${ids}/delete`,
    method: 'DELETE'
  })
}

// 工艺路径图形化配置 ------------------------------------------------

/**
 * 通过编号获取配置信息
 */

export const GetProRoutingViewJson = (params) => {
  return request({
    url: '/mes_api/v1/proRoutingMapView/getInfo',
    method: 'GET',
    params
  })
}

/**
 * 保存工序配置图形化信息
 */
export const SaveProRoutingViewJson = (data) => {
  if (data.id) {
    return request({
      url: '/mes_api/v1/proRoutingMapView/update',
      method: 'PUT',
      data
    })
  } else {
    return request({
      url: '/mes_api/v1/proRoutingMapView/save',
      method: 'POST',
      data
    })
  }
}

/*
 *  获取检验项配置列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const getBaseReviewList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/opBaseReview/page',
    method: 'POST',
    params,
    data
  })
}

// 新增
export const addBaseReviewItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/opBaseReview/add',
    method: 'POST',
    data
  })
}

//更新
export const editBaseReviewItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/opBaseReview/update',
    method: 'PUT',
    data
  })
}

//删除
export const delBaseReviewItem = (data) => {
  return request({
    url: `/mes_api/v1/opBaseReview/${data.ids}`,
    method: 'DELETE'
  })
}

// 质量管理 ------------------------------------------------

/*
 *  获取质量放行列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const getQualityList = (params = {}, data = {}) => {
  return request({
    // url: '/mes_api/v1/proControlLog/proOutList',
    url: '/mes_api/v1/wmHolding/qualityOutList',
    method: 'POST',
    params,
    data
  })
}
/*
 *   质量放行列表解冻操作
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const unfreezeQualityItems = (params = {}) => {
  return request({
    // url: '/mes_api/v1/proControlLog/free',
    url: '/mes_api/v1/stockDeal/free',
    method: 'GET',
    params
  })
}

/*
 *   质量履行记录列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const getProControlLog = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proControlLog/pageList',
    method: 'POST',
    params,
    data
  })
}

/*
 *   质量不合格品
 *  @param { params, data: Object }
 *  @returns { Promise }
 */

export const getUnqualified = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/nonpUnit/pages',
    method: 'POST',
    params,
    data
  })
}

// 不合格品物料损耗列表
export const getMertalierLossList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/nonpMatReuse/pages',
    method: 'POST',
    params,
    data
  })
}

//评审

//更新
export const addReviewItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/lineStock/review',
    method: 'POST',
    data
  })
}

// 质量管理 ------------------------------------------------

/**
 * @name 质量/单件追溯/通过载体编码(单件条码)获取追溯信息
 * @method GET
 * @param {string} tuNo 单件条码
 */
// 质量 单件追溯 通过载体编码(单件条码)获取追溯信息
export const trcTraceUnitSingle = (tuNo) => {
  return request({
    url: '/mes_api/v1/trcSingleTrace/detail',
    method: 'GET',
    params: {
      tuNo
    }
  })
}

/**
 * 通过集合id获取追溯信息
 * @method GET
 * @param {string} tuNo - 集合id
 * @returns {Promise} - 返回一个Promise对象，它在成功时解析为追溯信息
 */
export const TrcUnitDetail = (tuNo) => {
  return request({
    url: '/trace_api/v1/trcUnit/detail',
    method: 'GET',
    params: {
      tuNo
    }
  })
}

/**
 * @name 质量/批次追溯/通过批次号和物料号获取批次关系图及工单相关信息
 * @method GET
 * @param {string} batchCode 批次号
 * @param {string} matCode 物料号
 */
export const trcTraceUnitBatchRelation = (batchCode, matCode) => {
  return request({
    url: '/mes_api/v1/trcSingleTrace/batchRelation',
    method: 'GET',
    params: {
      batchCode,
      matCode
    }
  })
}

/**
 * @name 质量/批次追溯/通过批次号查询转换记录
 * @method GET
 * @param {string} batchCode 批次号
 * @param {string} matCode 物料号
 */
export const trcBatchConvertRecords = (batchCode, matCode) => {
  return request({
    url: '/trace_api/trcBatchConvert/detail',
    method: 'GET',
    params: {
      batchCode,
      matCode
    }
  })
}

/**
 * @name 过程检/过程检验/新增或编辑检验项项目配置
 * @method POST
 * @param { Object }: data
 */
export const addorEditCheckItemSetting = (data = {}) => {
  return request({
    url: '/mes_api/v1/opMatReview/saveOrUpdate',
    method: 'POST',
    data
  })
}

/**
 * @name 过程检/过程检验/通过物料编号查询检查配置信息
 * @method GET
 * @param {string} matCode 物料号
 */
export const getCheckInfoByMatCode = (matCode) => {
  return request({
    url: '/mes_api/v1/opMatReview/getByMatCode',
    method: 'GET',
    params: {
      matCode
    }
  })
}
/**
 * @name 过程检/过程检验/物料配置检验项查询
 * @param {Object} params
 * @param {Object} data
 */
export const getMatCheckItemList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/opMatReview/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name 过程检/过程检验/获取可过程检验单
 * @param {Object} params
 * @param {Object} data
 */
export const getOrderCheckList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/qcReview/getReviewOrder',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name 过程检/过程检验/获取工单的待检查配置信息
 * @param {Object} params
 * @param {Object} data
 */
export const getWaitCheckItemInfo = (data = {}) => {
  return request({
    url: '/mes_api/v1/qcReview/getReviewPropertyVo',
    method: 'POST',
    data
  })
}

/**
 * @name 过程检/过程检验/新增过程检验单数据
 * @param {Object} params
 * @param {Object} data
 */
export const addCheckItemData = (data = {}) => {
  return request({
    url: '/mes_api/v1/qcReview/add',
    method: 'POST',
    data
  })
}

/**
 * @name 过程检/过程检验/获取过程检验单详情列表
 * @param {Object} params
 * @param {Object} data
 */
export const getOrderCheckDetailList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/qcReview/page',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name 过程检/过程检验/获取过程检验单详情
 * @param {Object} params
 * @param {Object} data
 */
export const getOrderCheckDetailInfo = (data = {}) => {
  return request({
    url: '/mes_api/v1/qcReviewDetail/getOrderDetail',
    method: 'POST',
    data
  })
}

/**
 * @name 过程检/过程检验/删除物料关联的过程检验配置
 * @param {Object} params
 * @param {Object} data
 */
export const DelInspectionByMatItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/opMatReview/deleteByCode',
    method: 'DELETE',
    params
  })
}

/**
 * @name 过程检/过程检验/通过检测项id删除检查配置信息
 * @param {Object} params
 * @param {Object} data
 */
export const DelInspectionByIdItem = (params = {}) => {
  return request({
    url: '/mes_api/v1/opMatReview/deleteById',
    method: 'DELETE',
    params
  })
}
/**
 * @name 过程检/过程检验/通过检测项id删除检查配置信息
 * @param {Object} params
 * @param {Object} data
 */
export const DelInspectionRuleById = (params = {}) => {
  return request({
    url: '/mes_api/v1/spotCheckReview/deleteById',
    method: 'DELETE',
    params
  })
}

/**
 *@name 质量管理/质检/查询
 *@param  {String }  params
 *@param  {Object} data
  @return { Promise }
*/
export const getQcTaskList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcTask/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 *@name 质量管理/质检/合格/不合格
 *@param  {Object} data
  @return { Promise }
*/
export const changeQcTaskStatus = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcTask/updateWmQcTask',
    method: 'PUT',
    data
  })
}

/**
 *@name 质量管理/质量追溯/跟踪号追溯/正向
 *@param  {Object} data
  @return { Promise }
*/
export const GetSuNoTraceData = (params = {}) => {
  return request({
    url: '/trace_api/trcUnitConvert/trc/forward',
    method: 'GET',
    params
  })
}

/**
 *@name 质量管理/质量追溯/跟踪号追溯/反向
 *@param  {Object} data
  @return { Promise }
*/
export const GetSuNoReverseTraceData = (params = {}) => {
  return request({
    url: '/trace_api/trcUnitConvert/trc/reverse',
    method: 'GET',
    params
  })
}

/**
 *@name 质量管理/质量追溯/批次追溯/正向
 *@param  {Object} data
  @return { Promise }
*/
export const GetBatchTraceData = (params = {}) => {
  return request({
    url: '/trace_api/trcBatchConvert/trc/forward',
    method: 'GET',
    params
  })
}

/**
 *@name 质量管理/质量追溯/批次追溯/反向
 *@param  {Object} data
  @return { Promise }
*/
export const GetBatchReverseTraceData = (params = {}) => {
  return request({
    url: '/trace_api/trcBatchConvert/trc/reverse',
    method: 'GET',
    params
  })
}

/**
 *@name 质量管理/质量追溯/批次追溯/相关信息
 *@param  {Object} data
  @return { Promise }
*/
export const GetBatchTraceInfoData = (params = {}) => {
  return request({
    url: '/trace_api/trcBatchConvert/trc/batchInfo',
    method: 'GET',
    params
  })
}

/**
 *@name 质量管理/质量追溯/单件追溯/
 *@param  {Object} data
  @return { Promise }
*/
export const GetSingleTraceData = (params = {}) => {
  return request({
    url: '/trace_api/v1/trcUnit/trc/su',
    method: 'GET',
    params
  })
}

// export const getInventoryList = (params = {}, data = {}) => {
//   return request({
//     url: '/mes_api/v1/wmQuant/pages',
//     method: 'POST',
//     params,
//     data
//   })
// }
/*
 *  库存调整
 *  @param { params, data: Object }
 *  @returns { Promise }
 */

// 库存物料分页查询
// export const getInventoryList = (params = {}, data = {}) => {
//   return request({
//     url: '/mes_api/v1/wmQuant/pages',
//     method: 'POST',
//     params,
//     data
//   })
// }

// 库存物料分页查询
export const getInventoryList = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/moveBinQuery',
    method: 'POST',
    data
  })
}

//查询散件库存
export const getScatteredInventoryList = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/moveBinQuerySpare',
    method: 'POST',
    data
  })
}

//查询冻结解冻单件子集
export const getFreezeChildrrenList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/pages',
    method: 'POST',
    params,
    data
  })
}

// 拆合包记录 ----------------------
/**
 * @description: 拆合包记录列表
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const getUnpackingRecord = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmConsolidation/pages',
    method: 'POST',
    params,
    data
  })
}

// 移库提交
export const moveBinSubmit = (data = {}) => {
  return request({
    url: '/mes_api/v1/stockDeal/moveBinSubmit',
    method: 'POST',
    data
  })
}
// 拆合包 ---------------------------------------
/**
 * @description: 请扫描包装
 * @param { Object }: params
 * @param { Number }: pkgType: 1单件详情, 2包装数据
 * @return { Promise }
 */
export const GetScanByBackNo = (params = {}, pkgType) => {
  return request({
    url: `/mes_api/v1/wmQuant/wmQuantByBackNo?pkgType=${pkgType}`,
    method: 'GET',
    params
  })
}
/*
 * @description: 拆合包记录详情
 * @param { Object }: params
 * @return { Promise }
 */
export const getUnpackingRecordDetails = (params) => {
  return request({
    url: '/mes_api/v1/wmConsolidationItem/byOrderNo',
    method: 'GET',
    params
  })
}
/**
 * @description: 提交拆合包
 * @param { Object }: data
 * @return { Promise}
 */
export const submitUnpacking = (data) => {
  return request({
    url: '/mes_api/v1/trcPackLog/save',
    method: 'POST',
    data
  })
}

// 库存调整  移库记录
export const getMoveBinList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/moveBinOrder/pages',
    method: 'POST',
    params,
    data
  })
}

// 根据主键查询移库详情
export const getMoveBinDetail = (params = {}, data = {}) => {
  console.log(params, data)
  return request({
    url: '/mes_api/v1/moveBinOrderDetail/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name 库存台账/物料库存/根据物料号查询
 * @param {Object} page
 * @param {Number} page.page 页码
 * @param {Number} page.size 每页条数
 * @param {Object} data
 * @param {String} data.matCode 物料号
 * @param {String} data.matName 物料名称
 * @param {String} data.productionClass 生产分类
 */
export const GetWmQuantPaginByMatCode = (page = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/paginByMatCode',
    method: 'POST',
    params: page,
    data: {
      matCode: data.matCode,
      matName: data.matName,
      productionClass: data.productionClass
    }
  })
}

/**
 * @name 库存台账/物料库存/根据库位号查询
 * @param {Object} page
 * @param {Number} page.page 页码
 * @param {Number} page.size 每页条数
 * @param {Object} data
 * @param {String} data.binCode 库位号
 */
export const GetWmQuantPaginByBinCode = (page = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/paginByBinCode',
    method: 'POST',
    params: page,
    data: {
      binCode: data.binCode
    }
  })
}

/**
 * @name 库存台账/库存台账-选中一行展示列表
 * @param {String} matCode 物料号
 * @param {String} binCode 库位号 可为空
 */
export const GetStockDealLedgerList = (matCode, binCode) => {
  return request({
    url: '/mes_api/v1/stockDeal/ledgerList',
    method: 'POST',
    data: {
      matCode,
      binCode
    }
  })
}

//库存台帐统计
export function getStockDealLedgerCount(params, data) {
  return request({
    url: '/mes_api/v1/wmQuant/queryLedger',
    method: 'POST',
    params,
    data
  })
}

//库存台帐跟踪号查询
export function getStockDealBackNo(params, data) {
  return request({
    url: '/mes_api/v1/wmQuant/queryLedgerItem',
    method: 'POST',
    params,
    data
  })
}
/**
 * @name 点击库存台帐数据查询详情
 * @params {Object} page
 * @params {Object} data
 */

export function getStockDealLedgerDetail(params, data) {
  return request({
    url: '/mes_api/v1/wmQuant/queryLedgerItem',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name 通过库存数量编号查询占用详情
 * @params {quantNo}
 */
export function getStockDealLedgerDetailByCode(params) {
  return request({
    // url: '/mes_api/v1/wmQuantAlloc/getByQuantNo',
    url: '/mes_api/v1/wmQuantAlloc/getBySuNo',
    method: 'get',
    params
  })
}

/**
 * @name 通过包装号查询包含的SN号
 * @params {bankNo}
 */
export function getStockDealLedgerDetailByBackNo(params) {
  return request({
    url: '/mes_api/v1/wmStorageUnit/getSns',
    method: 'get',
    params
  })
}

/**
 * @name 计划管理负荷分析
 * @data {Object}
 */
export const GetLineloadAnalysis = (data) => {
  return request({
    url: '/mes_api/v1/planOrder/loadAnalysis',
    method: 'POST',
    data
  })
}

/**
 * @description: 获取在制品物料编码
 * @param { String }: wip
 * @return { Promise }
 */
export const GetGenerateByCode = () => {
  return request({
    url: '/mes_api/v1/idGenerator/generateByCode?ruleCode=WIP',
    method: 'GET'
  })
}
export const GetGenerateByCodePath = (params) => {
  return request({
    url: '/mes_api/v1/idGenerator/generateByCode',
    method: 'GET',
    params
  })
}

/**
 * @description: 根据托盘号查询库存详情
 * @data { String }: parentSu
 * @return { Promise }
 */
export const QueryParentSuWmquant = (data) => {
  return request({
    url: '/mes_api/v1/wmQuant/queryParentSuWmquant',
    method: 'POST',
    data
  })
}

/**
 * @description: 查询缺陷占比
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetPaginDefectPercent = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/nonpUnit/paginDefectPercent',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 查询缺陷模式
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetOpDefectMode = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/opDefectMode/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 生产工单操作记录接口
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetPlanOrderHistory = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/planOrderHistory/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 新增缺陷模式
 * @param { Object }: data
 * @return { Promise }
 */
export const AddOpDefectMode = (data) => {
  return request({
    url: '/mes_api/v1/opDefectMode/save',
    method: 'POST',
    data
  })
}

/**
 * @description: 更新缺陷模式
 * @param { Object }: data
 * @return { Promise }
 */
export const UpdateOpDefectMode = (data) => {
  return request({
    url: '/mes_api/v1/opDefectMode/update',
    method: 'PUT',
    data
  })
}

/**
 * @description: 删除缺陷模式
 * @param { String }: ids
 * @return { Promise }
 */
export const DelOpDefectMode = (ids) => {
  return request({
    url: `/mes_api/v1/opDefectMode/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 查询工序主数据
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetProRoutingOperation = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proRoutingOperation/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 根据包装号查询库存验证是否可以打托
 * @param { Object }: params
 * @return { Promise }
 */
export const getPalletBackNo = (params) => {
  return request({
    url: '/mes_api/v1/wmPalletizing/getSuNo',
    method: 'GET',
    params
  })
}

/**
 * @description: 打托提交
 * @param { Object }: data
 * @return { Promise }
 */
export const SavePallet = (data) => {
  return request({
    url: '/mes_api/v1/wmPalletizing/savePallet',
    method: 'POST',
    data
  })
}

/**
 * @description: 根据托盘号查询库存
 * @param { Object }: params
 * @return { Promise }
 */
export const getParentSu = (params) => {
  return request({
    url: '/mes_api/v1/wmPalletizing/getParentSu',
    method: 'GET',
    params
  })
}

/**
 * @description: 拆托提交
 * @param { Object }: data
 * @return { Promise }
 */
export const SaveUnPallet = (data) => {
  return request({
    url: '/mes_api/v1/wmPalletizing/saveUnPallet',
    method: 'POST',
    data
  })
}

/**
 * @description: 打托拆托记录
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetPalletLog = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPalletizing/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 打托拆托记录详情
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetPalletDetailedLog = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPalletizingItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: HU回溯
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetTrcPackageHuList = (params = {}, data = {}) => {
  return request({
    url: '/trace_api/trcPackage/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 装箱记录
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetProPkgLog = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proPkgLog/single/stationInPages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 工单报表
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetPlanOrderReport = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/planOrder/paginOrderReport',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 根据物料号进行分组，展示物料消耗
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetPaginGroupByMatCode = (params = {}, data = {}) => {
  return request({
    // url: '/mes_api/v1/trcMaterialConsume/paginGroupByMatCode',
    url: '/mes_api/v1/nonpUnitItem/getByOrderNo',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 加工记录
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetTrcProcessDocumentReport = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/trcProcessDocument/report/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 加工记录详情
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetTrcProcessDetailReport = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/trcProcessDetail/report/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 进出站记录分页查询
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetTrcStationLogReport = (params = {}, data = {}) => {
  return request({
    url: '/trace_api/v1/trcStationLog/report/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 工单工序完成率查询
 * @param { Object }: params
 * @return { Promise }
 */
export const GetTrcProcessDocumentRate = (params = {}) => {
  return request({
    url: '/mes_api/v1/trcProcessDocument/getRate',
    method: 'GET',
    params
  })
}

/**
 * @description: 工位配置主数据
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetProStationBinPages = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 新增工位配置主数据
 * @param { Object }: data
 * @return { Promise }
 */
export const SaveProStationBin = (data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/save',
    method: 'POST',
    data
  })
}

/**
 * @description: 编辑工位配置主数据
 * @param { Object }: data
 * @return { Promise }
 */
export const UpdateProStationBin = (data = {}) => {
  return request({
    url: '/mes_api/v1/proStationBin/update',
    method: 'PUT',
    data
  })
}

/**
 * @description: 删除工位配置主数据
 * @param { Object }: data
 * @return { Promise }
 */
export const DeleteProStationBin = (ids) => {
  return request({
    url: `/mes_api/v1/proStationBin/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 合包提交
 * @param { Object }: data
 * @return { Promise }
 */
export const SavePack = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmConsolidation/savePack',
    method: 'POST',
    data
  })
}

/**
 * @description: 拆包提交
 * @param { Object }: data
 * @return { Promise }
 */
export const SaveUnPack = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmConsolidation/saveUnPack',
    method: 'POST',
    data
  })
}

/**
 * @description: 获取进度分析数据
 * @param { Object } - params
 * @param { String } - params.lineCode 产线编号
 * @param { String } - params.month 月份
 * @return { Promise }
 */
export const GetPlanAnalysis = (params) => {
  return request({
    url: '/mes_api/v1/plan-analysis/report',
    method: 'GET',
    params
  })
}

/**
 * @description: 新增工艺路径时查询是否当前工艺路径存在并且有版本(7.29 Li POST改成GET)
 * @param { Object }: data
 * @return { Promise }
 */
export const GetIdGeneratorRoutingVersion = (params) => {
  return request({
    url: '/mes_api/v1/idGenerator/vertifyRoutVersion',
    method: 'GET',
    params
  })
}

/**
 * @description: 查询上架任务
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetPutawayTaskPages = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 新增上架任务
 * @param { Object }: data
 * @return { Promise }
 */
export const SavePutawayTask = (data) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/saveFromWmquant',
    method: 'POST',
    data
  })
}

/**
 * @description: 上架
 * @param { Array }: data
 * @return { Promise }
 */
export const OnShelf = (data) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/onShelf',
    method: 'PUT',
    data
  })
}

/**
 * @description: 取消上架
 * @param { String }: id
 * @return { Promise }
 */
export const CancleOnShelf = (id) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/cancleOnShelf',
    method: 'DELETE',
    params: { id }
  })
}

/**
 * @description: 更新上架
 * @param { Object }: data
 * @return { Promise }
 */
export const UpdatePutawayTask = (data) => {
  return request({
    url: '/mes_api/v1/wmPutawayTask/update',
    method: 'PUT',
    data
  })
}

/**
 * @description: 获取仓库树
 */
export const GetStorageTree = () => {
  return request({
    url: '/mes_api/v1/wmWarehouse/getStructural',
    method: 'GET'
  })
}

/**
 * @name 获取库位
 * @param {Object} page
 * @param {Number} page.page 页码
 * @param {Number} page.size 每页条数
 * @param {Object} data
 * @param {String} data.matCode 物料号
 * @param {String} data.matName 物料名称
 * @param {String} data.productionClass 生产分类
 */

export const GetStorageBinList = (page, data) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/pages',
    method: 'POST',
    params: page,
    data
  })
}

/**
 * @description: 保存仓库
 * @param { Object }: data
 */
export const SaveStorage = (data) => {
  return request({
    url: '/mes_api/v1/wmWarehouse/save',
    method: 'POST',
    data
  })
}

/**
 * @description:查询仓库详情
 * @params:params
 */
export const getStorageDetail = (params) => {
  return request({
    url: '/mes_api/v1/wmWarehouse/detail',
    method: 'POST',
    params
  })
}

/**
 * @description: 保存库区
 * @param { Object }: data
 */
export const SaveStorageArea = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageArea/save',
    method: 'POST',
    data
  })
}

/**
 * @description: 查询库区详情
 * @params { Object }: data
 */
export const GetStorageAreaDetail = (params) => {
  return request({
    url: '/mes_api/v1/wmStorageArea/detail',
    method: 'POST',
    params
  })
}

/**
 * @description: 保存分区
 * @param { Array }: data
 */
export const SaveStorageSection = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageSection/saveBatch',
    method: 'POST',
    data
  })
}

/**
 * @description: 保存库位
 * @param { Object }: data
 */
export const SaveStorageBin = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/save',
    method: 'POST',
    data
  })
}

/**
 * @description: 更新库位
 * @param { Object }: data
 */
export const PutStorageBin = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/update',
    method: 'PUT',
    data
  })
}

/**
 * @description: 查询库位详情
 * @id { id }: id
 */
export const GetStorageBinDetail = (id) => {
  return request({
    url: `/mes_api/v1/wmStorageBin/${id}`,
    method: 'GET'
  })
}

/**
 * @description: 库位批量生成
 * @data { data }: data
 */
export const submitStorageBinBatch = (data) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/batchGenerate',
    method: 'POST',
    data
  })
}

/**
 * @description: 库位编号模版下载
 */
export const downloadStorageBinTemplate = () => {
  return request({
    responseType: 'blob',
    origin: true,
    url: '/mes_api/v1/wmStorageBin/write',
    method: 'POST',
    skip: true
  })
}

/**
 * @description: 删除仓库
 * @param { String }: ids
 */
export const DeleteStorage = (ids) => {
  return request({
    url: `/mes_api/v1/wmWarehouse/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 删除仓库库区
 * @param { String }: ids
 */
export const DeleteStorageArea = (ids) => {
  return request({
    url: `/mes_api/v1/wmStorageArea/${ids}/deleteById`,
    method: 'DELETE'
  })
}

/**
 * @description: 删除仓库库区
 * @param { String }: ids
 */
export const DeleteStorageSection = (ids) => {
  return request({
    url: `/mes_api/v1/wmStorageSection/${ids}/deleteById`,
    method: 'DELETE'
  })
}

/**
 * @description: 仓库级联查询
 * @param { Object }: params
 * @return { Promise }
 */
export const getWarehouseAssociationInfo = (params = {}) => {
  return request({
    url: '/mes_api/v1/WarehouseAssociation/info',
    method: 'GET',
    params
  })
}

/**
 * @description: 删除库位
 * @param { String }: ids
 */
export const DeleteStorageBin = (ids) => {
  return request({
    url: `/mes_api/v1/wmStorageBin/${ids}/deleteById`,
    method: 'DELETE'
  })
}

//批量删除库位
export const DeleteStorageBinBatch = (ids) => {
  return request({
    url: `/mes_api/v1/wmStorageBin/${ids}/delete`,
    method: 'DELETE'
  })
}

// 班组管理
/**
 * @name: 技能等级
 * @description: 技能等级分页查询
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetLaborSkillLevelPages = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/laborSkillLevel/pages',
    method: 'POST',
    data,
    params
  })
}

/**
 * @name: 技能等级
 * @description: 技能等级查询全部
 * @param { Object }: params
 * @return { Promise }
 */
export const GetLaborSkillLevelAll = (params = {}) => {
  return request({
    url: '/shift_api/v1/laborSkillLevel/getList',
    method: 'GET',
    params
  })
}

/**
 * @name: 技能等级
 * @description: 新增技能等级
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLaborSkillLevel = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborSkillLevel/save',
    method: 'POST',
    data
  })
}

/**
 * @name: 技能等级
 * @description: 编辑技能等级
 * @param { Object }: data
 * @return { Promise }
 */
export const UpdateLaborSkillLevel = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborSkillLevel/update',
    method: 'PUT',
    data
  })
}

/**
 * @name: 技能等级
 * @description: 删除技能等级
 * @param { String } id
 * @return { Promise }
 */
export const DeleteLaborSkillLevel = (id) => {
  return request({
    url: `/shift_api/v1/laborSkillLevel/${id}/delete`,
    method: 'DELETE'
  })
}

/**
 * @name: 岗位管理
 * @description: 岗位管理分页查询
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetLaborPositionPages = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/laborPosition/laborPositionPages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name: 岗位管理
 * @description: 新增岗位管理
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLaborPosition = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborPosition/save',
    method: 'POST',
    data
  })
}

/**
 * @name: 岗位管理
 * @description: 编辑岗位管理
 * @param { Object }: data
 * @return { Promise }
 */
export const UpdateLaborPosition = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborPosition/update',
    method: 'PUT',
    data
  })
}

/**
 * @name: 岗位管理
 * @description: 删除岗位管理
 * @param { String } id
 * @return { Promise }
 */
export const DeleteLaborPosition = (id) => {
  return request({
    url: `/shift_api/v1/laborPosition/${id}/delete`,
    method: 'DELETE'
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联工位分页查询
 * @param { Object }: data
 * @param { Object }: params
 * @return { Promise }
 */
export const GetLaborPositionStationPages = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/laborPositionStation/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name: 岗位配置
 * @description: 查询岗位配置所有已关联工位
 * @return { Promise }
 */
export const GetLaborPositionStationBindStation = () => {
  return request({
    url: '/shift_api/v1/laborPositionStation/getBindStation',
    method: 'GET'
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联工位新增
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLaborPositionStation = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborPositionStation/save',
    method: 'POST',
    data
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联工位删除
 * @param { String } id
 * @return { Promise }
 */
export const DeleteLaborPositionStation = (id) => {
  return request({
    url: `/shift_api/v1/laborPositionStation/${id}/delete`,
    method: 'DELETE'
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联人员分页查询
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLaborPositionStaffPages = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborPositionStaff/getListStaff',
    method: 'POST',
    data
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联人员新增
 * @param { Object }: data
 * @return { Promise }
 */
export const AddLaborPositionStaff = (data = {}) => {
  return request({
    url: '/shift_api/v1/laborPositionStaff/save',
    method: 'POST',
    data
  })
}

/**
 * @name: 岗位配置
 * @description: 岗位配置关联人员删除
 * @param { String } id
 * @return { Promise }
 */
export const DeleteLaborPositionStaff = (id) => {
  return request({
    url: `/shift_api/v1/laborPositionStaff/${id}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 打卡记录
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLaborAttendancePaginAttendList = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/laborAttendance/paginAttendList',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 工时统计
 * @param { Object }: params
 * @param { Object }: data
 * @return { Promise }
 */
export const GetLaborAttnStatPageAttnStat = (params = {}, data = {}) => {
  return request({
    url: '/shift_api/v1/laborAttnStat/pageAttnStat',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 通过模版打印箱标签
 * @param {Object} data
 * @return { Promise }
 */
export const PrintBoxLabelByTempl = (data = {}) => {
  return request({
    url: '/mes_api/v1/labelStorageUnit/printBoxLabelByTempl',
    method: 'POST',
    data
  })
}

/**
 * @description: 箱标签打印记录
 * @param {Object} data
 * @return { Promise }
 */
export const GetLabelStorageUnitPage = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/labelStorageUnit/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: BOM模版下载
 */
export const downloadBomTemplate = () => {
  return request({
    responseType: 'blob',
    origin: true,
    url: '/mes_api/v1/matMaterialBom/downloadBom',
    method: 'POST',
    skip: true
  })
}

/**
 * @description: 根据产品编码导入BOM
 * @return { Promise }
 */
export const importBomByProdCode = (data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialBom/importBomByProdCode',
    method: 'POST',
    data
  })
}

export const fetchGetMatProPath = (params, data) => {
  return request({
    url: '/mes_api/v1/proRouting/paginProRouting',
    method: 'POST',
    params,
    data
  })
}

export const fetchGetPlanOrderPrepareMat = (data) => {
  return request({
    url: '/mes_api/v1/planOrder/prepareMatDetail',
    method: 'post',
    data
  })
}

/**
 * @name: 工位iiot追溯
 * @description: 查询
 * @return { Promise }
 */
export const GetStationIIOTData = (params = {}) => {
  return request({
    url: '/iiot_api/v1/iiotStationTrace/getIiotStationTrace',
    method: 'GET',
    params
  })
}

/**
 * @name: 工位iiot追溯
 * @description: 保存
 * @return { data }
 */
export const AddStationIIOTItem = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotStationTrace/save',
    method: 'post',
    data
  })
}

/**
 * @name: IQC-采购质量-检验配置
 * @description: 新增
 * @return { Promise }
 */
export const AddWmQcItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcItem/save',
    method: 'post',
    data
  })
}

/**
 * @name: IQC-采购质量-检验配置
 * @description: 编辑
 * @return { Promise }
 */
export const EditWmQcItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcItem/edit',
    method: 'PUT',
    data
  })
}

/**
 * @name: IQC-采购质量-检验配置
 * @description: 删除
 * @return { Promise }
 */
export const DeleteWmQcItem = (matCode) => {
  return request({
    url: `/mes_api/v1/wmQcItem/${matCode}/delete`,
    method: 'DELETE'
  })
}

/**
 * @name: IQC-采购质量-检验配置
 * @description: 列表
 * @return { Promise }
 */
export const GetWmQcItemPages = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcItem/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @name: IQC-采购质量-检验配置
 * @description: 根据物料编号查询质检项
 * @return { Promise }
 */
export const GetWmQcItemByMat = (matCode = '') => {
  return request({
    url: `/mes_api/v1/wmQcItem/${matCode}/getQcItems`,
    method: 'GET'
  })
}

/**
 * @name: IQC-采购质量-质检记录
 * @description: 获取质检记录
 * @return { Promise }
 */
export const GetwmQcRecord = (params = {}, data = {}) => {
  return request({
    url: `/mes_api/v1/wmQcRecord/pages`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @name: IQC-采购质量-来料质检
 * @description: 获取待质检清单
 * @return { Promise }
 */
export const GetwmQcTask = (params = {}, data = {}) => {
  return request({
    url: `/mes_api/v1/wmQcTask/pages`,
    method: 'POST',
    params,
    data
  })
}

/**
 * @name: IQC-采购质量-来料质检
 * @description: 质检评审
 * @return { Promise }
 */
export const SavewmQcRecord = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmQcRecord/save',
    method: 'POST',
    data
  })
}

/**
 * @name: IQC-采购质量-来料质检
 * @description: 通过质检单号查询详情
 * @return { Promise }
 */
export const GetWmQcRecordByQcNo = (qcNo) => {
  return request({
    url: `/mes_api/v1/wmQcRecord/${qcNo}}`,
    method: 'GET'
  })
}

/**
 * @name: IQC-采购质量-质检记录
 * @description: 撤销质检
 * @return { Promise }
 */
export const RevokeWmQcRecordById = (id) => {
  return request({
    url: `/mes_api/v1/wmQcRecord/${id}/revoke`,
    method: 'GET'
  })
}

/**
 * @description: 重新扣减
 * @return { Promise }
 */
export const DeductAgainProcess = (params = {}) => {
  return request({
    url: `/mes_api/v1/trcProcessDocument/reduce`,
    method: 'GET',
    params
  })
}

// 叫料配置
/**
 * @description: 查询
 * @return { Promise }
 */
export const GetCallmaterialSettingByLineData = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmCallMat/pages',
    method: 'POST',
    params,
    data
  })
}

/**
 * @description: 查询详情
 * @return { Promise }
 */
export const GetCallmaterialSettingInfo = (id) => {
  return request({
    url: `/mes_api/v1/wmCallMat/${id}`,
    method: 'GET'
  })
}

/**
 * @description: 新增配置
 * @return { Promise }
 */
export const AddCallmaterialSettingByLineItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmCallMat/add',
    method: 'POST',
    data
  })
}

/**
 * @description: 编辑配置
 * @return { Promise }
 */
export const EditCallmaterialSettingByLineItem = (data = {}) => {
  return request({
    url: '/mes_api/v1/wmCallMat/edit',
    method: 'PUT',
    data
  })
}

/**
 * @description: 删除
 * @return { Promise }
 */
export const DeleteCallmaterialData = (ids) => {
  return request({
    url: `/mes_api/v1/wmCallMat/${ids}/delete`,
    method: 'DELETE'
  })
}

/**
 * @description: 通过关联工厂查找对应的目的仓库
 * @return { Promise }
 */
export const GetWhcodeByFcCode = (params = {}) => {
  return request({
    url: `/mes_api/v1/wmCallMat/getRelationWhCode`,
    method: 'GET',
    params
  })
}

/**
 * @description: 一键修改工单状态
 * @return { Promise }
 */
export const EditPlanOrderStatus = (params = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/batchEditOpStatus`,
    method: 'GET',
    params
  })
}

/**
 * @description: 切换工单工序状态
 * @return { Promise }
 */
export const ChangeOrderProcessStatus = (params = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/editOpStatus`,
    method: 'GET',
    params
  })
}
export const queryLedgerV2 = (data = {}) => {
  return request({
    url: `/mes_api/v1/wmQuant/queryLedgerV2`,
    method: 'POST',
    data
  })
}
export const importOrderByExcelV2 = (data = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/importOrderByExcelV2`,
    method: 'POST',
    data
  })
}

export const planOrderpages = (data = {}) => {
  return request({
    url: `/mes_api/v1/productionPlanMaster/pages`,
    method: 'POST',
    data
  })
}

// 模拟层级表格数据的 planOrderpagesM 函数
export const planOrderpagesM = (data = {}, params = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/pages`,
    method: 'POST',
    data,
    params
  })
}

export const importBom = (params = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/importBom`,
    disassemble: false,
    method: 'GET',
    params
  })
}
export const getQuantAssistant = (params = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/getQuantAssistant`,
    disassemble: false,
    method: 'GET',
    params
  })
}

export const publishOrderByProdCode = (data = {}) => {
  return request({
    url: `/mes_api/v1/planOrder/publishOrderByProdCode`,
    disassemble: false,
    method: 'POST',
    data
  })
}


export const modifyTempRouting = (data = {}, orderNo) => {
  return request({
    url: `/mes_api/v1/graphicalConfig/modifyTempRouting?orderNo=${orderNo}`,
    disassemble: false,
    method: 'POST',
    data
  })
}

export const editProRouting = (data = {}, orderNo) => {
  return request({
    url: `/mes_api/v1/planOrder/editProRouting?orderNo=${orderNo}`,
    disassemble: false,
    method: 'PUT',
    data
  })
}

/**
 * 
 * @param {*} params 
 * @returns 
 */
export const productionSchedule = (params = {}) => {
  return request({
    url: `/mes_api/v1/productionSchedule/production`,
    disassemble: false,
    method: 'GET',
    params
  })
}

export const productionScheduleplanorde = (params = {}) => {
  return request({
    url: `/mes_api/v1/productionSchedule/plan-order`,
    disassemble: false,
    method: 'GET',
    params
  })
}

export const matMaterialMasterimportMat = (data) => {
  return request({
    url: `/mes_api/v1/matMaterialMaster/importMat`,
    disassemble: false,
    method: 'POST',
    data
  })
}

export const importBomByExcel = (data) => {
  return request({
    url: `/mes_api/v1/matMaterialBom/importBomByExcel`,
    disassemble: false,
    method: 'POST',
    data
  })
}



