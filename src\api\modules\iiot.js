import request from '@/common/request.js'

//查询OPC UA数据
export const GetOpcUaData = (params = {}, data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotOpcuaConn/pages',
    method: 'post',
    params,
    data
  })
}
//新增OPC UA数据
export const AddOpcUaData = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotOpcuaConn/save',
    method: 'post',
    data
  })
}

//编辑OPC UA数据
export const EditOpcUaData = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotOpcuaConn/edit',
    method: 'PUT',
    data
  })
}

// 删除OPC UA数据
export const delOpcUaItem = (data) => {
  return request({
    url: `/iiot_api/v1/iiotOpcuaConn/${data.ids}/delete`,
    method: 'DELETE'
  })
}

// 测试OPC UA数据
export const TestOpcUaConn = (data) => {
  return request({
    url: `/iiot_api/v1/iiotOpcuaConn/connTest`,
    method: 'POST',
    showLoading: true,
    data
  })
}

//通知消息

// 获取通知消息
export const GetIiotEventNotify = (params = {}, data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotEventNotify/pages',
    method: 'POST',
    params,
    data
  })
}

//查询详情
export const GetIiotEventNotifyInfo = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotEventNotify/getNotifyDetail',
    method: 'POST',
    showLoading: true,
    data
  })
}

//保存通知消息
export const SaveIiotEventNotify = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotEventNotify/save',
    method: 'POST',
    data
  })
}

//编辑通知消息
export const EditIiotEventNotify = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotEventNotify/edit',
    method: 'PUT',
    data
  })
}

// 删除通知消息
export const DelIiotEventNotify = (ids) => {
  return request({
    url: `/iiot_api/v1/iiotEventNotify/${ids}/delete`,
    method: 'DELETE'
  })
}

// 设备配置
// 获取设备配置
export const GetIiotDeviceNotify = (params = {}, data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotDevice/pages',
    method: 'POST',
    params,
    data
  })
}

//查询设备配置详情
export const GetIiotDeviceNotifyInfo = (data = {}) => {
  return request({
    url: `/iiot_api/v1/iiotDevice/getDeviceDetail/${data.devCode}`,
    method: 'GET',
    showLoading: true
  })
}

//保存设备配置
export const SaveIiotDeviceNotify = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotDevice/save',
    method: 'POST',
    data
  })
}

//编辑设备配置
export const EditIiotDeviceNotify = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotDevice/edit',
    method: 'PUT',
    data
  })
}

// 删除设备配置
export const DelIiotDeviceNotify = (id) => {
  return request({
    url: `/iiot_api/v1/iiotDevice/${id}/delete`,
    method: 'DELETE'
  })
}

//启用设备配置
export const OpenIiotDeviceNotify = (data = {}) => {
  return request({
    url: '/iiot_api/v1/iiotDevice/switchDevice',
    method: 'PUT',
    showLoading: true,
    data
  })
}
