import request from '@/common/request.js'

/**
 * 保存 BOM
 * @param {Object} data BOM 数据
 * @returns {Promise}
 */
export function saveBom(data) {
    return request({
        url: '/mes_api/v1/matMaterialBomV2/save',
        method: 'post',
        data
    })
}

/**
 * 获取 BOM 树形数据
 * @returns {Promise}
 */
export function getBomTree(data) {
    return request({
        url: '/mes_api/v1/matMaterialBomV2/queryAll',
        method: 'post',
        data
    })
}

/**
 * 更新 BOM
 * @param {Object} data BOM 数据
 * @returns {Promise}
 */
export function updateBom(data) {
    return request({
        url: `/mes_api/v1/matMaterialBomV2/update`,
        method: 'put',
        data
    })
}

/**
 * 删除 BOM
 * @param {Object} data BOM 数据
 * @returns {Promise}
 */
export function deleteBom(data) {
    return request({
        url: `/mes_api/v1/matMaterialBomV2/delete`,
        method: 'delete',
        data
    })
}

export function getBomItemList(data) {
    return request({
        url: `/mes_api/v1/matMaterialBomItem/pagesV2`,
        method: 'post',
        data
    })
}

export function importOrderByExcel(data) {
    return request({
        url: `/mes_api/v1/planOrder/importOrderByExcel`,
        method: 'post',
        data
    })
}

export function queryWorkOrderSummary(params, data) {
    return request({
        url: `/mes_api/v1/planOrder/queryWorkOrderSummary`,
        method: 'post',
        params,
        data
    })
}








