import dictionary from './dictionary'

const dict = {}
for (const key in dictionary) {
  dict[key] = dictionary[key]
  dict[`fmt_${key}`] = function () {
    let value = ''
    if (arguments.length === 4) {
      //来自el-table formatter
      value = arguments[2]
    } else {
      // 可以传入一个对象，也可以传入一个value值
      if (typeof arguments[0] === 'object') {
        value = arguments[0][key] || ''
      } else {
        value = arguments[0] || ''
      }
    }
    const fd = dictionary[key].find((i) => i.value == value)
    if (fd) {
      return fd.label
    } else {
      console.warn(`字典: [${key}:${value}]没有找到对应选项！`)
      return value
    }
  }
}
export default dict
