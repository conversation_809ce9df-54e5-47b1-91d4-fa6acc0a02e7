import request from '@/common/request.js'

//查询盘点单列表
export const fetchGetInventoryList = (data, params) => {
  return request({
    url: '/mes_api/v1/wmPhyinvOrder/pages',
    method: 'post',
    params,
    data
  })
}

//查询库存清单
export const fetchGetInventoryStockList = (data, params) => {
  return request({
    url: '/mes_api/v1/wmQuant/paginStorage',
    method: 'post',
    params,
    data
  })
}

//添加待盘点库存
export const fetchAddInventoryStock = (data) => {
  return request({
    url: '/mes_api/v1/wmPhyinvItem/save',
    method: 'post',
    data
  })
}

//查询待盘点库存清单
export const fetchGetPendingInventoryStockList = (data, params) => {
  return request({
    url: '/mes_api/v1/wmPhyinvItem/paginToPhy',
    method: 'post',
    params,
    data
  })
}

//提交或者保存盘点单
export const fetchSaveInventory = (data) => {
  return request({
    url: '/mes_api/v1/wmPhyinvDeal/saveWmPhyinv',
    method: 'post',
    skip: true,
    data
  })
}

//查询盘点阶段
export const fetchGetInventoryPhaseData = (data) => {
  return request({
    url: '/mes_api/v1/wmPhyinvPhase/pages',
    method: 'post',
    data
  })
}

//编辑或者详情的时候查询库存清单
export const fetchGetEditAndDetailInventoryStockList = (params, data) => {
  return request({
    url: '/mes_api/v1/wmPhyinvItem/paginPhy',
    method: 'post',
    params,
    data
  })
}

//确认编辑盘点计划
export const fetchConfirmEditInventoryPlan = (data) => {
  return request({
    url: '/mes_api/v1/wmPhyinvDeal/updateWmPhyinv',
    method: 'post',
    skip: true,
    data
  })
}

//批量移除待盘点库存
export const fetchBatchRemovePendingInventoryStock = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvItem/${data}/delete`,
    method: 'DELETE'
  })
}

//盘点计划提交
export const fetchSubmitInventoryPlan = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvDeal/${data}/submitWmPhyinv`,
    method: 'put'
  })
}

//盘点计划关闭
export const fetchCloseInventoryPlan = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvDeal/${data}/closeWmPhyinv`,
    method: 'put'
  })
}

//新增盘盈
export const fetchAddInventoryProfit = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvItem/savePhyProfit`,
    method: 'post',
    data
  })
}

//保存盘点执行
export const fetchSaveInventoryExecute = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvDeal/executeWmPhyinv`,
    method: 'post',
    data
  })
}

//查询差异库存
export const fetchGetDiffList = (data, params) => {
  return request({
    url: `/mes_api/v1/wmPhyinvItem/paginDiffPhy`,
    method: 'post',
    params,
    data
  })
}

//差异处理
export const fetchDiffHandle = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvDeal/diffHandleWmPhyinv`,
    method: 'post',
    data
  })
}

//新增盘盈移除sn码
export const fetchUpdateSnNo = (data) => {
  return request({
    url: `/mes_api/v1/wmPhyinvItem/update`,
    method: 'put',
    data
  })
}

//正常盘点移除sn码
export const fetchUpdateSnNoNormal = (data) => {
  return request({
    url: `/mes_api/v1/wmStorageUnit/saveOrDelSnNos`,
    method: 'post',
    data
  })
}

//查询是否启用有效期管理
export const fetchGetIsEnableExpire = (data) => {
  return request({
    url: `/mes_api/v1/wmMat/pages`,
    method: 'post',
    data
  })
}

//是否启用批次属性
export const fetchGetIsEnableBatch = (params) => {
  return request({
    url: `/mes_api/v1/matMaterialBatch/getMatBatch`,
    method: 'get',
    params
  })
}

//生产批次号
export const fetchGetBatchNo = (data) => {
  return request({
    url: `/mes_api/v1/matMaterialBatch/getBatchCode`,
    method: 'post',
    data
  })
}
