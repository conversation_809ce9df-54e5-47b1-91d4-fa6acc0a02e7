// https://vitejs.dev/config/
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import { svgBuilder } from './src/plugins/svg-render'
import UnoCSS from 'unocss/vite'

// 服务器配置 - 支持环境变量
const mainip = '**************'

// 服务配置对象 - 统一管理所有微服务的端口和路径重写规则
const services = {
  api: { port: 9099, rewrite: null },           // 主API服务
  mes_api: { port: 9095, rewrite: '/api' },     // MES制造执行系统
  andon_api: { port: 9096, rewrite: '/api' },   // Andon安灯系统
  oee_api: { port: 9097, rewrite: '/api' },     // OEE设备综合效率
  tpm_api: { port: 9098, rewrite: '/api' },     // TPM全面生产维护
  printer_api: { port: 8001, rewrite: '/api' }, // 打印服务
  trace_api: { port: 9100, rewrite: '/api' },   // 追溯系统
  pb_api: { port: 9102, rewrite: '/api' },      // 问题管理系统
  docu_api: { port: 9103, rewrite: '/api' },    // 文档管理系统
  shift_api: { port: 9104, rewrite: '/api' },   // 班次管理系统
  iiot_api: { port: 9105, rewrite: '/api' },    // 工业物联网
  audit_api: { port: 9106, rewrite: '/lya' },   // 审计系统
  bpmn_api: { port: 8087, rewrite: '/api' }     // 工作流引擎
}

// 创建代理配置的函数
const createProxyConfig = (services, baseIP) => {
  const proxy = {}

  Object.entries(services).forEach(([apiPrefix, config]) => {
    const { port, rewrite } = config
    const target = `http://${baseIP}:${port}`

    proxy[`/${apiPrefix}`] = {
      target,
      changeOrigin: true,
      ...(rewrite && { rewrite: (path) => path.replace(new RegExp(`^/${apiPrefix}`), rewrite) })
    }
  })

  return proxy
}
export default defineConfig({
  base: './',
  // 本地运行配置，及反向代理配置
  server: {
    host: '0.0.0.0',
    cors: true,
    port: 5184,
    open: true,
    proxy: createProxyConfig(services, mainip)
  },
  // 打包配置
  build: {
    target: 'modules',
    outDir: 'dist', //指定输出路径
    assetsDir: 'assets', // 指定生成静态资源的存放路径
    minify: 'terser' // 混淆器，terser构建后文件体积更小
  },
  plugins: [
    vue(),
    svgBuilder('./src/icons/'),
    UnoCSS()
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";@import "@/styles/mixin.scss";`
      }
    }
  }
})
