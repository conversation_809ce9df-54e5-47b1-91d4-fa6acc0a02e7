<script>
import Silder from './components/silder/index.vue'
import Navbar from './components/navbar/index.vue'
import Appmain from './components/appmain/index.vue'
import Tabview from './components/tabview/index.vue'
export default {
  components: {
    Silder,
    Navbar,
    Tabview,
    Appmain
  },
  setup() {}
}
</script>

<template>
  <div class="layout">
    <div class="layout-sider"><Silder /></div>
    <div class="layout-contrainer">
      <div class="layout-navbar"><Navbar /></div>
      <!-- <div class="layout-breadcrumb"><Breadcrumb /></div> -->
      <div class="layout-tabview"><Tabview /></div>
      <div class="layout-main"><Appmain /></div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.layout {
  display: flex;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  background-color: $layout-background;
  padding: $base-margin;
}
.layout {
  .layout-sider {
    width: 200px;
    height: 100%;
    flex-shrink: 0;
  }
  .layout-contrainer {
    flex: 1;
    width: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}
.layout {
  .layout-navbar {
    width: 100%;
    flex-shrink: 0;
  }
  .layout-breadcrumb {
    width: 100%;
    flex-shrink: 0;
  }
  .layout-tabview {
    width: 100%;
    margin: 5px 0 0 0;
    flex-shrink: 0;
  }
  .layout-main {
    flex: 1;
    width: 100%;
    height: 0;
  }
}
</style>
