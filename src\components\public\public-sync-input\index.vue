<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: '表单输入'
  },
  fields: {
    type: Array,
    default: () => []
    // default: () => [{ field: 'name', label: '名称', placeholder: '请输入名称' }]
  },
  submit: {
    type: Function,
    default: null
  },
  cancel: {
    type: Function,
    default: null
  }
})
const form = ref(null)
const show = ref(true)

function handleSubmit() {
  show.value = false
  setTimeout(() => {
    props.submit && props.submit(form.value)
  }, 200)
}

function handleCancel() {
  show.value = false
  setTimeout(() => {
    props.cancel && props.cancel()
  }, 200)
}

onMounted(() => {
  nextTick(() => {
    form.value = props.fields.reduce((acc, cur) => {
      acc[cur.field] = ''
      return acc
    }, {})
  })
  show.value = true
})
</script>

<template>
  <el-dialog :title="props.title" v-model="show" width="400px" :close-on-click-modal="false">
    <el-form @submit.native.prevent label-width="auto" label-position="left" v-if="form">
      <el-form-item :label="field.label" v-for="field in props.fields" :key="field.field">
        <el-input v-model="form[field.field]" :placeholder="field.placeholder" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center items-center">
        <el-button @click="handleCancel()">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
