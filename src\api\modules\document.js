// 文档管理模块
import request from '@/common/request.js'

/**
 * 查询文档管理页面数据
 *
 * @param {Object} params - 分页与查询参数
 * @param {Object} data - 请求体数据
 * @returns {Promise} - 异步请求结果
 */
export const DocuManageQueryPage = (params, data) => {
  return request({
    url: '/docu_api/docuManage/queryPage',
    method: 'POST',
    params,
    data
  })
}

/**
 * 新增文档
 *
 * @param {object} data - 添加所需的数据对象。
 * @returns {Promise} 包含服务器响应的Promise对象。
 */
export const DocuManageAdd = (data) => {
  request({
    url: '/docu_api/docuManage/add',
    method: 'POST',
    data
  })
}

/**
 * 调用文档管理编辑接口
 *
 * @param {Object} data - 编辑文档所需的数据
 * @returns {Promise} - 请求结果的Promise对象
 */
export const DocuManageEdit = (data) => {
  request({
    url: '/docu_api/docuManage/edit',
    method: 'POST',
    data
  })
}

/**
 * 添加文档版本
 *
 * @param {Object} data - 包含添加文档版本所需信息的数据对象。
 * @returns {Promise} - 处理请求结果的Promise对象。
 */
export const DocuManageAddVersion = (data) => {
  request({
    url: '/docu_api/docuManage/addVersion',
    method: 'POST',
    data
  })
}

/**
 * 查询文档管理详情
 *
 * @param {Object} params - 请求参数对象
 * @returns {Promise} - 解析后的API响应数据
 */
export const DocuManageQueryDetail = (params) => {
  return request({
    url: '/docu_api/docuManage/queryDetail',
    method: 'POST',
    params
  })
}

// /api/docuManage/delete
export const DocuManageDelete = (params) => {
  request({
    url: '/docu_api/docuManage/delete',
    method: 'DELETE',
    params
  })
}

// /api/docuManage/queryUpload
export const DocuManageQueryUpload = (params) => {
  return request({
    url: '/docu_api/docuManage/queryUpload',
    method: 'POST',
    params
  })
}
