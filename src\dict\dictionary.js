export default {
  sex: [
    {
      label: '男',
      value: 1
    },
    {
      label: '女',
      value: 2
    }
  ],
  // 设备状态
  devStatus: [
    {
      label: '使用中',
      value: '1'
    },
    {
      label: '闲置',
      value: '2'
    },
    {
      label: '报废',
      value: '3'
    }
  ],
  // 工单状态
  orderStatus: [
    {
      label: '待接单',
      value: '1'
    },
    {
      label: '待维修',
      value: '2'
    },
    {
      label: '待验收',
      value: '3'
    },
    {
      label: '已验收',
      value: '4'
    }
  ],
  // 保养周期
  upkeepWeek: [
    {
      label: '月',
      value: 3
    },
    {
      label: '周',
      value: 2
    },
    {
      label: '天',
      value: 1
    }
  ],
  // 安灯级别
  andonLevel: [
    {
      label: '一级',
      value: 1
    },
    {
      label: '二级',
      value: 2
    },
    {
      label: '三级',
      value: 3
    }
  ],
  // 安灯状态
  andonStatus: [
    {
      label: '待接单',
      value: '1'
    },
    {
      label: '待处理',
      value: '2'
    },
    {
      label: '待验收',
      value: '3'
    },
    {
      label: '完成',
      value: '4'
    }
  ],
  // 是否
  statusList: [
    {
      label: '是',
      value: '1'
    },
    {
      label: '否',
      value: '0'
    }
  ],
  // 生产工单状态
  prodOrderStatus: [
    {
      label: '未开始',
      value: 1
    },
    {
      label: '进行中',
      value: 2
    },
    {
      label: '已完成',
      value: 3
    }
  ],
  // 结果类型
  resultType: [
    {
      label: 'OK/NG',
      value: 'OK/NG'
    },
    {
      label: '手动输入',
      value: '手动输入'
    }
  ],
  //  任务类型
  taskTypeList: [
    {
      label: '加工',
      value: 1
    },
    {
      label: '合包',
      value: 2
    }
  ],
  // 移库类型
  tranferWareHouseTypeList: [
    {
      label: '托盘',
      value: 'PALLET'
    },
    {
      label: '跟踪号',
      value: 'SUNO'
    },
    {
      label: '散件',
      value: 'SPARE'
    }
  ],
  // 过程检验类型
  reviewTypeList: [
    {
      label: '首检',
      value: 0
    },
    {
      label: '末检',
      value: 1
    },
    {
      label: '抽检',
      value: 2
    }
  ],
  reviewResultList: [
    {
      label: '合格',
      value: 1
    },
    {
      label: '不合格',
      value: 2
    }
  ],
  isInspectList: [
    {
      label: '是',
      value: true
    },
    {
      label: '否',
      value: false
    }
  ],
  // 质检状态
  taskList: [
    {
      label: '待执行',
      value: 'UNEXECUTED'
    },
    {
      label: '已执行',
      value: 'EXECUTED'
    }
  ],
  // 质检状态
  qcResultList: [
    {
      label: 'OK',
      value: 'OK'
    },
    {
      label: 'NG',
      value: 'NG'
    }
  ],
  // 采购订单状态
  purchaseOrderStatusList: [
    {
      label: '待收货',
      value: '1'
    },
    {
      label: '部分收货',
      value: '2'
    },
    {
      label: '完成',
      value: '3'
    }
  ],
  mixstkControl: [
    {
      label: '允许混产品',
      value: '1'
    },
    {
      label: '允许混批次',
      value: '2'
    },
    {
      label: '允许混产品 & 批次',
      value: '4'
    },
    {
      label: '不允许混放',
      value: '3'
    }
  ],
  //入库单状态
  stockinStatusList: [
    {
      label: '待上架',
      value: 'BE_SHELF'
    },
    {
      label: '取消上架',
      value: 'CANCLEONSHELF'
    },
    {
      label: '待质检',
      value: 'BE_INSPECTED'
    },
    {
      label: '完成',
      value: 'COMPLETED'
    }
  ],
  // 出库入库禁用标识Boolean
  binInBlockList: [
    {
      label: '是',
      value: true
    },
    {
      label: '否',
      value: false
    }
  ],
  //入库单状态
  outBoundStatusList: [
    {
      label: '待拣货',
      value: '1'
    },
    {
      label: '部分拣货',
      value: '2'
    },
    {
      label: '完成',
      value: '3'
    }
  ],
  outOrderStatusList: [
    {
      label: '保存',
      value: '1'
    },
    {
      label: '提交',
      value: '2'
    },
    {
      label: '关闭',
      value: '3'
    }
  ],
  //拣货单据状态
  pickOrderStatus: [
    {
      label: '新建',
      value: '1'
    },
    {
      label: '关闭',
      value: '2'
    }
  ],
  // 成品入库状态
  shopprodinOrderStatusList: [
    {
      label: '保存',
      value: 'SAVE'
    },
    {
      label: '提交',
      value: 'SUBMIT'
    }
  ],
  //跟踪号类型
  unitTypeList: [
    {
      label: '散件',
      value: 0
    },
    {
      label: '包',
      value: 2
    },
    {
      label: 'SN',
      value: 3
    }
  ],

  //跟踪号类型对象
  unitTypeObj: {
    0: '散件',
    2: '包',
    3: 'SN'
  },

  //采购单类型
  purchaseOrderStateList: [
    {
      label: '已确认',
      value: 'CONFIRMED'
    },
    {
      label: '未确认',
      value: 'UNCONFIRM'
    }
  ],
  deliveOrderList: [
    {
      label: '已发货',
      value: 'TO_RECEIVED'
    },
    {
      label: '待发货',
      value: 'TO_DELIVERY'
    },
    {
      label: '已关闭',
      value: 'CLOSE'
    }
  ],
  //委外单据状态
  outSourceOrderStatus: [
    {
      label: '新建',
      value: '1'
    },
    {
      label: '提交',
      value: '2'
    }
  ],

  //容器类型数组
  containerTypeList: [
    {
      label: '箱',
      value: 2
    },
    {
      label: '托',
      value: 1
    }
  ],

  //容器类型对象
  containerTypeObj: {
    2: '箱',
    1: '托',
    4: '不合格品编号'
  },
  //交货报告字典
  deliveryTypeList: [
    {
      label: '是',
      value: true
    },
    {
      label: '否',
      value: false
    }
  ],
  // 库存属性状态
  wmsStatusList: [
    {
      label: '保存',
      value: 0
    },
    {
      label: '提交',
      value: 1
    }
  ],
  //通知对象类型
  msgObjectList: [
    {
      label: 'HTTP',
      value: 'HTTP'
    },
    {
      label: 'EMAIL',
      value: 'EMAIL'
    }
  ],
  isEnableList: [
    {
      label: '是',
      value: 1
    },
    {
      label: '否',
      value: 0
    }
  ],
  methodList: [
    {
      label: 'POST',
      value: 'POST'
    },
    {
      label: 'GET',
      value: 'GET'
    }
  ],
  iiotDataType: [
    {
      label: '布尔类型',
      value: 'Boolean'
    },
    {
      label: '字符类型',
      value: 'String'
    },
    {
      label: '整数类型',
      value: 'Short'
    },
    {
      label: '小数类型',
      value: 'Float'
    }
  ],
  levelList: [
    {
      label: '一级',
      value: 1
    },
    {
      label: '二级',
      value: 2
    },
    {
      label: '三级',
      value: 3
    },
    {
      label: '四级',
      value: 4
    },
    {
      label: '五级',
      value: 5
    },
    {
      label: '六级',
      value: 6
    },
    {
      label: '七级',
      value: 7
    },
    {
      label: '八级',
      value: 8
    },
    {
      label: '九级',
      value: 9
    }
  ],
  // 点检周期
  checkWeek: [
    { label: '天', value: 1 },
    { label: '周', value: 2 },
    { label: '月', value: 3 },
    { label: '班', value: 4 }
  ],
  //检测类型
  checkTypeList: [
    {
      label: '开班点检',
      value: 1
    },
    {
      label: '班后点检',
      value: 2
    }
  ]
}
