import request from '@/common/request.js'

/**
 * @name oee统计报表查询
 * @param {import('../models/oee-tong-ji-bao-biao-cha-xun-tiao-jian').OeeTongJiBaoBiaoChaXunTiaoJian} data
 * @returns {Promise<(import('../models/oee-hui-zong-kan-ban').OeeHuiZongKanBan)[]>}
 */
export const countBoardGetCountUsingPOST = (data) => {
  return request({
    url: '/oee_api/v1/countBoard/getCount',
    method: 'POST',
    data
  })
}

/**
 * @name oee统计节拍报表查询
 * @param {import('../models/oee-tong-ji-bao-biao-cha-xun-tiao-jian').OeeTongJiBaoBiaoChaXunTiaoJian} data
 * @returns {Promise<(import('../models/oee-jie-pai-hui-zong-kan-ban').OeeJiePaiHuiZongKanBan)[]>}
 */
export const countCtGetCountUsingPOST = (data) => {
  return request({
    url: '/oee_api/v1/countCT/getCount',
    method: 'POST',
    data
  })
}

/**
 * @name oee统计班次产量报表查询
 * @param {import('../models/oee-tong-ji-bao-biao-cha-xun-tiao-jian').OeeTongJiBaoBiaoChaXunTiaoJian} data
 * @returns {Promise<(import('../models/oee-chan-pin-hui-zong-kan-ban').OeeChanPinHuiZongKanBan)[]>}
 */
export const countProGetCountUsingPOST = (data) => {
  return request({
    url: '/oee_api/v1/countPro/getCount',
    method: 'POST',
    data
  })
}

/**
 * @name oee统计停机报表查询
 * @param {import('../models/oee-tong-ji-bao-biao-cha-xun-tiao-jian').OeeTongJiBaoBiaoChaXunTiaoJian} data
 * @returns {Promise<(import('../models/oee-chan-pin-hui-zong-kan-ban1').OeeChanPinHuiZongKanBan1)[]>}
 */
export const stopCountGetCountUsingPOST = (data) => {
  return request({
    url: '/oee_api/v1/stopCount/getCount',
    method: 'POST',
    data
  })
}

/**
 * @name oee统计停机原因查询
 * @param {import('../models/oee-tong-ji-bao-biao-cha-xun-tiao-jian').OeeTongJiBaoBiaoChaXunTiaoJian} data
 * @returns {Promise<(import('../models/oee-chan-pin-hui-zong-kan-ban1').OeeChanPinHuiZongKanBan1)[]>}
 */
export const stopReasonGetCountUsingPOST = (data) => {
  return request({
    url: '/oee_api/v1/stopCount/getReadCount',
    method: 'POST',
    data
  })
}
