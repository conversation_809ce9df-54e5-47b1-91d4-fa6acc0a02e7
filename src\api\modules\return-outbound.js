import request from '@/common/request.js'

// 查询退货出库清单
export function queryReturnOutboundList(data) {
    return request({
        url: '/mes_api/v1/wmPurchoutOrder/orderPage',
        method: 'POST',
        data
    })
}
// 根据退货出库单号查询物料明细
export function queryReturnOutboundDetail(data) {
    return request({
        url: '/mes_api/v1/wmPurchoutItem/itemPage',
        method: 'POST',
        data
    })
}
// 退货需求提交与保存
export function submitReturnOutbound(data) {
    return request({
        url: '/mes_api/v1/wmPurchoutOrder/confirm',
        method: 'post',
        data
    })
}

// 根据id获取采购出库单详情
export function getReturnDetailById(params) {
    return request({
        url: '/mes_api/v1/wmPurchoutOrder/detail',
        method: 'GET',
        params
    })
}

// 关闭采购出库单
export function closeReturnOutbound(params) {
    return request({
        url: '/mes_api/v1/wmPurchoutOrder/close',
        method: 'GET',
        params
    })
}

// 退货出库编辑
export function editReturnOutbound(data) {
    return request({
        url: '/mes_api/v1/wmOthstkoutOrder/updateOthstkout',
        method: 'post',
        data
    })
}
// 校验退货出库对象数量是否正确
export function checkOtherOutbound(data) {
    return request({
        url: '/mes_api/v1/wmOthstkoutOrder/checkOtherOutBound',
        method: 'post',
        data,
        skip: true
    })
}

// 编辑——退货出库添加物料明细
export function returnDetailAddMaterial(data) {
    return request({
        url: `/mes_api/v1/wmPurchoutItem/add`,
        method: 'POST',
        data
    })
}

// 编辑——退货出库删除物料明细
export function returnDetailDeleteMaterial(data) {
    return request({
        url: `/mes_api/v1/wmPurchoutItem/del`,
        method: 'DELETE',
        data
    })
}

// 确认出库
export function outbountConfirm(data) {
    return request({
        url: `/mes_api/v1/wmPurchoutItem/confirm`,
        method: 'POST',
        data
    })
}