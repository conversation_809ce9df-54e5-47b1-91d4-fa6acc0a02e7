import request from '@/common/request.js'

// 合作伙伴
/*
 *  获取合作伙伴列表
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const GetPartnerList = (params = {}, data = {}) => {
    return request({
        url: '/api/v1/masterPartner/pages',
        method: 'POST',
        params: params,
        data: data
    })
}
/*
 *  新增合作伙伴
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const AddPartner = (data = {}) => {
    return request({
        url: '/api/v1/masterPartner/save',
        method: 'POST',
        data,
    })
}
/*
 *  编辑合作伙伴
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
export const EditPartner = (data = {}) => {
    return request({
        url: '/api/v1/masterPartner/update',
        method: 'PUT',
        data,
    })
}
/*
 *  删除合作伙伴
 *  @param { params, data: Object }
 *  @returns { Promise }
 */

export const DeletePartner = (ids = '') => {
    return request({
        url: `/api/v1/masterPartner/${ids}/delete`,
        method: 'DELETE',
    })
}