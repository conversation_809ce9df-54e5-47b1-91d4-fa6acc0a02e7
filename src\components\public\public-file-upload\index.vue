<template>
  <div class="public-file-upload">
    <div v-for="(item, index) in modelValue" :key="index" :title="item.fileName" class="file-item" @click="download(item)">
      <svg-icon name="attachment" size="35" class="primary attachment" />
      <div class="file-info">
        <p class="file-name">{{ item.fileName || item.filePath }}</p>
        <span class="file-size">{{ item.fileSize ? formatFileSize(item.fileSize) : '' }}</span>
      </div>
      <el-popconfirm title="是否确认删除?" @confirm="modelValue.splice(index, 1)" v-if="!disabled">
        <template #reference>
          <svg-icon name="delete" size="16" class="del" @click.stop />
        </template>
      </el-popconfirm>
    </div>
    <div class="file-item add" @click="upload(item)" v-if="!disabled && modelValue.length < limit" v-loading="uploading">
      <svg-icon name="add" size="20" class="primary" />
      <span>上传附件</span>
    </div>
    <span style="color: #999" v-if="disabled && modelValue && modelValue.length === 0">暂无文件</span>
  </div>
</template>
<script setup>
import { computed, shallowRef } from 'vue'
import { uploadFileEasy } from '@/api/modules/device'
import { ElMessage } from 'element-plus'

const emits = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  modelValue: {
    type: Array,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 5
  }
})
const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
    emits('change', val)
  }
})
const uploading = shallowRef(false)

function upload() {
  if (props.disabled) return
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '*'
  input.onchange = (e) => {
    if (e.target.files.length === 0) return
    const file = e.target.files[0]
    uploading.value = true
    uploadFileEasy(file)
      .then((res) => {
        uploading.value = false
        modelValue.value.push(res[0])
        ElMessage.success('上传成功')
      })
      .catch(() => {
        uploading.value = false
        ElMessage.error('上传失败')
      })
    // clear
    e.target.value = ''
    input.remove()
  }
  input.click()
}

function download(item) {
  window.open(item.filePath)
}

function formatFileSize(size) {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / 1024 / 1024).toFixed(2) + 'MB'
  } else {
    return (size / 1024 / 1024 / 1024).toFixed(2) + 'GB'
  }
}

defineExpose({ upload })
</script>
<style scoped lang="scss">
.public-file-upload {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.file-item {
  margin: 5px 5px 5px 0;
  height: 50px;
  min-width: 200px;
  max-width: 400px;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  box-sizing: border-box;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    border-color: #dfdfdf;
  }

  &.add {
    color: $primary;
    font-weight: bold;
    min-width: 100px;
    border: 1px dashed #ebeef5;
    background: #f9f9f9;
  }
}

.attachment {
  padding: 4px;
  border-radius: 8px;
  background: #f7f7f7;
}

.file-info {
  font-size: 12px;
  flex: 1;
  width: 0;
  margin: 0 10px 0 10px;
}

.file-name {
  width: 100%;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  color: #666;
}

.del {
  color: red;
  &:hover {
    opacity: 0.6;
  }
}
</style>
