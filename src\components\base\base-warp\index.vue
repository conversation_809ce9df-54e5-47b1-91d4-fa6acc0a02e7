<script setup>
defineProps({
  // 是否可以滚动 内嵌el-scrollbar
  scroll: {
    type: <PERSON>olean,
    default() {
      return false
    }
  },
  // slot
  // 盒子头部文字 加粗主题色文字
  title: {
    type: String,
    default() {
      return ''
    }
  },
  // style
  // 自定义传入高度，直接到style{ height }
  height: {
    type: String,
    default() {
      return ''
    }
  },
  // class
  // 内容盒子是否为flex布局
  flex: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 开启flex后默认排列方向为 横向row, 为true时将变为竖向排列
  flexColumn: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 是否占满 。 父元素为flex时可用
  fill: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 是否开启内边距
  padding: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 边框
  margin: {
    type: String,
    default() {
      return ''
    }
  }
})
</script>

<template>
  <div class="base-warp" :class="[{ 'is-fill': fill }, { 'is-padding': padding }]" :style="[{ height: height }, { margin: margin || undefined }]">
    <div class="base-warp-content" :class="[{ 'is-flex': flex }, { 'flex-column': flexColumn }]">
      <el-scrollbar class="y" v-if="scroll && !flex">
        <base-label :title="title" v-if="title || $slots.header">
          <slot name="header" />
        </base-label>
        <slot />
      </el-scrollbar>
      <template v-else>
        <base-label :title="title" v-if="title || $slots.header">
          <slot name="header" />
        </base-label>
        <slot />
      </template>
    </div>
    <div class="base-warp-footer" v-if="$slots.footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-warp {
  @include box;
  // display: inline-block;
  overflow: hidden;
  // text-align: left;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}
.is-flex {
  display: flex;
  align-items: center;
}

.is-padding {
  padding: $base-padding;
  padding-top: $base-padding;
}

.is-fill {
  height: 0;
  flex: 1;
  flex-basis: 0;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.base-warp-content {
  flex: 1;
  height: 0;
}

.base-warp-footer {
  padding: 10px;
  text-align: center;
  border-top: 1px solid #eee;
}

.is-flex.flex-row {
  .is-fill {
    width: 0;
    height: auto;
    flex: 1;
    flex-basis: 0;
  }
}
</style>
