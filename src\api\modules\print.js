import request from '@/common/request.js'
import printJS from 'print-js'
// import FileSaver from 'file-saver'

/**
 * @name 打印!
 * @param {Object} data
 * @returns
 */
export const DoPrint = (data = {}) => {
  return request({
    url: '/printer_api/printer/print',
    method: 'POST',
    data
  })
}

/**
 * @name DoPrintLocal
 * @description 本地打印
 * @returns {File} pdf
 */
export const DoPrintLocal = (data = {}) => {
  return request({
    url: '/printer_api/printer/printLocal',
    method: 'POST',
    skip: true,
    data,
    responseType: 'blob'
  }).then((blob) => {
    printJS({
      printable: URL.createObjectURL(blob),
      type: 'pdf'
    })
  })
}

/**
 * @name DoPrintDownload
 * @description 本地打印-下载pdf
 * @returns {File} pdf
 */
export const DoPrintDownload = (data = {}) => {
  return request({
    url: '/printer_api/printer/printLocal',
    method: 'POST',
    skip: true,
    data,
    responseType: 'blob'
  }).then((blob) => {
    FileSaver.saveAs(blob, `${Number(new Date())}.pdf`)
  })
}

/**
 * @name GetTemplatePage
 * @description 获取模板分页列表
 * @param {Object} params
 * @param {Number|String} params.page
 * @param {Number|String} params.size
 * @param {String|Number} params.templateCode
 * @param {String} params.templateName
 * @param {String} params.templateType
 */
export const GetTemplatePage = (data) => {
  return request({
    url: '/printer_api/template/page',
    method: 'POST',
    data
  })
}

/**
 * @name GetTemplateById
 * @description 获取模板详情 根据模板id
 * @param {Number|String} templateCode
 */
export const GetTemplateById = (templateCode) => {
  return request({
    url: '/printer_api/template/detail',
    method: 'POST',
    data: {
      templateCode: templateCode
    }
  })
}

/**
 * @name DelTemplateById
 * @description 删除模板 根据模板id
 * @param {Number|String} templateCode
 */
export const DelTemplateById = (templateCode) => {
  return request({
    url: '/printer_api/template/delete',
    method: 'POST',
    data: {
      templateCode: templateCode
    }
  })
}

/**
 * @name GetPrinters
 * @description 获取打印机列表
 */
export const GetPrinters = () => {
  return request({
    url: '/printer_api/printer/list',
    method: 'POST'
  })
}

/**
 * @name GetPrinterStatus
 * @param {String} printerName
 * @description 获取打印机状态
 */
export const GetPrinterStatus = (printerName) => {
  return request({
    url: '/printer_api/printer/status',
    method: 'POST',
    data: {
      printerName: printerName
    }
  })
}

/**
 * @name GetTemplates
 * @description 获取模板列表
 */
export const GetTemplates = () => {
  return request({
    url: '/printer_api/template/list',
    method: 'POST'
  })
}
