<template>
  <el-dialog v-model="dialogVisible" title="数据查看" width="700" :closeOnClickModal="false">
    <template #title> JSON查看 </template>
    <el-scrollbar style="height: 500px">
      <json-viewer :value="jsonData" :expand-depth="jsonDepth" boxed sort> </json-viewer>
    </el-scrollbar>
  </el-dialog>
</template>
<script setup>
import { shallowRef } from 'vue'
import JsonViewer from 'vue-json-viewer'

const dialogVisible = shallowRef(false)
const jsonData = shallowRef(null)
const jsonDepth = shallowRef(6)

function open({ data, depth = 6 }) {
  dialogVisible.value = true
  jsonData.value = data
  jsonDepth.value = depth
}
defineExpose({
  open
})
</script>
