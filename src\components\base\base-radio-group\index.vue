<script setup>
const emit = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  }
})
const onChange = (item) => {
  emit('update:modelValue', item)
  emit('change', item)
}
</script>
<template>
  <div class="form-select-header">
    <span class="label">{{ props.title }}:</span>
    <span v-for="item in props.options" :key="item.value" class="option" :class="{ 'is-active': props.modelValue === item }" @click="onChange(item)">
      {{ item }}
    </span>
  </div>
</template>
<style lang="scss" scoped>
.form-select-header {
  margin-bottom: 20px;
  display: inline-block;

  & + & {
    margin-left: 100px;
  }

  .label {
    color: #333;
    font-size: 14px;
    font-weight: bold;
    display: inline-block;
  }

  .option {
    color: #333;
    font-size: 14px;
    margin-left: 20px;
    display: inline-block;
    cursor: pointer;
    &:hover,
    &.is-active {
      color: $primary;
    }
  }
}
</style>
