<template>
  <div 
    class="copyable-select" 
    @mouseenter="showTooltip = true" 
    @mouseleave="showTooltip = false"
    @contextmenu.prevent
  >
    <el-select
      v-bind="$attrs"
      :model-value="modelValue"
      @update:model-value="handleChange"
      class="custom-select"
      :loading="loading"
      :remote-method="handleRemoteSearch"
      @visible-change="handleVisibleChange"
    >
      <slot></slot>
    </el-select>
    <el-tooltip
      v-model:visible="showTooltip"
      placement="top"
      :hide-after="0"
      popper-class="copyable-tooltip"
      v-if="modelValue"
    >
      <template #content>
        <div class="tooltip-content" @contextmenu.prevent>
          <span class="selectable-text">{{ selectedLabel }}</span>
          <el-button
            v-if="selectedLabel"
            type="primary"
            link
            @click="copyToClipboard"
          >
            复制
          </el-button>
        </div>
      </template>
      <div class="tooltip-trigger"></div>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { debounce } from 'lodash-es'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  remoteMethod: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const showTooltip = ref(false)
const loading = ref(false)

// 计算当前选中项的标签
const selectedLabel = computed(() => {
  const option = props.options.find(opt => opt.value === props.modelValue)
  return option ? option.label : ''
})

// 处理选择变化
const handleChange = (value) => {
  emit('update:model-value', value)
  emit('change', value)
}

// 处理远程搜索
const handleRemoteSearch = debounce(async (query) => {
  if (props.remoteMethod) {
    loading.value = true
    try {
      await props.remoteMethod(query)
    } finally {
      loading.value = false
    }
  }
}, 300)

// 处理下拉框显示状态变化
const handleVisibleChange = (visible) => {
  if (visible && props.remoteMethod) {
    handleRemoteSearch('')
  }
}

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(selectedLabel.value)
    ElMessage.success('复制成功')
    showTooltip.value = false
  } catch (err) {
    ElMessage.error('复制失败')
  }
}
</script>

<style lang="scss" scoped>
.copyable-select {
  display: inline-block;
  position: relative;
  width: 100%;
  user-select: none;

  :deep(.custom-select) {
    width: 180px;
    
    .el-input {
      width: 100%;
    }

    .el-input__wrapper {
      &:hover {
        box-shadow: 0 0 0 1px var(--el-input-hover-border-color) inset;
      }
      
      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
      }
    }
  }
}

// 全局样式，确保下拉菜单样式正确应用
:deep(.el-select__popper.el-popper) {
  min-width: 180px !important;
  width: auto !important;
}

.tooltip-trigger {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

:deep(.copyable-tooltip) {
  .tooltip-content {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    min-width: 150px;
    
    .selectable-text {
      user-select: text;
      cursor: text;
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .el-button {
      padding: 2px 8px;
      font-size: 12px;
      flex-shrink: 0;
    }
  }
}
</style> 