import request from '@/common/request.js'

//查询其他出库清单
export function queryOtherOutboundList(params, data) {
  return request({
    url: '/mes_api/v1/wmOthstkoutOrder/getOrderPage',
    method: 'post',
    params,
    data
  })
}
// 根据其他出库单号查询出库单明细
export function queryOtherOutboundDetail(params) {
  return request({
    url: '/mes_api/v1/wmOthstkoutOrder/pagesByOthstkoutNo',
    method: 'get',
    params
  })
}
// 提交与保存
export function submitOtherOutbound(data) {
  return request({
    url: '/mes_api/v1/wmOthstkoutOrder/othstkoutConfirm',
    method: 'post',
    data
  })
}
// 其他出库编辑
export function editOtherOutbound(data) {
  return request({
    url: '/mes_api/v1/wmOthstkoutOrder/updateOthstkout',
    method: 'post',
    data
  })
}
// 校验其他出库对象数量是否正确
export function checkOtherOutbound(data) {
  return request({
    url: '/mes_api/v1/wmOthstkoutOrder/checkOtherOutBound',
    method: 'post',
    data,
    skip: true
  })
}
// 通过主键删除数据

export function deleteOtherOutbound(ids) {
  return request({
    url: `/mes_api/v1/wmOthstkoutOrder/${ids}/delete`,
    method: 'DELETE'
  })
}
