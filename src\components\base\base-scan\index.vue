<script setup>
import { computed, ref, watch, nextTick, getCurrentInstance } from 'vue'
import { Loading } from '@element-plus/icons-vue'

const instance = getCurrentInstance()
const props = defineProps({
  disable: {
    type: <PERSON><PERSON><PERSON>,
    default: () => {
      return false
    }
  },
  loading: {
    type: <PERSON><PERSON><PERSON>,
    default: () => {
      return false
    }
  },
  async: {
    type: <PERSON><PERSON>an,
    default: () => {
      return false
    }
  },
  placeholder: {
    type: String,
    default: '请扫描'
  },
  height: {
    type: String,
    default: '40px'
  },
  width: {
    type: String,
    default: ''
  },
  fill: {
    type: <PERSON>olean,
    default: () => {
      return false
    }
  },
  autofocus: {
    type: <PERSON><PERSON><PERSON>,
    default: () => {
      return false
    }
  },
  uppercase: {
    type: <PERSON><PERSON>an,
    default: () => {
      return false
    }
  },
  lowercase: {
    type: Boolean,
    default: () => {
      return false
    }
  },
  trim: {
    type: <PERSON>olean,
    default: () => {
      return true
    }
  },
  icon: {
    type: <PERSON><PERSON><PERSON>,
    default: () => {
      return true
    }
  }
})
const emit = defineEmits(['onScan'])

const styles = computed(() => {
  let sty = {
    width: props.width,
    height: props.height,
    lineHeight: props.height
  }
  if (props.fill) {
    sty.width = '0'
    sty.flex = '1'
  }
  return sty
})

const inputVlaue = ref('')
const selfDisable = ref(false)
const selfLoading = ref(false)

const onScan = () => {
  if (inputVlaue.value) {
    let value = inputVlaue.value
    if (props.lowercase) {
      value = value.toLowerCase()
    }
    if (props.uppercase) {
      value = value.toUpperCase()
    }
    if (props.trim) {
      value = value.trim()
    }
    //
    selfDisable.value = true
    inputVlaue.value = ''
    if (props.async) {
      selfLoading.value = true
      const cb = () => {
        selfLoading.value = false
      }
      emit('onScan', value, cb)
    } else {
      emit('onScan', value)
    }
    // 防止高频扫码枪
    setTimeout(() => {
      selfDisable.value = false
    }, 500)
  }
}

watch(
  () => props.autofocus,
  (val) => {
    if (val) {
      nextTick(() => {
        instance.refs.RefInput.focus()
      })
    }
  },
  { immediate: true }
)
</script>
<template>
  <el-input class="scan-input" ref="RefInput" :readonly="disable || selfDisable || loading || selfLoading" v-model="inputVlaue" :placeholder="placeholder" @keyup.enter="onScan" :style="styles">
    <template #suffix>
      <div class="scan-input-icon">
        <el-icon class="is-loading" v-if="loading || selfLoading"><Loading /></el-icon>
        <SvgIcon auto name="scan" size="20" class="primary" v-if="icon" />
      </div>
    </template>
  </el-input>
</template>

<style lang="scss" scoped>
::v-deep.scan-input {
  .el-input__inner {
    height: inherit !important;
    line-height: inherit !important;
  }
}
.scan-input-icon {
  height: 100%;
  width: auto;
  display: flex;
  align-items: center;
  img {
    height: 70%;
  }
  i {
    font-size: 20px;
    margin-right: 5px;
  }
}
</style>
