<script setup>
import { computed, ref } from 'vue'
import { hasAuth } from '@/common/auth'
import router from '@/router'
// 路由数据
const props = defineProps({
  data: {
    type: Object,
    default: () => null
  }
})
// 子菜单下拉是否展开
const isOpenGroup = ref(false)
// 子菜单
const children = computed(() => {
  if (!props.data.children) return []
  return props.data.children.filter((i) => {
    if (i.hidden) {
      return false
    }
    return hasAuth(i.meta.auth)
  })
})
const childrenHeight = computed(() => {
  let h = 28
  const winw = window.innerWidth
  if (winw <= 1680) {
    h = 22
  }
  if (isOpenGroup.value && children.value.length) {
    // height + margin
    return children.value.length * (h + 5) - 5 + 10 + 'px'
  } else {
    return 0
  }
})
// 主菜单是否高亮状态
const blockActive = computed(() => {
  if (props.data.path === router.currentRoute.value.path || props.data.path === router.currentRoute.value.meta.parent) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    isOpenGroup.value = true
    return true
  }
  if (props.data.children && props.data.children.length) {
    if (props.data.children.find((i) => i.path === router.currentRoute.value.path)) {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      isOpenGroup.value = true
      return true
    }
  }
  return false
})

/*
 *   点击菜单
 *   @params {void}
 *   @return {void}
 */
const menuItemClick = (route) => {
  if (children.value.length) {
    isOpenGroup.value = !isOpenGroup.value
  } else {
    router.push(route.path)
  }
}
const childrenClick = (route) => {
  router.push(route.path)
}
</script>
<template>
  <div v-if="props.data">
    <div class="silder-menu-item" :class="{ 'is-active': blockActive }" @click="menuItemClick(props.data)">
      <span class="silder-menu-item-icon">
        <SvgIcon :name="props.data.meta.icon" size="22" />
      </span>
      <span class="silder-menu-item-title">{{ $t(props.data.meta.title) }}</span>
    </div>
    <ul class="silder-menu-item-group" :class="{ 'is-open': isOpenGroup }" :style="{ height: childrenHeight }">
      <li
        class="silder-menu-item-group-child"
        :class="{
          'is-active': item.path === router.currentRoute.value.path || item.path === router.currentRoute.value.meta.parent
        }"
        v-for="item in children"
        :key="item.path"
        @click="childrenClick(item)"
      >
        {{ $t(item.meta.title) }}
      </li>
    </ul>
  </div>
</template>
<style scoped lang="scss">
.silder-menu-item {
  color: #333;
  width: 100%;
  margin-bottom: 2px;
  border-radius: 6px;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.3s;
  cursor: pointer;
  font-family: 'menuFont', serif;
}
.silder-menu-item-icon {
  $size: 35px;
  width: $size;
  height: $size;
  padding: 5px;
  margin: 6px 9px;
  overflow: hidden;
  border-radius: 6px;
  box-sizing: border-box;
  display: inline-flex;
  transition: background-color 0.3s;
  @include flex-center;
}
.silder-menu-item-title {
  font-size: 14px;
  //font-weight: 800;
  transition: color 0.3s;
}
// active
.silder-menu-item.is-active,
.silder-menu-item:hover {
  background-color: fade-out($color: $primary, $amount: 0.85);
  .silder-menu-item-icon {
    background-color: white;
  }
  .silder-menu-item-title {
    color: $primary;
  }
}
.silder-menu-item-group {
  height: 0;
  margin: 0;
  overflow: hidden;
  padding-left: 54px;
  transition: all 300ms;
  box-sizing: border-box;
  .silder-menu-item-group-child {
    height: 28px;
    line-height: 28px;
    font-size: 14px;
    color: #777;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    font-family: 'Microsoft YaHei';
    &:first-child {
      margin-top: 5px;
    }
    &:last-child {
      margin-bottom: 5px;
    }
    &:hover,
    &.is-active {
      color: $primary;
    }
    & + .silder-menu-item-group-child {
      margin-top: 5px;
    }
  }
}
.silder-menu-item-group.is-open {
  height: auto;
  box-sizing: border-box;
}
@media screen and (max-width: 1920px) {
  .silder-menu-item-icon {
    $size: 35px;
    width: $size;
    height: $size;
  }
  .silder-menu-item-title {
    font-size: 14px;
  }
  .silder-menu-item-group-child {
    font-size: 13px !important;
  }
}
@media screen and (max-width: 1600px) {
  .silder-menu-item-icon {
    $size: 30px;
    width: $size;
    height: $size;
  }
  .silder-menu-item-title {
    font-size: 13px;
  }
  .silder-menu-item-group-child {
    font-size: 13px !important;
  }
}
@media screen and (max-width: 1400px) {
  .silder-menu-item-icon {
    $size: 25px !important;
    width: $size;
    height: $size;
  }
  .silder-menu-item-title {
    font-size: 12px;
  }
  .silder-menu-item-group-child {
    font-size: 13px !important;
  }
}
@media screen and (max-width: 1200px) {
  .silder-menu-item-icon {
    $size: 25px;
    width: $size;
    height: $size;
  }
  .silder-menu-item-title {
    font-size: 13px;
  }
  .silder-menu-item-group-child {
    font-size: 13px !important;
  }
}
</style>
