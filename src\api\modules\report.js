import request from '@/common/request.js'
// 报表 分析

/*
 *  获取点检分析列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const ReportGetCheckAnalysis = (data = {}) => {
  return request({
    url: '/tpm_api/v1/dcCheckOrder/check/analysis',
    method: 'POST',
    data
  })
}

/*
 *  获取设备分析列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const ReportGetDeviceAnalysis = (data = {}) => {
  return request({
    url: '/tpm_api/v1/fmRepairOrder/fault/analysis',
    method: 'POST',
    data
  })
}

/*
 *  获取人员分析列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const ReportGetPersonAnalysis = (data = {}) => {
  return request({
    url: '/tpm_api/v1/fmRepairOrder/fault/perAnalysis',
    method: 'POST',
    data
  })
}

/*
 *  获取保养分析列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const ReportGetMaintAnalysis = (data = {}) => {
  return request({
    url: '/tpm_api/v1/maint/analysis',
    method: 'POST',
    data
  })
}
