import request from '@/common/request.js'

//查询出库单的需求明细
export function getOutboundDetail(params, data) {
  return request({
    url: '/mes_api/v1/wmStockoutItem/pages',
    method: 'post',
    params,
    data
  })
}

//出库仓库级联查询
export function getCascadeWhData(params) {
  return request({
    url: '/mes_api/v1/WarehouseAssociation/info',
    method: 'get',
    params
  })
}

//出库需求单提交
export function fetchSubmitOutbound(data) {
  return request({
    url: '/mes_api/v1/wmPickingTask/submit',
    method: 'post',
    data
  })
}

//查询可用库存清单
export function getStockList(params, data) {
  return request({
    url: '/mes_api/v1/wmQuant/paginStorage',
    method: 'post',
    params,
    data
  })
}

//查询其它入库库存数据
export function GetOtherInStockList(params, data) {
  return request({
    url: '/mes_api/v1/wmQuant/outAccount',
    method: 'post',
    params,
    data
  })
}

//查询拣货单明细
export function getPickDetailList(params) {
  return request({
    url: '/mes_api/v1/wmPickingItem/pageRelate',
    method: 'get',
    params
  })
}

//查询拣货明细-确认拣货列表
export function getConfirmPickData(data) {
  return request({
    url: '/mes_api/v1/wmPickingItem/confirmPickInfo',
    method: 'PUT',
    data
  })
}

//查询出库确认单-出库明细
export function getOutboundConfirmDetail(params) {
  return request({
    url: '/mes_api/v1/wmOutcfmItem/pageRelate',
    method: 'get',
    params
  })
}

//出库确认单-出库明细列表
export function getOutboundConfirmList(params) {
  return request({
    url: '/mes_api/v1/wmOutcfmOrder/cfmItemPage',
    method: 'get',
    params
  })
}

//出库凭证列表查询
export function getOutboundVoucherList(params, data) {
  return request({
    url: '/mes_api/v1/wmOutboundDoc/pages',
    method: 'post',
    params,
    data
  })
}

//出库凭证明细
export function getOutboundVoucherDetailsList(params) {
  return request({
    url: '/mes_api/v1/wmOutboundDoc/getOutDocItem',
    method: 'get',
    params
  })
}

//出库需求单关闭
export function getCloseOutboundOrder(params) {
  return request({
    url: '/mes_api/v1/wmPickingTask/setClose',
    method: 'put',
    params
  })
}

//出库需求单-详情-配货明细-拣货确认查询列表
export function getPickConfirmList(data) {
  return request({
    url: '/mes_api/v1/wmPickingItem/stuPickInfo',
    method: 'put',
    data
  })
}

//出库需求单-拣货确认-确定
export function submitPickConfirm(data) {
  return request({
    url: '/mes_api/v1/wmPickingTask/submitPickTask',
    method: 'put',
    data
  })
}

//出库需求单-车间配送-确认
export function submitTransfer(data) {
  return request({
    url: '/mes_api/v1/wmOutcfmOrder/submitTransOrder',
    method: 'post',
    data
  })
}

//出库需求单-出库确认
export function submitOutboundOrder(data) {
  return request({
    url: '/mes_api/v1/wmOutcfmOrder/submitConfirm',
    method: 'post',
    data
  })
}

//出库单详情-出库明细列表查询
export function getOutboundItemList(params, data) {
  return request({
    url: '/mes_api/v1/wmOutboundItem/pageRelate',
    method: 'post',
    params,
    data
  })
}

//出库单详情-出库物料列表
export function getOutboundMaterialList(params, data) {
  return request({
    url: '/mes_api/v1/wmOutboundDoc/pages',
    method: 'post',
    params,
    data
  })
}

//根据仓库查询出库单流程
export function getOutboundProcess(params) {
  return request({
    url: '/mes_api/v1/wmPickingItem/processType',
    method: 'get',
    params
  })
}
// 拣货移除
export function removePickingTask(ids) {
  return request({
    url: `/mes_api/v1/wmPickingItem/${ids}/delete`,
    method: 'DELETE'
  })
}

// 手动配货确定
export function fetchManualAllocation(data) {
  return request({
    url: '/mes_api/v1/wmPickingTask/handDistribute',
    method: 'PUT',
    data,
    skip: true
  })
}
