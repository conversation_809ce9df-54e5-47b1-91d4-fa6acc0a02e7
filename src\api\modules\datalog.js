import request from '@/common/request.js'
// 数据记录

/*
 *  获取停机记录列表
 *  @param { params, data }
 *  @returns { Promise }
 */

export const GetOeeStopLog = (params = { page: 1, size: 999999 }, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopLog/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  新增停机记录
 *  @param { data }
 *  @returns { Promise }
 */
export const SaveOeeStopLog = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopLog/save',
    method: 'POST',
    data
  })
}

/*
 *  编辑停机记录
 *  @param { data }
 *  @returns { Promise }
 */
export const UpdateOeeStopLog = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeStopLog/update',
    method: 'PUT',
    data
  })
}

/*
 *  删除停机记录
 *  @param { params } ids多条,分割
 *  @returns { Promise }
 */
export const DeleteOeeStopLog = (params = {}) => {
  return request({
    url: `/oee_api/v1/oeeStopLog/${params.ids}/delete`,
    method: 'DELETE',
    params
  })
}

/*
 *  获取产量记录列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const GetOeeProLog = (params = { page: 1, size: 999999 }, data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProLog/pages',
    method: 'POST',
    params,
    data
  })
}

/*
 *  新增产量记录
 *  @param { data }
 *  @returns { Promise }
 */
export const SaveOeeProLog = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProLog/save',
    method: 'POST',
    data
  })
}

/*
 *  编辑产量记录
 *  @param { data }
 *  @returns { Promise }
 */
export const UpdateOeeProLog = (data = {}) => {
  return request({
    url: '/oee_api/v1/oeeProLog/update',
    method: 'PUT',
    data
  })
}

/*
 *  删除产量记录
 *
 *  @returns { Promise }
 */
export const DeleteOeeProLog = (params = {}) => {
  return request({
    url: `/oee_api/v1/oeeProLog/${params.ids}/delete`,
    method: 'DELETE',
    params
  })
}
