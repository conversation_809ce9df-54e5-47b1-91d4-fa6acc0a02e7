<template>
  <div id="tagsview" class="tagsview">
    <div class="tagsview-btn prve" @click="prve()">
      <svg-icon name="tagsview-icon" size="16" class="primary" />
    </div>
    <scroll-pane ref="scrollPane" class="flex-x no-scroll" style="height: 90%" @scroll="handleScroll">
      <div class="tagsview-warp">
        <router-link
          v-for="tag in visitedViews"
          ref="tag"
          :key="tag.path"
          :class="isActive(tag) ? 'active' : ''"
          :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
          class="tagsview-item"
          @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
          @contextmenu.prevent="openMenu(tag, $event)"
        >
          <span class="tagsview-item-label">{{ $t(tag.meta.title) }}</span>
          <svg-icon v-if="!isAffix(tag)" name="close-f" size="12" @click.prevent.stop="closeSelectedTag(tag)" />
        </router-link>
      </div>
    </scroll-pane>
    <div class="tagsview-btn next" @click="next()">
      <svg-icon name="tagsview-icon" size="16" class="primary" />
    </div>

    <Transition>
      <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
        <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">关闭</li>
        <li @click="closeOthersTags">关闭其他</li>
        <li @click="closeAllTags(selectedTag)">关闭所有</li>
      </ul>
    </Transition>
  </div>
</template>

<script>
import ScrollPane from './scrollPane.vue'
import { useTagview } from '@/stores/tagview.js'
import path from 'path'

export default {
  components: { ScrollPane },
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      tagview: useTagview()
    }
  },
  computed: {
    visitedViews() {
      return this.tagview.visitedViews
    },
    routes() {
      return this.$router.options.routes
    }
  },
  watch: {
    $route() {
      this.addTags()
      this.moveToCurrentTag()
    },
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    }
  },
  mounted() {
    this.initTags()
    this.addTags()
  },
  methods: {
    isActive(route) {
      return route.path === this.$route.path
    },
    isAffix(tag) {
      return tag.meta && tag.meta.affix
    },
    filterAffixTags(routes, basePath = '/') {
      let tags = []
      routes.forEach((route) => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path)
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta }
          })
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path)
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags]
          }
        }
      })
      return tags
    },
    initTags() {
      this.tagview.delAllViews()
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes))
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.tagview.addVisitedView(tag)
        }
      }
    },
    addTags() {
      const { name } = this.$route
      if (name) {
        this.tagview.addView(this.$route)
      }
      return false
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
            this.$refs.scrollPane.moveToTarget(tag)
            // when query is different then update
            if (tag.to.fullPath !== this.$route.fullPath) {
              this.tagview.updateVisitedView(this.$route)
            }
            break
          }
        }
      })
    },
    closeSelectedTag(view) {
      this.tagview.delView(view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          this.toLastView(visitedViews, view)
        }
      })
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag)
      this.tagview.delOthersViews(this.selectedTag).then(() => {
        this.moveToCurrentTag()
      })
    },
    closeAllTags(view) {
      this.tagview.delAllViews().then(({ visitedViews }) => {
        if (this.affixTags.some((tag) => tag.path === view.path)) {
          return
        }
        this.toLastView(visitedViews, view)
      })
    },
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.$router.push(latestView.fullPath)
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        if (view.name === 'Dashboard') {
          // to reload home page
          this.$router.replace({ path: '/redirect' + view.fullPath })
        } else {
          this.$router.push('/')
        }
      }
    },
    openMenu(tag, e) {
      const menuMinWidth = 105
      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth // container width
      const maxLeft = offsetWidth - menuMinWidth // left boundary
      const left = e.clientX - offsetLeft + 215 // 15: margin right; 200: sidebar

      if (left > maxLeft) {
        this.left = maxLeft
      } else {
        this.left = left
      }

      this.top = e.clientY
      this.visible = true
      this.selectedTag = tag
    },
    closeMenu() {
      this.visible = false
    },
    handleScroll() {
      this.closeMenu()
    },
    prve() {
      this.$refs.scrollPane.prve()
    },
    next() {
      this.$refs.scrollPane.next()
    }
  }
}
</script>

<style lang="scss" scoped>
$tabSize: 40px;
.tagsview {
  margin: 5px;
  padding: 10px;
  background-color: #fff;
  box-shadow: 4px 4px 10px #deedf8;
  border-radius: 10px;
  box-sizing: border-box;
  padding: 0;
  user-select: none;
  height: $tabSize;
  align-items: center;
  font-size: 16px;
  display: flex;
  margin-top: 0;
  margin-bottom: 0;
  border-radius: 5px;
  border-bottom: 2px solid $primary;
}

.tagsview-btn {
  width: $tabSize;
  height: $tabSize - 2px;
  box-sizing: border-box;
  background-color: white;
  border: 1px solid #c2c2c25c;
  border-radius: inherit;
  text-align: center;
  line-height: $tabSize - 2px;
  box-shadow: 0 0 8px -2px #c2c2c2;
  cursor: pointer;
  transform: rotateY(180deg);

  &:hover .svg-icon {
    transform: scale(1.1);
  }

  &.next {
    transform: rotateY(0deg);
  }
}

.tagsview-warp {
  white-space: nowrap;
}

.tagsview-item {
  width: 120px;
  height: $tabSize;
  padding: 0 10px;
  font-size: 14px;
  line-height: $tabSize;
  color: #666;
  border: 1px solid #e4e4e4;
  box-shadow: 0 0 5px rgb(194 194 194 / 97%);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #ffffff;
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  position: relative;
  transform: translateY(1px);
  text-decoration: none;

  .tagsview-item-label {
    flex: 1;
    width: 0;
    flex-basis: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .iwarp {
    flex-shrink: 0;
    margin-left: auto;
    color: fade($primary, 20%);

    &:hover {
      color: rgba(255, 255, 255, 0.7) !important;
    }
  }

  &::after {
    content: '';
    position: absolute;
    // -34px = $tabSize
    left: calc(calc(-40px / 4) - 1px);
    bottom: -100%;
    width: calc($tabSize / 4);
    height: calc($tabSize / 4);
    background: radial-gradient(circle at -10% 0%, transparent calc($tabSize / 4), $primary 0);
  }

  &::before {
    content: '';
    position: absolute;
    // -34px = $tabSize
    left: calc(calc(-34px / 4) - 1px);
    bottom: -100%;
    width: calc($tabSize / 4);
    height: calc($tabSize / 4);
    background: radial-gradient(circle at 110% 0%, transparent calc($tabSize / 4) / 4, $primary 0);
  }
}

.tagsview-item.active,
.tagsview-item:hover {
  cursor: pointer;
  color: white;
  background-color: $primary;
  border-color: $primary;
  z-index: 1;

  &:after,
  &:before {
    bottom: 0;
  }

  .iwarp {
    color: white;
  }
}

.contextmenu {
  color: #333;
  margin: 0;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.2s;
  border-radius: 10px;
  background: #fff;
  box-shadow: 0px 6px 25px rgba(0, 0, 0, 0.25);

  li {
    margin: 0;
    padding: 7px 16px;
    cursor: pointer;
    font-weight: bold;
    cursor: pointer;

    &:hover {
      color: white;
      background: $primary;
    }
  }
}

.v-enter-active,
.v-leave-active {
  transition: all 0.2s ease;
  transform: translateX(0);
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
  transform: translateX(5px);
}
</style>
