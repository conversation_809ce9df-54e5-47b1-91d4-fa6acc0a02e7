<template>
  <div class="appmain" id="appmain">
    <RouterView v-slot="{ Component, route }">
      <KeepAlive :include="tagview.cachedViews" :max="20">
        <component :is="wrap(route.name, Component)" />
      </KeepAlive>
    </RouterView>
  </div>
</template>
<script setup>
import { h } from 'vue'
import { useTagview } from '@/stores/tagview.js'

const tagview = useTagview()

const wrapperMap = new Map()

const wrap = (name, component) => {
  let wrapper
  const wrapperName = name
  if (wrapperMap.has(wrapperName)) {
    wrapper = wrapperMap.get(wrapperName)
  } else {
    wrapper = {
      name: wrapperName,

      render() {
        return h('div', { className: 'appmain-page-wrapper' }, component)
      }
    }
    wrapperMap.set(wrapperName, wrapper)
  }
  return h(wrapper)
}
</script>
<style scoped lang="scss">
.appmain {
  width: 100%;
  height: 100%;
}
.appmain-page-wrapper {
  height: 100%;
  width: 100%;
}
</style>
