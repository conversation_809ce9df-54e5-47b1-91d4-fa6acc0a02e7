import { computed } from "vue"
import i18n from "@/i18n"
export default computed(() => {
  return {
    // 角色级别
    roleLevel: [
      { zh: '工厂级', label: i18n.$t('layerAudit.factoryLevel'), value: 'factory' },
      { zh: '车间级', label: i18n.$t('layerAudit.workshopLevel'), value: 'shop' },
      { zh: '工段级', label: i18n.$t('layerAudit.workSectionLevel'), value: 'vsm' },
      // { zh: '产线级', label: i18n.$t('layerAudit.lineLevel'), value: 'line' }
    ],
    // 角色状态
    roleStatus: [
      { zh: '启用', label: i18n.$t('base.enable'), value: true },
      { zh: '未启用', label: i18n.$t('base.notEnable'), value: false }
    ],
  }
})
