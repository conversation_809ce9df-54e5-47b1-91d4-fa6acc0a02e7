<script setup>
/* eslint-disable */
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import { deepClone } from '@/common/utils'
import propsData from './props'
// 参数
const props = defineProps(propsData)
// 事件
const emit = defineEmits(['clear', 'query'])
// 表单数据
const oldData = ref({})
const formHeight = ref('')
const timer = ref(null)
const parentWatchKeys = ref([])
const isClose = ref(false)
const formRef = ref(null)
const labelWidth = ref('auto')
const modelData = computed(() => deepClone(props.data))

// 表单高度
const onResize = ({ height }) => {
  formHeight.value = height
}
// 初始化设置
const defineSetting = () => {
  // 是否显示折叠
  if (props.fold) {
    isClose.value = true
    nextTick(() => {
      formHeight.value = formRef.value.offsetHeight + 'px'
    })
  } else {
    isClose.value = false
  }
  // 处理parent数据
  for (let i in props.model) {
    let item = props.model[i]
    // 设置select中有parent属性的值
    if (item.type === 'select' && item.parent) {
      item['prooptions'] = deepClone(item.options)
      parentWatchKeys.value.push(item.parent)
    }
  }
}
// 查找改变属性
const diffKey = (newData, oldData) => {
  for (let i in newData) {
    if (newData[i] !== oldData[i]) {
      return i
    }
  }
}
// 监听值发生变化时触发
const loadChildOptions = (key) => {
  const childs = props.model.filter((item) => item.parent === key)
  childs.forEach((item) => {
    if (props.data[key]) {
      item.options = item.prooptions.filter((item) => item[key] === props.data[key])
    } else {
      item.options = deepClone(item.prooptions)
    }
    //父级值修改时，子元素清空值
    // eslint-disable-next-line vue/no-mutating-props
    props.data[item.model] = ''
  })
}
// 查询
const onQuery = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    emit('query')
  }, 500)
}
// 清空
const onClear = async () => {
  Object.assign(props.data, oldData.value)
  emit('clear')
  if (props.isClear) return
  await nextTick()
  onQuery()
}
// 表单项发生改变时触发 TODO...
const onFormChange = (item) => {
  if (props.autoQuery) {
    onQuery()
  }
}

watch(
  () => props.model,
  () => {
    defineSetting()
  },
  { deep: true, immediate: true }
)
watch(
  modelData,
  (n, o) => {
    // data数据变动属性名称
    const parentDiffKey = diffKey(n, o)
    if (parentWatchKeys.value.includes(parentDiffKey)) {
      loadChildOptions(parentDiffKey)
    }
  },
  { deep: true }
)

onMounted(() => {
  Object.assign(oldData.value, props.data)
})
</script>

<template>
  <div class="base-header" :class="[{ 'is-autosize': autosize }, { fold: fold }, { 'is-close': isClose }, { 'is-open': !isClose }]">
    <div class="title" v-if="props.title">{{ props.title }}</div>
    <div class="first-btn-warp" v-if="props.fold">
      <el-button v-if="props.showQuery" type="primary" @click="onQuery()"> {{ $t('base.search') }} </el-button>
      <el-button v-if="props.showClear" plain @click="onClear()"> {{ $t('base.clear') }} </el-button>
      <el-button link @click="isClose = !isClose"> {{ $t('base.open') }} <i class="el-icon-arrow-down el-icon--right"> </i> </el-button>
      <slot></slot>
    </div>
    <el-form :model="props.data" ref="formRef" :style="{ height: formHeight }" v-resize="onResize" :inline="autosize" @submit.prevent>
      <el-form-item
        v-for="(item, index) in props.model"
        :key="index"
        :prop="item.model"
        :label="item.label"
        :label-width="item.labelWidth || labelWidth"
        :class="{ block: item.block }"
        :style="{
          width: `calc( ${item.width} - 20px)`
        }"
      >
        <!-- input || textarea -->
        <el-input
          v-if="['input', 'textarea'].includes(item.type)"
          v-model="props.data[item.model]"
          :type="item.type"
          :rows="item.rows"
          :autosize="item.autosize"
          :min="item.min"
          :max="item.max"
          :minlength="item.minlength"
          :maxlength="item.maxlength"
          :show-password="item.showPassword"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :placeholder="item.placeholder || $t('form.pleaseInput') + ` ${item.label}`.toLocaleLowerCase()"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @input="item.oninput && item.oninput(props.data[item.model], $event, item.model)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
          <template #suffix v-if="item.suffix">{{ item.suffix }}</template>
        </el-input>
        <!-- select -->
        <el-select
          v-else-if="item.type === 'select'"
          v-model="props.data[item.model]"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :multiple="item.multiple"
          :filterable="item.filterable"
          :collapse-tags="item.collapseTags"
          :multiple-limit="item.multipleLimit"
          :remote="item.remoteBol"
          :remote-method="item.remoteBol && item.remoteMethods"
          :placeholder="item.placeholder || $t('form.pleaseSelect') + ` ${item.label}`.toLocaleLowerCase()"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
          <el-option v-if="item.showAll" label="全部" value="" />
          <el-option
            v-for="(option, optionIndex) in item.options"
            :label="item.optionLabel ? option[item.optionLabel] : option.label"
            :value="item.optionValue ? option[item.optionValue] : option.value"
            :key="item.optionKey ? option[item.optionKey] : optionIndex"
            :disabled="option.disabled"
          >
            <span v-if="item.optionTemplate" v-html="item.optionTemplate(option)"></span>
          </el-option>
        </el-select>

        <!-- select-dict -->
        <el-select-dict
          v-else-if="item.type === 'select-dict'"
          v-model="props.data[item.model]"
          :dictType="item.dictType"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :multiple="item.multiple"
          :filterable="item.filterable"
          :collapse-tags="item.collapseTags"
          :multiple-limit="item.multipleLimit"
          :placeholder="item.placeholder || $t('form.pleaseSelect') + ` ${item.label}`.toLocaleLowerCase()"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        />

        <!-- select-remote -->
        <base-select-remote
          v-else-if="item.type === 'select-remote'"
          v-model="props.data[item.model]"
          :request="item.request"
          :paramsKey="item.paramsKey"
          :otherParams="item.otherParams"
          :optionLabel="item.optionLabel"
          :optionValue="item.optionValue"
          :optionTemplate="item.optionTemplate"
          :montage="item.montage"
          :placeholder="item.placeholder || $t('form.pleaseSelect') + ` ${item.label}`.toLocaleLowerCase()"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        />
        <!-- select-page-remote -->
        <!-- :placeholder="item.placeholder || `请选择${item.label}`" -->
        <base-select-page-remote
          v-else-if="item.type === 'select-page-remote'"
          v-model="props.data[item.model]"
          :request="item.request"
          :paramsKey="item.paramsKey"
          :optionLabel="item.optionLabel"
          :optionValue="item.optionValue"
          :montage="item.montage"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        />
        <!-- cascader -->
        <el-cascader
          v-if="item.type === 'cascader'"
          v-model="props.data[item.model]"
          :options="item.options"
          :props="item.props"
          :clearable="item.clearable"
          :disabled="item.disabled"
          :placeholder="item.placeholder || $t('form.pleaseSelect') + ` ${item.label}`.toLocaleLowerCase()"
          :show-all-levels="item.showAllLevels"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        />
        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="item.type === 'number'"
          v-model="props.data[item.model]"
          :disabled="item.disabled"
          :min="item.min"
          :max="item.max"
          :step="item.step"
          :precision="item.precision"
          :controls="item.controls"
          :controls-position="item.controlsPosition"
          :placeholder="item.placeholder || '请输入'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        />
        <el-radio-group
          v-else-if="item.type === 'radio'"
          v-model="props.data[item.model]"
          :fill="item.fill"
          :text-color="item.textColor"
          :disabled="item.disabled"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
        >
          <template v-if="item.isButton">
            <el-radio-button
              v-for="(option, optionIndex) in item.options"
              :label="item.optionValue ? option[item.optionValue] : option.value"
              :key="item.optionKey ? option[item.optionKey] : optionIndex"
              :disabled="option.disabled"
            >
              <span v-if="item.optionTemplate" v-html="item.optionTemplate(option)"></span>
              <span v-else>{{ item.optionLabel ? option[item.optionLabel] : option.label }}</span>
            </el-radio-button>
          </template>
          <template v-else>
            <el-radio
              v-for="(option, optionIndex) in item.options"
              :label="item.optionValue ? option[item.optionValue] : option.value"
              :key="item.optionKey ? option[item.optionKey] : optionIndex"
              :disabled="option.disabled"
              :border="option.border"
            >
              <span v-if="item.optionTemplate" v-html="item.optionTemplate(option)"></span>
              <span v-else>{{ item.optionLabel ? option[item.optionLabel] : option.label }}</span>
            </el-radio>
          </template>
        </el-radio-group>
        <!-- checkbox -->
        <el-checkbox-group
          v-else-if="item.type === 'checkbox'"
          v-model="props.data[item.model]"
          :fill="item.fill"
          :text-color="item.textColor"
          :min="min"
          :max="max"
          :disabled="item.disabled"
          @change="item.onchange && item.onchange($event)"
        >
          <el-checkbox
            v-for="(option, optionIndex) in item.options"
            :label="item.optionValue ? option[item.optionValue] : option.value"
            :key="item.optionKey ? option[item.optionKey] : optionIndex"
            :disabled="option.disabled"
            :border="option.border"
            @change="option.onchange && option.onchange($event), onFormChange(item)"
          >
            <span v-if="item.optionTemplate" v-html="item.optionTemplate(option)"></span>
            <span v-else>{{ item.optionLabel ? option[item.optionLabel] : option.label }}</span>
          </el-checkbox>
        </el-checkbox-group>
        <!-- 日期选择 -->
        <!-- 选择日 -->
        <el-date-picker
          v-else-if="item.type === 'date'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :placeholder="item.placeholder || $t('form.pleaseSelect')"
          :format="item.format || 'YYYY-MM-DD'"
          :value-format="item.valueFormat || 'YYYY-MM-DD'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
          style="width: 100%"
        >
        </el-date-picker>
        <!-- 选择周 -->
        <el-date-picker
          v-else-if="item.type === 'week'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :placeholder="item.placeholder || '请选择'"
          :format="item.format || '[Week] ww'"
          :value-format="item.valueFormat || 'YYYY-MM-DD / [Week] ww '"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 选择月 -->
        <el-date-picker
          v-else-if="item.type === 'month'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :placeholder="item.placeholder || '请选择'"
          :format="item.format || 'YYYY-MM'"
          :value-format="item.valueFormat || 'YYYY-MM'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 选择年 -->
        <el-date-picker
          v-else-if="item.type === 'year'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :placeholder="item.placeholder || '请选择'"
          :format="item.format || 'YYYY'"
          :value-format="item.valueFormat || 'YYYY'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 范围-日 -->
        <el-date-picker
          v-else-if="item.type === 'daterange'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :range-separator="item.rangeSeparator || $t('base.timeTo')"
          :start-placeholder="item.startPlaceholder || $t('base.startDate')"
          :end-placeholder="item.endPlaceholder || $t('base.endDate')"
          :format="item.format || 'YYYY-MM-DD'"
          :value-format="item.valueFormat || 'YYYY-MM-DD'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 范围-月 -->
        <el-date-picker
          v-else-if="item.type === 'monthrange'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :range-separator="item.rangeSeparator || '至'"
          :start-placeholder="item.startPlaceholder || '开始月份'"
          :end-placeholder="item.endPlaceholder || '结束月份'"
          :format="item.format || 'YYYY-MM'"
          :value-format="item.valueFormat || 'YYYY-MM'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 日期时间 -->
        <el-date-picker
          v-else-if="item.type === 'datetime'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :placeholder="item.placeholder || $t('form.pleaseSelect')"
          :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
          :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 日期时间范围 -->
        <el-date-picker
          v-else-if="item.type === 'datetimerange'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :disabledDate="item.disabledDate"
          :default-value="item.defaultValue"
          :default-time="item.defaultTime"
          :shortcuts="item.shortcuts"
          :range-separator="item.rangeSeparator || $t('base.timeTo')"
          :start-placeholder="item.startPlaceholder || $t('base.startDate')"
          :end-placeholder="item.endPlaceholder || $t('base.endDate')"
          :format="item.format || 'YYYY-MM-DD HH:mm:ss'"
          :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-date-picker>
        <!-- 时间选择 -->
        <el-time-picker
          v-else-if="item.type === 'time'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :default-value="item.defaultValue"
          :placeholder="item.placeholder || '请选择'"
          :disabled-hours="item.disabledHours"
          :disabled-minutes="item.disabledMinutes"
          :disabled-seconds="item.disabledSeconds"
          :format="item.format || 'HH:mm:ss'"
          :value-format="item.valueFormat || 'HH:mm:ss'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-time-picker>
        <!-- 时间选择范围 -->
        <el-time-picker
          v-else-if="item.type === 'timerange'"
          v-model="props.data[item.model]"
          :type="item.type"
          :editable="item.editable"
          :disabled="item.disabled"
          :clearable="item.clearable"
          :readonly="item.readonly"
          :is-range="true"
          :default-value="item.defaultValue"
          :placeholder="item.placeholder || '请选择'"
          :disabled-hours="item.disabledHours"
          :disabled-minutes="item.disabledMinutes"
          :disabled-seconds="item.disabledSeconds"
          :format="item.format || 'HH:mm:ss'"
          :value-format="item.valueFormat || 'HH:mm:ss'"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
          @focus="item.onfocus && item.onfocus(props.data[item.model], $event)"
          @blur="item.onblur && item.onblur(props.data[item.model], $event)"
        >
        </el-time-picker>
        <!-- switch -->
        <el-switch
          v-else-if="item.type === 'switch'"
          v-model="props.data[item.model]"
          :active-text="item.activeText || ''"
          :inactive-text="item.inactiveText || ''"
          :active-value="item.activeValue || true"
          :inactive-value="item.inactiveValue || false"
          :disabled="item.disabled"
          :size="item.size"
          @change="item.onchange && item.onchange($event), onFormChange(item)"
        >
        </el-switch>
      </el-form-item>
      <div class="last-btns-warp">
        <el-button v-if="props.showClear" type="primary" @click="onQuery()"> {{ $t('base.search') }} </el-button>
        <el-button v-if="props.showQuery" plain @click="onClear()"> {{ $t('base.clear') }} </el-button>
        <el-button v-if="props.fold" link @click="isClose = !isClose"> {{ $t('base.close') }}<i class="el-icon-arrow-up el-icon--right" /> </el-button>
        <slot></slot>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.base-header {
  // @include box;
  height: auto;
  overflow: hidden;
  transition: height 0.2s;
  .title {
    font-size: 15px;
    margin-bottom: 5px;
    font-weight: 700;
    color: $primary-font-color;
  }
  .el-form {
    text-align: left;
  }
  :deep .el-cascader {
    width: 100%;
  }
  .el-form-item {
    min-width: 200px;
    :deep .el-form-item__label {
      color: $primary-font-color;
    }
    .el-form-item__content {
      $contentWidth: 100%;
      > .el-select {
        width: $contentWidth;
      }
      > .el-input__wrapper {
        width: $contentWidth;
      }
      > .el-input {
        width: $contentWidth;
      }
      > .el-textarea {
        width: $contentWidth;
      }
      > .el-input-number {
        width: $contentWidth;
      }
    }
  }
}

:deep(.el-date-editor) {
  width: 100% !important;
}
.base-header.fold {
  &.is-close {
    &.medium {
      height: 48px !important;
    }

    &.small {
      height: 45px !important;
    }

    &.mini {
      height: 44px !important;
    }

    .first-btn-warp {
      // display: inline-block !important;
      float: right;
    }

    .last-btns-warp {
      display: none !important;
    }
  }

  &.is-open {
    .first-btn-warp {
      display: none !important;
    }

    .last-btns-warp {
      display: inline-block !important;
    }
  }
}
.base-header.is-autosize {
  .el-form {
    .el-button {
      margin-bottom: 7px;
      margin-top: 7px;
    }
    .el-form-item,
    .first-btn-warp,
    .last-btns-warp {
      width: calc(16.666% - 20px);
      margin-right: 20px;
      margin-bottom: 7px;
      margin-top: 7px;
      white-space: nowrap;
      box-sizing: border-box;
    }
    .last-btns-warp {
      float: right;
      width: fit-content !important;
    }
    .first-btn-warp {
      margin: 0 !important;
      // float: right;
      text-align: right;
      display: inline-block;
    }

    .last-btns-warp {
      margin: 0 20px 0 0 !important;
      text-align: left;
      display: inline-block;
    }

    .el-form-item.block {
      width: 100%;
    }
    &.el-form--inline {
      .el-form-item {
        display: inline-flex;
        align-items: center;
        :deep .el-form-item__label-wrap {
          flex-shrink: 0;
        }
        :deep .el-form-item__content {
          flex: 1;
        }
      }
    }

    @media screen and (max-width: 2559px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(16.666% - 20px);
        margin-right: 20px;
        margin-bottom: 7px;
        margin-top: 7px;
      }

      .el-button {
        margin-bottom: 7px;
        margin-top: 7px;
      }
    }

    @media screen and (max-width: 1920px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(20% - 20px);
        margin-right: 20px;
        margin-bottom: 7px;
        margin-top: 7px;
      }

      .el-button {
        margin-bottom: 7px;
        margin-top: 7px;
      }
    }

    @media screen and (max-width: 1680px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(25% - 20px);
        margin-right: 20px;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }

    @media screen and (max-width: 1440px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(25% - 20px);
        margin-right: 20px;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }

    @media screen and (max-width: 1200px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(33.33% - 20px);
        margin-right: 20px;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }

    @media screen and (max-width: 1024px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(33.333% - 20px);
        margin-right: 20px;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }

    @media screen and (max-width: 780px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: calc(50% - 20px);
        margin-right: 20px;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }

    @media screen and (max-width: 640px) {
      .el-form-item,
      .first-btn-warp,
      .last-btns-warp {
        width: 100%;
        margin-right: 0;
        margin-bottom: 5px;
        margin-top: 5px;
      }

      .el-button {
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }
  }
}
</style>
