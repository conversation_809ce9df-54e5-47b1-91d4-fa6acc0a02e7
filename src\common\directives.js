import { hasAuth } from '@/common/auth'
export default function (app) {
  //监听宽高变化
  app.directive('resize', {
    mounted(el, binding) {
      let width = '',
        height = ''
      function isResize() {
        const style = document.defaultView.getComputedStyle(el)
        if (width !== style.width || height !== style.height) {
          binding.value({ width: style.width, height: style.height })
        }
        width = style.width
        height = style.height
      }
      el.__vueSetInterval__ = setInterval(isResize, 300)
    },
    unmounted(el) {
      clearInterval(el.__vueSetInterval__)
    }
  })
  //校验权限
  app.directive('auth', {
    mounted(el, binding) {
      if (!hasAuth(binding.value)) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  })

  //拖拽
  app.directive('draggable', {
    mounted(el) {
      const downEl = el
      if (!downEl.style.transform) {
        downEl.style.transform = ' translateX(0px) translateY(0px)'
      }
      el.onmousedown = function () {
        document.onmousemove = function (e) {
          if (downEl) {
            let x = 0
            let y = 0
            let newTransform = ''
            let transform = ''
            if (downEl.style.transform) {
              transform = downEl.style.transform
            }

            // 默认填充
            if (transform.indexOf('translateX') === -1) {
              transform += ' translateX(0px) '
            }
            if (transform.indexOf('translateY') === -1) {
              transform += ' translateY(0px) '
            }

            // start
            const values = transform.split(' ')
            values.forEach((value) => {
              if (value.indexOf('translateX') !== -1) {
                x = parseInt(value.replace('translateX(', '').replace('px)', '')) + e.movementX
                newTransform += `translateX(${x}px) `
              } else if (value.indexOf('translateY') !== -1) {
                y = parseInt(value.replace('translateY(', '').replace('px)', '')) + e.movementY
                newTransform += `translateY(${y}px) `
              } else {
                newTransform += value + ' '
              }
            })
            downEl.style.transform = newTransform
          }
        }
        document.onmouseup = function () {
          document.onmousemove = document.onmouseup = null
        }
      }
    }
  })
  //滚动
  app.directive('wheelscale', {
    mounted(el, binding) {
      let min = 0.2
      let max = 3
      let speed = 1
      let scale = null
      let translateY = null
      let translateX = null
      const downEl = el
      if (binding.value) {
        min = binding.value.min || min
        max = binding.value.max || max
        speed = binding.value.speed || speed
        scale = binding.value.scale || null
        translateY = binding.value.translateY || null
        translateX = binding.value.translateX || null
      }
      // 初始化缩放
      if (downEl && scale) {
        downEl.style.transform += `scale(${scale})`
      }
      // 初始化上移
      if (downEl && translateY) {
        downEl.style.transform += `translateY(${-translateY}px)`
      }
      // 初始化左移
      if (downEl && translateX) {
        downEl.style.transform += `translateX(${-translateX}px)`
      }
      el.onmousewheel = function (e) {
        if (downEl) {
          let newTransform = ''
          let transform = ''
          if (downEl.style.transform) {
            transform = downEl.style.transform
          }

          // 默认填充
          if (transform.indexOf('scale') === -1) {
            transform += ' scale(1) '
          }

          const values = transform.split(' ')
          values.forEach((value) => {
            if (value.indexOf('scale') !== -1) {
              let scale = parseFloat(value.replace('scale(', '').replace(')', ''))
              scale += (speed * e.wheelDelta) / 5000
              if (scale < min) {
                scale = min
              }
              if (scale > max) {
                scale = max
              }
              newTransform += `scale(${scale}) `
            } else {
              newTransform += value + ' '
            }
          })
          downEl.style.transform = newTransform
        }
      }
    }
  })
}
