<script setup>
import { computed } from 'vue'
const props = defineProps({
  name: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: ''
  },
  hover: {
    type: Boolean,
    default: false
  }
})

const iconName = computed(() => `#icon-${props.name}`)
const iconClass = computed(() => {
  let name = 'svg-icon'
  if (props.name) {
    name += ` svg-icon-${props.name}`
  }
  if (props.hover) {
    name += ` is-hover`
  }
  return name
})
const iconStyle = computed(() => {
  return {
    color: props.color,
    fontSize: props.size,
    width: props.size,
    height: props.size
  }
})
</script>

<template>
  <svg :class="iconClass" :style="iconStyle" v-bind="$attrs">
    <use :xlink:href="iconName" />
  </svg>
</template>

<style lang="scss" scoped>
.svg-icon {
  // width: 1em;
  // height: 1em;
  fill: currentColor;
  overflow: hidden;
  flex-shrink: 0;
}
.svg-icon.primary {
  color: $primary;
}
.svg-icon.is-hover {
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    transform: scale(1.1);
  }
}
</style>
