import { ref } from 'vue'
import { GetRoleOptions } from '@/api/modules/role'

const optionList = ref([])
const isload = ref(false)

function loadOption() {
  if (isload.value) return
  GetRoleOptions().then((list) => {
    optionList.value = list || []
    isload.value = true
  })
}

function format(value) {
  return optionList.value.find((item) => item.value == value)?.label || value
}

function formatTable(row, column, cellValue) {
  return format(cellValue)
}

function formatTableSpecial01(row, column, cellValue) {
  if (!cellValue) return ''
  let result = ''
  cellValue.split(',').forEach((item) => {
    result += format(item) + ','
  })
  return result.slice(0, -1)
}

export function useRoleDict() {
  loadOption()
  return {
    format,
    formatTable,
    formatTableSpecial01
  }
}
