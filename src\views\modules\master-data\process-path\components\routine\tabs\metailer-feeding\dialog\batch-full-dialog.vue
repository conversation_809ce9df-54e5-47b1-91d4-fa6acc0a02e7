<script setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { AddLaborPosition, GetLaborSkillLevelAll, getBinList } from '@/api/modules/mes'
import { ElMessage } from 'element-plus'
import { GetWhList } from '@/api/modules/wms'
import { asynConfirm, deepClone, getSpecificFormat } from '@/common/utils'
import { useWarehouse } from '@/stores/warehouse'
const emit = defineEmits(['saveFormData'])
// 对话框配置项
const dialogVisible = ref(false)
const dialogOptions = reactive({
  title: '批量填充',
  width: '450px',
  closeOnClickModal: false
})
// 打开弹窗
const formData = ref({})
const curData = ref({})
const callback = ref(null)
const openDialog = async (data, cb) => {
  console.log(data, 666)
  callback.value = cb
  curData.value = data
  formData.value = {
    fromWhsCode: '', // 原料仓库 - 填默认值
    feedSuType: '', // 投入方式 - 填默认值
    fromType: '', // 来源类型 - 填默认值
    fromBinList: [],
    csmBinList: [],
    maxFeedQuant: '', // 最大投料数 - 填默认值
    minFeedQuant: '', // 投料库存预指 - 填默认值
    backFeedWay: '',
    consumeWay: '', // 消耗方式 - 填默认值
    consumeSort: '', // 扣减规则 - 填默认值
    feedSts: [],
    fromBin: '',
    fromBinWhsCode: '',
    csmBinCode: '',
    csmWhsCode: '',
    subPart: false,
    repairIntercept: false,
    repairTimes: 0
  }
  dialogVisible.value = true
  await nextTick()
  queryWhData()
  getWarehouse()
  resetForm()
}

// 表单配置项
// 表单验证规则
const formRules = ref({
  // fromWhsCode: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }],
  // feedSuType: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }],
  // csmBinCode: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }],
  // maxFeedQuant: [{ required: true, message: '请输入', trigger: 'blur' }],
  // minFeedQuant: [{ required: true, message: '请输入', trigger: 'blur' }],
  // backFeedWay: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }],
  // consumeWay: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }]
  // feedSts: [{ required: true, message: '请选择', trigger: ['change', 'blur'] }]
})
const formOptions = computed(() => ({
  size: 'default',
  statusIcon: true,
  model: formData.value,
  rules: formRules.value
}))

// 获取仓库下拉框数据
const whList = ref([])
const queryWhData = async () => {
  try {
    let res = await GetWhList()
    whList.value = res && res.length ? getSpecificFormat(res, 'descrip', 'whCode') : []
  } catch (e) {
    whList.value = []
  }
}

// 仓库主数据
const warehouseModel = ref(null)
const getWarehouse = async () => {
  let res = await useWarehouse().loadData()
  warehouseModel.value = res?.warehouseList ?? []
}
//选择库位触发
const onStationChange = (row, i, type) => {
  const { fromBinList, csmBinList } = formData.value
  if (type == 'from') {
    formData.value.fromBin = fromBinList.length ? fromBinList[3] : ''
    formData.value.fromBinWhsCode = fromBinList.length ? fromBinList[0] : ''
  } else {
    formData.value.csmBinCode = csmBinList.length ? csmBinList[3] : ''
    formData.value.csmWhsCode = csmBinList.length ? csmBinList[0] : ''
  }
}

//指定工位数据
const stationList = computed(() => {
  return curData.value.workStations ? getSpecificFormat(curData.value.workStations, 'stationName', 'stationCode') : []
})

// 确认
const RefForm = ref(null)
const submitLoading = ref(false)
const submitForm = () => {
  RefForm.value.validate((valid) => {
    if (!valid) return
    try {
      submitLoading.value = true
      emit('saveFormData', formData.value)
      dialogVisible.value = false
    } finally {
      submitLoading.value = false
    }
  })
}

// 清空
const resetForm = () => {
  nextTick(() => {
    RefForm.value.clearValidate()
  })
}

// #Expose
defineExpose({
  openDialog
})
</script>

<template>
  <div class="dialog">
    <el-dialog v-model="dialogVisible" v-bind="dialogOptions">
      <el-form v-bind="formOptions" ref="RefForm" v-if="formData">
        <el-form-item label="原料仓库" prop="fromWhsCode">
          <el-select v-model="formData.fromWhsCode" placeholder="请选择" clearable filterable>
            <el-option v-for="item in whList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投入方式" prop="feedSuType">
          <el-select-dict dictType="INVEST_METHOD" v-model="formData.feedSuType" />
        </el-form-item>
        <el-form-item label="来源类型" prop="fromType">
          <el-select-dict dictType="BINFROM" v-model="formData.fromType" />
        </el-form-item>
        <el-form-item label="来源库位" prop="fromBinList">
          <el-cascader v-model="formData.fromBinList" :options="warehouseModel" placeholder="请选择" @change="onStationChange(formData.fromBinList, 'from')" />
        </el-form-item>

        <el-form-item label="消耗库位" prop="csmBinList">
          <el-cascader v-model="formData.csmBinList" :options="warehouseModel" placeholder="请选择" @change="onStationChange(formData.csmBinList, 'csm')" />
        </el-form-item>
        <el-form-item label="最大投料数" prop="maxFeedQuant">
          <el-input-number v-model="formData.maxFeedQuant" :min="0" />
        </el-form-item>
        <el-form-item label="投料库存预指" prop="minFeedQuant">
          <el-input-number v-model="formData.minFeedQuant" :min="0" />
        </el-form-item>
        <!-- <el-form-item label="退料方式" prop="backFeedWay">
          <el-select-dict dictType="BACK_METHOD" v-model="formData.backFeedWay" />
        </el-form-item> -->
        <el-form-item label="消耗方式" prop="consumeWay">
          <el-select-dict dictType="CONSUME_METHOD" v-model="formData.consumeWay" />
        </el-form-item>
        <el-form-item label="扣减规则" prop="consumeSort">
          <el-select-dict dictType="CONSUME_METERIAL" v-model="formData.consumeSort" />
        </el-form-item>
        <el-form-item label="上料工位指定" prop="feedSts">
          <el-select v-model="formData.feedSts" placeholder="请选择" clearable filterable multiple>
            <el-option v-for="item in stationList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="子零件扫描" prop="subPart">
          <el-switch v-model="formData.subPart" style="--el-switch-on-color: #3277f5; --el-switch-off-color: #b4b4b4" />
        </el-form-item>
        <el-form-item label="返修次数是否限制" prop="repairIntercept">
          <el-switch v-model="formData.repairIntercept" style="--el-switch-on-color: #3277f5; --el-switch-off-color: #b4b4b4" />
        </el-form-item>
        <el-form-item label="限制次数" prop="repairTimes" v-if="formData.repairIntercept">
          <el-input-number v-model="formData.repairTimes" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" :loading="submitLoading" @click="submitForm()">保存</el-button>
          <el-button @click="dialogVisible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep .el-dialog__body {
  padding: 10px 20px 20px 20px;
}
:deep .el-dialog__footer {
  text-align: center;
}

.el-form {
  padding: 0 15px;
  .el-form-item {
    :deep .el-form-item__label {
      width: 140px;
      &::after {
        content: ':';
        font-weight: 700;
      }
    }
    :deep .el-form-item__content {
      .el-input,
      .el-select {
        width: 100%;
      }
    }
  }
}
</style>
