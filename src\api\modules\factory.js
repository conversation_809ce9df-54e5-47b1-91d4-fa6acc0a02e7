// 工厂
import request from '@/common/request.js'

/*
 *  获取工厂数据列表
 *  @param { params, data }
 *  @returns { Promise }
 */
export const GetFactoryList = (params = { page: 1, size: 999999 }, data = {}) => {
  return request({
    url: '/api/v1/factory/pages',
    method: 'POST',
    params,
    data: {
      ...data,
      typeCode: 1
    }
  })
}

/*
 *  获取工厂模型树 (用户中心接口)
 *  @param { params, data }
 *  @returns { Promise }
 */
export const GetFactoryModleTree = () => {
  return request({
    url: '/api/v1/factory/options',
    method: 'GET'
  })
}

/*
 *  新增工厂模型 (用户中心接口)
 *  @param { params, data }
 *  @returns { Promise }
 */
export const AddFactoryModel = (data) => {
  return request({
    url: '/api/v1/factory/save',
    method: 'POST',
    data
  })
}

/*
 *  删除工厂模型 (用户中心接口)
 *  @param { params, data }
 *  @returns { Promise }
 */
export const DelFactoryModelById = (ids) => {
  return request({
    url: `/api/v1/factory/${ids}/deletes`,
    method: 'DELETE'
  })
}

/*
 *  修改工厂模型 (用户中心接口)
 *  @param { params, data }
 *  @returns { Promise }
 */
export const UpdateFactoryById = (data) => {
  return request({
    url: `/api/v1/factory/${data.code}/up`,
    method: 'PUT',
    data
  })
}
