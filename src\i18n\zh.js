export default {
  // 路由菜单
  menu: {
    // 用户中心
    userCenter: {
      module: '用户中心',
      personalCenter: '个人中心',
      user: '用户管理',
      organization: '组织架构',
      permission: '权限管理',
      role: '角色配置',
      factoryModel: '工厂模型',
      dictionary: '字典管理',
      shift: '班次管理',
      partner: '合作伙伴'
    },
    //质量
    quality: {
      management: '质量管理',
      procurementQuality: '采购质量',
      qualityInspection: '来料质检',
      processControl: '过程控制',
      productionRelease: '生产放行',
      releaseRecord: '放行记录',
      unqualifiedReview: '不合格品评审',
      processInspection: '过程检验',
      processInspectionRecord: '过程检验记录',
      processInspectionConfig: '过程检验配置',
      addProcessConfig: '过程检验配置-新增',
      editProcessConfig: '过程检验配置-编辑',
      qualityTrace: '质量追溯',
      singleTrace: '单件追溯',
      batchTrace: '批次追溯',
      trackNoTrace: '跟踪号追溯',
      inspectionRecord: '质检记录',
      inspectionRecordDetail: '质检记录-详情',
      inspectionConfig: '检验配置',
      inspectionConfigAdd: '检验配置-新增',
      inspectionConfigEdit: '检验配置-编辑',
      inspectionReview: '质检评审',
      layerAudit: '分层审核',
      score: '分数',
      organizationManagement: '机构管理',
      userManagement: '用户管理',
      userManagementEdit: '用户管理-编辑',
      roleManagement: '角色管理',
      shiftManagement: '班次管理',
    },
    masterData: {
      masterManagement: '主数据管理',
      masterConfig: '主数据配置',
      materail: '物料',
      materailAdd: '物料-新增',
      materailEdit: '物料-编辑',
      materailDetail: '物料-详情',
      stationConfig: '工位配置',
      defectMode: '缺陷模式'
    },
    oee: {
      masterConfig: '主数据配置',
      shutdownType: '停机类型',
      ratedBeat: '额定节拍',
      reportAnalysis: '报表分析',
      shiftOee: '班次OEE统计',
      shiftYield: '班次产量统计',
      shiftBeat: '班次节拍统计',
      shutdownReason: '停机原因分析',
      dataRecord: '数据记录',
      shutdownRecord: '停机记录',
      yieldRecord: '产量记录',
      oeeBoard: 'OEE看板',
      outputStatistics: '产量爬坡图'
    },
    iiot: {
      middleware: 'IIOT中间件',
      config: '配置',
      opcua: 'OPC UA连接',
      eventRules: '事件规则',
      deviceConnect: '设备连接',
      iiotStation: '工位IIOT追溯',
      opcuaNotificationObject: '通知对象',
      equipmentManage: '设备管理',
      equipmentSetting: '设备配置'
    },
    planManage: {
      planning: '计划管理',
      planOrder: '计划工单',
      Progress: '图形进度',
      planOrderAdd: '计划工单-新增',
      planOrderEdit: '计划工单-编辑',
      orderReport: '工单报表',
      loadAnalysis: '负荷分析',
      processAnalysis: '进度分析',
      bomModifications: 'BOM修改',
      orderSummary: '工单汇总',
      programOverview: '计划总览'
    }
  },
  // 通用
  base: {
    langue: '语言',
    resourceDownload: '资源下载',
    backHome: '返回首页',
    back: '返回',
    exit: '退出',
    zh: '中文',
    en: '英文',
    add: '新增',
    edit: '编辑',
    delete: '删除',
    search: '查询',
    clear: '清空',
    dataList: '数据列表',
    open: '展开',
    close: '收起',
    closed: '关闭',
    role: '角色',
    noRole: '无角色',
    status: '状态',
    tips: '提示',
    confirm: '确认',
    cancel: '取消',
    enable: '启用',
    notEnable: '未启用',
    disable: '禁用',
    No: '序号',
    name: '姓名',
    sex: '性别',
    email: '邮箱',
    phone: '手机号',
    employeeId: '工号',
    company: '公司',
    ID: '身份证',
    organization: '组织',
    account: '账号',
    operation: '操作',
    remove: '移除',
    level: '层级',
    system: '系统',
    catalog: '目录',
    menu: '菜单',
    button: '按钮',
    other: '其他',
    factory: '工厂',
    workShop: '车间',
    workSection: '工段',
    line: '产线',
    station: '工位',
    equipment: '设备',
    type: '类型',
    code: '编码',
    originName: '名称',
    superior: '上级',
    remark: '备注',
    supplier: '供应商',
    customer: '客户',
    save: '保存',
    time: '时间',
    startTime: '开始时间',
    endTime: '结束时间',
    whetherEnable: '是否启用',
    shift: '班次',
    batchDelete: '批量删除',
    baseInfo: '基本信息',
    age: '年龄',
    position: '职位',
    wx: '微信号',
    number: '数量',
    unit: '单位',
    qualified: '合格',
    unqualified: '不合格',
    createTime: '创建时间',
    detailsView: '查看',
    revoke: '撤销',
    productCode: '产品编码',
    productName: '产品名称',
    mateCode: '物料编码',
    mateName: '物料名称',
    mateNo: '物料号',
    storage: '库位',
    orderNo: '工单号',
    orderCode: '工单编码',
    process: '工序',
    operator: '操作人员',
    operateTime: '操作时间',
    inStation: '进站',
    outStation: '出站',
    trackNo: '跟踪号',
    batchNo: '批次号',
    supplierCode: '供应商编码',
    supplierName: '供应商名称',
    output: '产出',
    inspection: '检验',
    result: '结果',
    inspector: '检验人',
    inspectionTime: '检验时间',
    detail: '详情',
    creator: '创建人',
    deviceCode: '设备编码',
    factoryModel: '工厂模型',
    productionObject: '生产对象',
    dimension: '维度',
    yes: '是',
    no: '否',
    date: '日期',
    month: '月份',
    export: '导出',
    total: '总计',
    currentShift: '当天班次',
    error: '错误',
    h: '时',
    m: '分',
    s: '秒',
    startDate: '开始日期',
    endDate: '结束日期',
    timeTo: '至',
    copy: '复制',
    config: '配置',
    GraphicalConfiguration: '图形化配置',
    processCode: '工序编号',
    processName: '工序名称',
    processType: '工序类型',
    upload: '上传',
    StationNumber: '工位编号',
    stationName: '工位名称',
    toolingNumber: '工装编号',
    toolingName: '工装名称',
    submit: '提交',
    addTo: '添加',
    customerCode: '客户编码',
    customerName: '客户名称',
    department: '部门'
  },
  // 消息提示
  message: {
    confirm: '是否确认操作?',
    confirmDelete: '是否确认删除?',
    deleteSuccess: '删除成功',
    addSuccess: '新增成功',
    editSuccess: '编辑成功',
    modifySuccess: '修改成功',
    saveSuccess: '保存成功',
    submitSuccess: '提交成功',
    confirmRemoveUser: '确认移除该用户吗?',
    removeSuccess: '移除成功',
    searchFail: '数据查询失败',
    noData: '无数据',
    selectAtLeastOne: '请至少选择一项进行操作',
    operationSuccess: '操作成功',
    emptySubmit: '不能提交空数据',
    selectReviewData: '请选择评审数据',
    inspectionReviewSubmit: '待质检状态才可以提交质检',
    matchAllInspectionList: '是否匹配所有满足该条件的待质检清单?',
    selectFourSame: '勾选数据应属于同一物料、批次、仓库、质检范围',
    selectOneFullCheck: '全检类型仅能勾选一条进行质检评审',
    revokeSuccess: '撤销成功'
  },
  // 表单
  form: {
    keySearch: '请输入关键字搜索',
    selectRole: '请选择角色',
    pleaseInput: '请输入',
    pleaseSelect: '请选择',
    selectPermission: '请选择权限',
    selectType: '请选择类型',
    inputCode: '请输入编码',
    inputOriginName: '请输入名称',
    inputPhone: '请输入手机号',
    inputEmail: '请输入邮箱',
    emailValidate: '请输入正确的邮箱格式',
    inputPassWord: '请输入密码',
    inputPassWordMore: '请再次输入密码',
    inputAccount: '请输入账号',
    inputAge: '请输入年龄',
    inputName: '请输入姓名',
    selectSex: '请选择性别',
    inputIdentityId: '请输入身份',
    inputEmployeeId: '请输入工号',
    inputID: '请输入身份证号',
    IDValidate: '请输入正确的身份证格式',
    inputJob: '请输入职位',
    selectCompany: '请选择公司',
    inputWx: '请输入微信号',
    selectUserType: '请选择用户类型',
    selectPartner: '请选择合作伙伴',
    selectDept: '请选择部门',
    pleaseInputKeyWord: '请输入关键词',
    selectFactory: '请选择工厂',
    selectWorkShop: '请选择车间',
    selectSection: '请选择工段',
    selectLine: '请选择产线',
    selectStation: '请选择工位',
    selectEquipment: '请选择设备',
    inputProductNum: '请输入产品编号',
    inputProductName: '请输入产品名称',
    selectProductObjectType: '请选择生产对象类型',
    inputProductObjectNum: '请输入生产对象编号',
    inputProductObjectName: '请输入生产对象名称',
    inputRatedBeat: '请输入额定节拍',
    selectMonth: '请选择月份',
    selectWeek: '请选择周',
    selectDimension: '请选择维度',
    selectShutdownType: '请选择停机类型',
    selectShutdownReason: '请选择停机原因',
    selectIsPlanned: '请选择是否计划停机',
    selectStartTime: '请选择开始时间',
    selectEndTime: '请选择结束时间',
    inputShutdownDesc: '请输入停机描述',
    inputNumber: '请输入数量',
    inputActualBeat: '请输入实际节拍',
    selectOutputTime: '请选择产出时间',
    selectQualified: '请选择是否合格',
    selectOnePass: '请选择是否一次通过',
    selectDateOrWeek: '请选择日期或周',
    selectMatCode: '请选择物料编码',
    systemBringsOut: '系统带出'
  },
  // 功能模块

  /**
   * 用户中心-----------------------------------------------------------
   */

  // 角色配置
  role: {
    member: '角色成员',
    authSetting: '权限配置',
    roleName: '角色名称',
    roleCode: '角色编码',
    addRole: '新增角色',
    editRole: '编辑角色',
    managementScope: '管理范围',
    permissionChange: '权限变更',
    permissionChangeSuccess: '权限变更成功',
    inputName: '请输入角色名称',
    inputCode: '请输入角色编码'
  },
  // 权限管理
  permission: {
    permissionType: '权限类型',
    permissionCode: '权限编码',
    permissionName: '权限名称',
    addPermission: '新增权限',
    editPermission: '编辑权限',
    permissionChange: '权限变更',
    selectType: '请选择权限类型',
    inputName: '请输入权限名称',
    inputRoute: '请输入路由地址',
    inputCode: '请输入权限编码'
  },
  // 字典管理
  dict: {
    dictName: '字典名称',
    dictCode: '字典编码',
    dictType: '字典类型',
    dictValue: '字典值',
    dictGroup: '字典组',
    dictExplain: '字典说明',
    dictLabel: '字典标签',
    dictSort: '字典排序',
    addDict: '新增字典',
    editDict: '编辑字典',
    dictChange: '字典变更',
    dictChangeSuccess: '字典变更成功'
  },
  // 工厂模型
  factory: {
    pleaseInput: '请输入部门或车间',
    addOrganization: '新增组织',
    chooseModel: '请选择工厂模型'
  },
  // 组织架构
  organ: {
    addOrganization: '新增组织',
    inputOrganName: '请输入组织名称',
    selectOrganType: '请选择组织类型',
    selectOrgan: '请选择组织',
    superiorOrgan: '上级组织',
    organType: '组织类型',
    organName: '组织名称'
  },
  // 合作伙伴
  partner: {
    partner: '合作伙伴',
    code: '合作伙伴编码',
    name: '合作伙伴名称',
    type: '合作伙伴类型',
    contact: '联系人',
    phone: '联系电话',
    address: '地址',
    remark: '备注',
    inputCode: '请输入合作伙伴编码',
    inputName: '请输入合作伙伴名称',
    inputContact: '请输入联系人',
    inputPhone: '请输入联系电话',
    inputAddress: '请输入地址'
  },
  // 个人中心
  person: {
    personalData: '个人资料',
    accountInfo: '账号信息',
    password: '密码',
    newPassWord: '新密码',
    confirmPassWord: '确认密码',
    noMatchPassWord: '两次输入密码不一致',
    modifySuccess: '修改密码成功',
    phoneEmpty: '手机号不能为空',
    phoneFormat: '手机号格式不正确'
  },
  //   班次管理
  shift: {
    code: '班次编码',
    name: '班次名称',
    selectFactory: '请选择工厂',
    inputCode: '请输入班次编码',
    inputName: '请输入班次名称',
    selectStart: '请选择开始时间',
    selectEnd: '请选择结束时间'
  },
  //   用户管理
  user: {
    checkUser: '请勾选用户',
    confirmReset: '确认重置该用户密码？',
    resetSuccess: '密码重置成功',
    userList: '用户清单',
    userNumber: '名成员',
    pleaseInput: '请输入用户名/昵称/手机号',
    addUser: '新增用户',
    batchAssignRoles: '批量分配角色',
    deptMove: '移入部门',
    resetPassWord: '重置密码',
    userType: '用户类型',
    externalUsers: '外部用户',
    internalUsers: '内部用户',
    roleAssign: '角色分配',
    jurisdictionScope: '管辖范围',
    partner: '合作伙伴',
    editUser: '编辑用户',
    allocateSuccess: '分配成功'
  },

  /**
   * 质量管理-----------------------------------------------------------
   */

  // 采购质量
  purchase: {
    qualityOrder: '质检任务单号',
    warehousOrder: '入库单号',
    mateCode: '物料编码',
    mateName: '物料名称',
    trackNo: '跟踪号',
    supplierCode: '供应商编码',
    supplierName: '供应商名称',
    qualityResult: '质检结果',
    taskStatus: '任务状态',
    inspectionTime: '检验时间',
    inspectionList: '质检任务清单',
    damageNum: '破坏数量',
    batchNo: '批次号',
    trackNoType: '跟踪号类型',
    trayNo: '托盘号',
    storageNo: '库位号',
    inspector: '检验人',
    unqualifiedValidate: '请选择同仓库的数据才能进行不合格操作',
    revokeSuccess: '回撤成功',
    defective: '不合格品',
    stashLocation: '存放库位',
    emptyValidate: '库区、分区、库位数据都不能为空',
    SN: 'SN查询',
    inventoryRequirementNo: '入库需求单号',
    receiptTime: '收货时间',
    inventoryStatus: '库存状态',
    whetherStrictly: '是否严检',
    inspectionScope: '质检范围',
    inspectionChecklist: '待质检清单',
    inspectionInventoryList: '质检库存清单',
    qualityInspectionItem: '质检项目',
    scrapNumber: '破坏数量',
    trackingNumberSearch: '跟踪号搜索',
    inspectionItem: '检验项目',
    resultType: '结果类型',
    upperLimit: '上限',
    lowerLimit: '下限',
    standardValue: '标准值',
    fileUpload: '附件上传',
    qualityEvaluation: '质检评定',
    qualified: '合格',
    unqualified: '不合格',
    unqualifiedStorage: '不合格存放位置',
    viewSN: '查看SN',
    scrapMoreThanNum: '破坏数量不能大于数量',
    resultEmpty: '检验结果不能为空',
    selectUnqualifiedStorage: '请选择不合格品存放位置',
    inspectionNo: '质检单号',
    inspectionPerson: '质检人员',
    confirmRevoke: '是否确认撤销该质检记录?',
    inspectionItemConfig: '检验项目配置',
    deleteConfigConfirm: '是否删除该配置项？',
    inspectionExist: '该检验项已存在',
    pleaseAddConfig: '请添加配置项'
  },
  // 过程控制
  processControl: {
    inventory: '库存清单',
    thaw: '解冻',
    controlType: '卡控类型',
    controlStartTime: '卡控开始时间',
    releaseList: '放行记录列表',
    submissionTime: '提报时间',
    unqualifiedNo: '不合格品编号',
    unqualifiedList: '不合格品清单',
    defectNum: '缺陷数量',
    defectMode: '缺陷模式',
    defectRemarks: '缺陷备注',
    occurrenceProcess: '发生工序',
    productionProcess: '产生工序',
    submissionProcess: '提报工序',
    reviewResult: '评审结果',
    processingStatus: '处理状态',
    review: '评审',
    reviewComments: '评审意见',
    qualityInfo: '质量信息',
    materialLoss: '物料损耗',
    scrapType: '报废类型',
    batchStatua: '批产状态',
    whetherDebugging: '是否调试件',
    singleCode: '单件码',
    consumedQuantity: '消耗数量',
    disassemble: '拆解',
    scrap: '报废',
    repair: '返修',
    compromise: '让步',
    reviewCommentsValidate: '评审意见不能为空！',
    cannotSubmit: '处理状态已改变，不能提交'
  },
  // 过程检验
  processInspection: {
    orderStartTime: '工单计划开始时间',
    firstCheckFrequency: '首检次数',
    lastCheckFrequency: '末检次数',
    spotCheckFrequency: '抽检次数',
    firstCheck: '首检',
    lastCheck: '末检',
    spotCheck: '抽检',
    spotCheckProduct: '抽检产品',
    initDelete: '初始第一项检验项目不能删除',
    confirmDelete: '确认删除该抽检产品及对应检验数据吗？',
    lastInspectionTime: '最近一次检验时间',
    inspectionNo: '检验单号',
    checkItemValidate: '每个表格的检验项结果都必填，不能为空！',
    checkSuccess: '检验任务操作成功！',
    samplingRules: '抽检规则',
    productionNum: '生产数量',
    spotNum: '抽检数量',
    allowUnqualified: '允许不合格数量',
    inspectionItems: '检验项目',
    processStandards: '工艺标准',
    standardValue: '检验标准值',
    errorRange: '误差范围',
    checkType: '检验类型',
    checkResult: '检验结果',
    resultType: '结果类型',
    configured: '已配置',
    notConfigured: '未配置',
    mateValidate: '物料编号不能为空！',
    addInspection: '新增检验项',
    addSamplingRules: '添加抽检规则',
    signalUrl: '信号URL',
    removefromBack: '当前后面有配置的抽检规则不能移除，要移除的话请从后面往前移除！',
    spotItem: '抽检项目',
    input: '手动输入'
  },
  //  质量追溯
  qualityTrace: {
    mateconsumption: '物料消耗',
    singleBind: '单件绑定',
    processParameters: '过程参数',
    productionRecords: '生产记录',
    productionTime: '生产时间',
    usedNum: '使用数量',
    singleBar: '单件条码号',
    offLineTime: '下线时间',
    forwardTrace: '正向追溯',
    reverseTrace: '反向追溯',
    conversionRecord: '转换记录',
    batchAttribute: '批次属性',
    orderInfo: '工单信息',
    inventoryInfo: '库存信息',
    traceValidate: '批次号和物料编号需要同时输入才能追溯！',
    batchOld: '原批次号',
    batchNew: '新批次号',
    mateOld: '原物料号',
    mateNew: '新物料号',
    numOld: '原数量',
    numNew: '新数量',
    documentNo: '单据号',
    documentType: '单据类型',
    productionTime: '生产日期',
    productGrade: '产品等级',
    deliveryTime: '收货日期',
    productionBatch: '生产批号',
    productionOrder: '生产工单',
    productionShift: '生产班次',
    expiringTime: '失效日期',
    trackValidate: '跟踪号不能为空！',
    mateInput: '物料投入',
    mateOutput: '物料产出',
    inStationTime: '进站时间',
    outStationTime: '出站时间'
  },
  /**
   * 主数据管理
   */
  // 物料
  // material: {
  //   category: '制品分类',
  //   materDesc: '物料描述',
  //   mateList: '物料数据列表',
  //   bigCategory: '物料大类',
  //   smallCategory: '物料小类',
  //   unit: '计量单位',
  //   specifications: '规格说明',
  //   qualityCheck: '质检',
  //   needQualityCheck: '是否质检',
  //   deadlineWarning: '库存有效期提前预警天数',
  //   batchManage: '启用批次管理',
  //   batchName: '批次属性名称',
  //   batchCode: '批次属性编码',
  //   inputControl: '输入控制',
  //   batchRule: '批次规则',
  //   storageRule: '仓储规则',
  //   expirationDateManage: '启用有效期管理',
  //   qualityPeriod: '保质期天数',
  //   qualityDisabledPeriod: '保质期禁收天数',
  //   storageType: '物料存储类型'
  // },

  //主数据管理-物料
  material: {
    materialDataList: '物料数据列表',
    productClassification: '制品分类',
    materialCategories: '物料大类',
    materialSubcategory: '物料小类',
    specifications: '规格说明',
    IsQualityInspection: '是否质检',
    qcScope: '质检范围',
    materialDescription: '物料描述',
    InventoryValidityDays: '库存有效期提前预警天数',
    batchRules: '批次规则',
    EnableBatchManagement: '启用批次管理',
    BatchAttributeName: '批次属性名称',
    BatchAttributeCode: '批次属性编码',
    inputControl: '输入控制',
    enable: '启用',
    warehousingRules: '仓储规则',
    shelfLifeDays: '保质期天数',
    shelfLifeProhibitedDays: '保质期禁收天数',
    materialStorageType: '物料存储类型',
    enableExpirationManagement: '启用有效期管理',
    shelfLifeFill: '请填写保质期天数',
    shelfLifeProhibited: '请填写保质期禁收天数'
  },

  //装箱
  packing: {
    numberPackingSpecifications: '装箱规格数',
    packagingMaterialNumber: '包装材料编号',
    packagingMaterialName: '包装材料名称',
    packingList: '装箱列表',
    deleteMeg: '确定删除该条装箱数据吗?',
    PackingSpecifications: '装箱规格',
    codeRepeat: '编号不能重复'
  },

  //物料BOM
  materialBom: {
    BOMNumber: 'BOM编号',
    BOMUse: 'BOM用途',
    IsItValid: '是否有效期',
    WhetherToEnable: '是否启用',
    Version: '版本',
    ActivationTime: '启用时间',
    ExpirationTime: '到期时间',
    BOMList: 'BOM列表'
  },

  //工艺路径
  processPath: {
    RoutingNumber: '工艺路线编号',
    RoutingName: '工艺路线名称',
    standardBeat: '标准节拍',
    graphical: '新增（图形化）',
    ProcessPathList: '工艺路径列表',
    IsDue: '是否到期',
    baseInfo: '基础信息',
    tips: '注意: 切换BOM编号会清除上料数据',
    isFirstProcessFeeding: '是否首工序投料',
    BomMeg: 'BOM异常，请检查BOM配置!',
    FunctionConfiguration: '功能配置',
    processName: '工序名称',
    AddProcess: '新增工序',
    IntroduceProcess: '引入工序',
    BasicConfiguration: '基础配置',
    WorkstationInformation: '工位信息',
    MaterialInput: '物料投入',
    MaterialOutput: '物料产出',
    WorkwearInformation: '工装信息',
    addStation: '添加工位',
    addWorkClothes: '添加工装',
    Workwearuse: '工装使用',
    QualityInspection: '质检检验',
    IsCloseOrder: '是否可以关闭工单',
    binConfig: '库位配置',
    AddMaterial: '添加物料',
    BatchFill: '批量填充',
    BOMUsage: 'BOM用量',
    InvestmentMethod: '投入方式',
    SourceType: '来源类型',
    SourceLocation: '来源库位',
    ConsumptionLocation: '消耗库位',
    maximumFeeding: '最大投料数',
    FeedStock: '投料库存预指',
    DeductionRules: '扣减规则',
    LoadingStation: '上料工位指定',
    consumptionMethod: '消耗方式',
    SubpartScan: '子零件扫描',
    MaterialWarehouse: '原料仓库',
    isSubmit: '是否报交',
    outboundMode: '出站方式',
    outboundLabel: '出站标签',
    autoboxing: '是否自动装箱',
    WhetherRetrace: '是否回扫',
    labelTemplate: '标签模版',
    WhetherUseContainers: '是否使用容器',
    QualityControl: '质量卡控',
    StandstillControl: '静置卡控',
    controlDuration: '卡控时长',
    consumptionType: '消耗类型',
    DesignatedWorkstation: '指定工位',
    fileName: '文件名称',
    fileType: '文件类型',
    uploadName: '上传人',
    uploadTime: '上传时间',
    isPassProcess: '是否过站工序',
    PleaseSelectRouting: '请输入工艺路线名称',
    PleaseSelectMaterial: '请选择物料编码',
    PleaseSelectPurpose: '请选择用途',
    PleaseSelectBOM: '请选择BOM编号',
    PleaseEnterStandardBeat: '请输入标准节拍',
    Processlist: '工序清单',
    PleaseSelectLine: '请选择产线',
    DragIntoCanvas: '（拖入画布中）',
    PleaseMatNoData: '选择物料数据不能为空!',
    processConfig: '工序配置',
    processList: '工序列表',
    AddTooling: '添加工装'
  },

  // 工位配置
  stationConfig: {
    factoryModel: '工厂模型',
    stationConfig: '工位配置',
    stationName: '工位名称',
    stationCode: '工位编号',
    stationType: '工位类型',
    storageConfig: '库位配置',
    warehouse: '仓库',
    onlineWarehouse: '线上库',
    offlineWarehouse: '线下库',
    associatedStorage: '关联库位',
    outboundStorage: '出站库',
    isolationStorage: '隔离库',
    scrapStorage: '报废库',
    printerSettings: '打印机设置',
    printer: '打印机',
    materialPullConfig: '物料拉动配置',
    packagePull: '包材拉动',
    toolPull: '工装拉动'
  },
  // 缺陷模式
  defectMode: {
    defectMode: '缺陷模式',
    defectName: '缺陷名称',
    defectCode: '缺陷编号',
    defectModeList: '缺陷模式列表',
    processCode: '工序编号'
  },
  /**
   * OEE-----------------------------------------------------------
   */
  // oee主数据配置
  oeeMaster: {
    shutdownType: '停机类型',
    shutdownReason: '停机原因',
    addType: '新建类型',
    stateColor: '状态颜色',
    isPlanned: '是否计划性停机',
    ratedBeat: '额定节拍',
    productionObjNum: '生产对象编号',
    productionObjName: '生产对象名称',
    productionObjType: '生产对象类型'
  },
  // oee报表分析
  oeeReport: {
    statisticalAnalysis: '统计分析',
    detailList: '明细表',
    shiftOee: '班次OEE分析',
    dailyAverage: '日平均节拍',
    shiftBeat: '班次节拍分析',
    shiftYield: '班次产量分析',
    stopReason: '停机原因分析',
    stopDetail: '停机原因明细',
    timeLossType: '时间损失分析(停机类型)',
    timeLossReason: '时间损失分析(停机原因)',
    shutdownDuration: '停机时长',
    reasonType: '原因类型',
    shutdowmDesc: '停机描述'
  },
  // oee数据记录
  oeeDataLog: {
    actualBeat: '实际节拍',
    outputTime: '产出时间',
    isQualified: '是否合格',
    isOnePass: '是否一次通过'
  },
  // oee看板及产量爬坡图
  oee: {
    qualityInfo: '质量信息',
    dataMonitoring: '数据监控',
    tomeLossRecord: '时间损失记录',
    tomeLossAnalysis: '时间损失分析',
    runningTime: '运行时间',
    shutdownDuration: '停机总时长',
    about: '约',
    timeRate: '时间开动率',
    performanceRate: '性能开动率',
    qualityRate: '质量合格率',
    innerCircle: '内圈小',
    outputStatistics: '产量爬坡图',
    unit: '件',
    shiftTarget: '每班目标',
    shifyActual: '实际产量',
    hourYield: '小时产量',
    ngCount: 'NG数量',
    date: '日期',
    durationEdit: '时段编辑',
    shutdownTotal: '总停机时段',
    shutdownDuration: '停机时段',
    timeTo: '至',
    addShutdownStatus: '添加停机状态',
    descReson: '具体原因描述',
    notHaveTime: '剩余停机时长不足',
    notFullyAllocated: '停机时长未分配完，请继续!',
    timeExceed: '停机时长已超出，请合理分配!',
    cannotEmpty: '停机时长、停机类型、停机原因不能为空!',
    selectDuration: '请选择停机时长',
    overTotalTime: '已大于总时长',
    timeTmpty: '时间不能为空',
    qualifiedNum: '合格品数',
    onceQualifiedNum: '一次合格数',
    scrapNum: '报废数'
  },
  /**
   * iiot中间件-----------------------------------------------------------
   */
  // iiot配置
  iiot: {
    connectName: '连接名称',
    connectList: 'OPC UA连接清单',
    connectUrl: '连接URL',
    vertify: '身份验证',
    userName: '用户名',
    connectTest: '测试连接',
    connectSuccess: '测试连接成功',
    connectFail: '测试连接失败',
    inputConnectName: '请输入连接名称',
    inputConnectUrl: '请输入连接URL',
    inputUserName: '请输入用户名',
    inputPassword: '请输入密码',
    opcua: 'OPC UA',
    params: '参数',
    shutdownRecord: '停机记录',
    qualifiedNum: '合格数',
    unqualifiedNum: '不合格数',
    signalMode: '信号模式',
    switchMode: '开关模式',
    changeMode: '变化模式',
    countMode: '计数模式',
    shutdownSingal: '停机信号',
    shutdownSignalList: '停机信号清单',
    mode: '模式',
    digitalSignal: '数字信号',
    beatSignal: '节拍信号',
    qualifiedSignal: '合格信号',
    unqualifiedSignal: '不合格信号',
    signalThreshold: '信号维持时常阀值(秒)',
    standardValue: '标准值',
    productNum: '产品料号',
    outputNum: '产量料号',
    designStaion: '指定工位',
    accordingDevice: '按设备信号',
    accordingStation: '按工位工单',
    opcuaEmpty: 'OPC UA数据不能为空！',
    processParamsConfig: '加工参数配置',
    processParamsName: '加工参数名称',
    setValue: '设定值',
    limit: '上下限Tag',
    codeValue: '条码值',
    whetherMainCode: '是否主条码',
    configCodeEmpty: '加工参数配置或者条码值数据不能为空',
    paramsValid: '新增加工参数配置每行的参数为必填项且数量不能为0！',
    codeValid: '新增条码配置每行的参数为必填项！',
    modelCode: '模版编号',
    objectNo: '对象编号',
    objectName: '对象名称',
    objectType: '通知对象类型',
    messageModule: '消息模版',
    deviceName: '设备名称'
  },
  /**
   * 计划管理-----------------------------------------------------------
   */
  // 计划管理
  planManage: {
    // // --------- 计划工单 ---------
    // orderCode: '工单编号',
    // technologyCode: '工艺编号',
    // technologyName: '工艺名称',
    // customerNum: '客户号',
    planStartTime: '计划开始时间',
    planEndTime: '计划结束时间',
    // planOrderList: '计划工单列表',
    // versionNum: '版本号',
    // orderStatus: '工单状态',
    // productStatus: '生产状态',
    // revoke: '撤回',
    // release: '发布',
    // planMaterial: '计划备料',
    // completenessInspect: '齐套性检查',
    // selectOne: '请选择一项',
    // selectAtLeastOne: '请至少选择一项',
    // releaseSuccess: '发布成功',
    // orderType: '工单类型',
    // packageCode: '包装材料编号',
    // newProcessPath: '新工艺路径',
    // processPath: '工艺路径',
    // processPathVersion: '工艺路径版本',
    // processPathName: '工艺路径名称',
    // firstProcess: '是否首工序投料',
    // bySystem: '系统带出',
    // selectProcess: '选择标准工艺',
    // editProcess: '编辑工艺路径',
    // editBom: '编辑BOM',
    // afterProcess: '选择标准工艺后带出',
    // selectTechnology: '选择工艺',
    // minFeeding: '最低投料数',
    // downloadTemplate: '下载模板',

    // // 计划备料
    planNumber: '计划数量',
    // bomUse: 'BOM用量',
    // prepareNum: '备料数量',

    // // 齐套性检查
    // rawMaterialWarehouse: '原材料仓',
    // inventoryQuantity: '库存数量',
    // whetherMeetRequire: '是否满足生产',
    // bomInfo: 'BOM信息',
    // packageInfo: '包材信息',
    // packageSpecifications: '装箱规格',

    // // --------- 工单报表 ---------
    orderDetail: '工单明细表',
    timeRange: '时间范围',
    orderPlanNum: '工单计划数量',
    actualNum: '实际生产数量',
    actualStartTime: '实际开始时间',
    actualEndTime: '实际结束时间',
    consumptionList: '消耗清单',
    unqualifiedNum: '不合格数量',
    processProgress: '工序进度',
    processCode: '工序编号',
    processName: '工序名称',
    outputTime: '产出时间',
    completeRate: '工序完成率',
    useQuantity: '消耗数量',
    notStart: '未开始',
    inProgress: '进行中',
    completed: '已完成',
    unknown: '未知',
    mateConsumption: '物料消耗',
    planProductionTime: '计划生产时间',
    attendanceTime: '出勤时间',
    attendanceNum: '出勤数量',
    overloadTime: '超负荷时间',
    overloadNum: '超负荷数量',
    deviceCapacity: '设备产能',
    hours: '小时'
  },
  // 分层审核
  layerAudit: {
    scoreList: '分数列表',
    scoringCriteria: '评分标准',
    scoreValue: '分数值',
    organManagement: '机构管理',
    synchronous: '同步',
    organName: '机构名称',
    workShopID: '车间ID',
    workShopName: '车间名称',
    workSectionID: '工段ID',
    workSectionName: '工段名称',
    lineID: '产线ID',
    lineName: '产线名称',
    whetherLogisticsArea: '是否物流区',
    organizationalLoading: '组织架构获取中...',
    inputOrganName: '请填写机构名称',
    syncSuccess: '同步成功',
    confirmSynchronization: '确定同步',
    updateTime: '更新时间',
    allUser: '全部用户',
    syncUser: '同步用户',
    userdataSynchronizing: '用户数据同步中...',
    userRoles: '用户角色',
    addRole: '添加角色',
    roleName: '角色名称',
    roleLevel: '角色级别',
    roleStatus: '角色状态',
    belongingFactory: '所属工厂',
    belongingWorkshop: '所属车间',
    belongingWorkSection: '所属工段',
    belongingLine: '所属产线',
    whetherInvalid: '是否失效',
    factoryLevel: '工厂级',
    workshopLevel: '车间级',
    workSectionLevel: '工段级',
    lineLevel: '产线级',
    active: '激活',
    notActive: '未激活',
    belongingShift: '所属班次',
    shiftStartTime: '班次开始时间',
    shiftEndTime: '班次结束时间',
    roleList: '角色列表',
    shiftList: '班次列表'
  }
}
