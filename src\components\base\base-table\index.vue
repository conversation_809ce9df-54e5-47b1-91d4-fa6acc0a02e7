<script setup>
import { ref, getCurrentInstance, computed } from 'vue'
const instance = getCurrentInstance()
const emit = defineEmits(['pageChange'])
const props = defineProps({
  // data
  // 分页信息
  page: {
    type: Object,
    default() {
      return null
    }
  },
  // 是否占满, 父元素为flex时可用
  fill: {
    type: Boolean,
    default() {
      return false
    }
  },
  // 表格适应模式
  tableLayout: {
    type: String,
    default() {
      return 'auto'
    }
  },
  // 分页包含内容
  pageLayout: {
    type: String,
    default() {
      return 'total, sizes, prev, pager, next, jumper'
    }
  },
  // 表头是否固定(这个要配合 tableLayout 为 auto 时)
  fixed: {
    type: Boolean,
    default() {
      return false
    }
  }
})
const tableHeight = ref(0)
const pageData = ref(props.page)

//宽高发生变化时 触发
const onResize = (e) => {
  tableHeight.value = e.height
}
//分页修改 触发
const pageChange = () => {
  emit('pageChange', pageData.value)
}

const table = computed(() => instance.refs.table)

// 表头固定: tableLayout 为 auto 并且 fixed 为 true 时, 设置表头固定
const FixedTableHeader = computed(() => {
  const isFixedHeader = props.fixed && props.tableLayout === 'auto'
  if (isFixedHeader) {
    return { position: 'sticky', top: 0, 'z-index': 10 }
  }
  return null
})

// #Expose
defineExpose({
  table
})
</script>

<template>
  <div class="base-table" :class="[{ 'is-fill': fill }]">
    <div class="base-table-box" v-resize="onResize">
      <el-table :height="tableHeight" v-bind="$attrs" :table-layout="tableLayout" ref="table" :header-row-style="FixedTableHeader" :class="`table-layout-${tableLayout}`">
        <slot />
      </el-table>
    </div>
    <div class="base-table-page" v-if="pageData">
      <el-pagination
        background
        :small="pageData.small ? true : false"
        style="float: right"
        v-model:currentPage="pageData.page"
        v-model:page-size="pageData.size"
        :page-sizes="[10, 20, 50, 100, 200]"
        :layout="pageLayout"
        :total="pageData.total"
        @size-change="pageChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-table {
  // height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  :deep(thead tr th .cell) {
    white-space: nowrap;
  }
}
.is-fill {
  height: 0;
  flex: 1;
  margin-bottom: 2px;
}
.base-table-box {
  height: 0;
  flex: 1;
  flex-basis: 0;
  margin-bottom: 8px;
}
:deep .el-pager,
.el-pagination > .is-first {
  z-index: 0;
}
:deep .el-table--border:after {
  height: 100%;
}
:deep .el-table--border:before {
  height: 0;
}
</style>
