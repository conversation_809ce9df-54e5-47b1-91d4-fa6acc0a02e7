import request from '@/common/request.js'

/*
 *  获取菜单&权限
 *  @param { void }
 *  @returns { Promise }
 */
export const GetAuthMenu = () => {
  return request({
    url: '/api/v1/menus/resources',
    method: 'GET'
  })
}

/*
 *  获取菜单列表
 *  @param { keywords: String, status: 0:隐藏 1:显示 }
 *  @returns { Promise }
 */
export const GetMenuList = (params) => {
  return request({
    url: '/api/v1/menus/query',
    method: 'GET',
    params
  })
}

/*
 *  获取权限选项
 *  @param { void }
 *  @returns { Promise }
 */
export const GetAuthOptions = () => {
  return request({
    url: '/api/v1/menus/options',
    method: 'GET'
  })
}

/*
 *  获取权限详情
 *  @param { void }
 *  @returns { Promise }
 */
export const GetAuthDetailById = (menuId) => {
  return request({
    url: `/api/v1/menus/${menuId}/info`,
    method: 'GET'
  })
}

/*
 *  修改权限
 *  @param { void }
 *  @returns { Promise }
 */
export const UpdateAuthById = (data) => {
  return request({
    url: `/api/v1/menus/${data.menuId}/update`,
    method: 'PUT',
    data
  })
}

/*
 *  删除权限
 *  @param { id }
 *  @returns { Promise }
 */
export const DelAuthByIds = (ids) => {
  return request({
    url: `/api/v1/menus/${ids}/delete`,
    method: 'DELETE'
  })
}

/*
 *  新增权限
 *  @param { auth }
 *  @returns { Promise }
 */
export const AddAuth = (data) => {
  return request({
    url: `/api/v1/menus/save`,
    method: 'POST',
    data
  })
}
