import { ref } from 'vue'
// import { GetPrinters, DoPrint, GetTemplates, GetPrinterStatus, DoPrintLocal } from '@/api/modules/print'

const printers = ref([])
const templates = ref([])
const templateStatus = ref([])

const defaultPrinter = ref(localStorage.getItem('printer') || '')

async function doPrint({ templateCode, printData, printerName, options = {} }) {
  // return await DoPrint({ templateCode, printData, printerName, options })
  console.log('打印功能已禁用', { templateCode, printData, printerName, options })
  return Promise.resolve()
}

async function doPrintLocal({ templateCode, printData }) {
  // return await DoPrintLocal({ templateCode, printData })
  console.log('本地打印功能已禁用', { templateCode, printData })
  return Promise.resolve()
}

// 节流查询打印机
const throttleGetPrinters = throttle(getPrinters, 1000)
// 节流查询模板
const throttleGetTemplates = throttle(getTemplates, 1000)

async function getPrinters() {
  // printers.value = (await GetPrinters()) || []
  printers.value = []
  return printers.value
}

async function getTemplates() {
  // templates.value = (await GetTemplates()) || []
  templates.value = []
  return templates.value
}

async function getPrinterStatus(printerName, reload = false) {
  // const hasStatus = templateStatus.value.find((item) => item.printerName === printerName)
  // if (!hasStatus || reload) {
  //   templateStatus.value.push({
  //     printerName: printerName,
  //     ...(await GetPrinterStatus(printerName))
  //   })
  // }
  // return templateStatus.value.find((item) => item.printerName === printerName)
  return {
    status: 'disabled',
    message: '打印功能已禁用'
  }
}

async function selectPrinter(printerName) {
  localStorage.setItem('printer', printerName)
  defaultPrinter.value = printerName
}

// 节流
function throttle(fn, delay) {
  let lastTime = 0
  return function () {
    let nowTime = Date.now()
    if (nowTime - lastTime > delay) {
      fn.apply(this, arguments)
      lastTime = nowTime
    }
  }
}

const usePrinter = () => {
  throttleGetPrinters()
  throttleGetTemplates()
  return {
    doPrint,
    doPrintLocal,
    throttleGetPrinters,
    throttleGetTemplates,
    getPrinters,
    getTemplates,
    selectPrinter,
    getPrinterStatus,
    printers,
    templates,
    defaultPrinter
  }
}

export default usePrinter
