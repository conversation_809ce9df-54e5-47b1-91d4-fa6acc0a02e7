// Desc: 打印数据格式化

/**
 * #BOX_A1_V1
 * @name 箱标签-送货标签 type=1
 * @property {String} barcode 条码
 * @property {String} stkinNo 入库需求单号
 * @property {String} suNo 包装号
 * @property {String} matCode 物料编码
 * @property {String} matName 物料名称
 * @property {String} batchCode 批次号
 * @property {String} quant 数量
 * @property {String} supplierCode 供应商编码
 * @property {String} supplierName 供应商名称
 * @returns {Object} printData
 * @description P1,包装号,物料号,批次号,数量,生产日期
 * TODO: 生产日期很多地方没有!
 */
export function boxDataFormat(printData) {
  if (Array.isArray(printData)) {
    return printData.map((i) => {
      return {
        barcode: `P1,${i.suNo || ''},${i.matCode || ''},${i.batchCode || ''},${i.quant || ''},${i.prodDate || ''}`,
        ...i
      }
    })
  } else if (typeof printData === 'object') {
    return {
      barcode: `P1,${printData.suNo || ''},${printData.matCode || ''},${printData.batchCode || ''},${printData.quant || ''},${printData.prodDate || ''}`,
      ...printData
    }
  } else {
    return {}
  }
}

/**
 * #TRA_A1_V1
 * @name 托盘码 type=5
 * @property {String} barcode 条码
 * @property {String} palletCode 托盘码
 * @property {String} binCode 储位
 * @property {String} createBy 创建人
 * @property {Array} dataList 数据列表
 * @property {String} dataList.backNo 回单号
 * @property {String} dataList.matCode 物料编码
 * @property {String} dataList.matName 物料名称
 * @property {String} dataList.batchCode 批次号
 * @property {String} dataList.totalQuant 数量
 * @returns {Object} printData
 * @description PN,托盘码,binCode
 * TODO: 该生成规则未确认！
 */
export function trayDataFormat(printData) {
  if (Array.isArray(printData)) {
    return printData.map((i) => {
      return {
        barcode: `PN,${i.palletCode || ''},${i.binCode || ''}`,
        ...i
      }
    })
  } else if (typeof printData === 'object') {
    return {
      barcode: `PN,${i.palletCode || ''},${i.binCode || ''}`,
      ...printData
    }
  } else {
    return {}
  }
}

/**
 * #PIC_A1_V1
 * @name 拣货单 type=
 * @property {String} barcode 条码
 * @property {String} picktskNo 拣货单号
 * @property {String} stkoutNo 出库需求单号
 * @property {String} reqTime 需求时间
 * @property {String} planOutTime 计划发货时间
 * @property {String} pickPerson 拣货人员
 * @property {String} note 备注
 * @property {String} pickQuantTotal 拣货总数量
 * @property {Array} detail 数据列表
 * @property {Number} detail.index 序号
 * @property {String} detail.suNo 包装号
 * @property {String} detail.batchCode 批次号
 * @property {String} detail.matCode 物料编号
 * @property {String} detail.matName 物料名称
 * @property {String} detail.quant 包装数量
 * @property {String} detail.pickQuant 拣货数量
 * @property {String} detail.binCode 库位
 * @returns {Object} printData
 * @description PN,picktskNo
 * TODO: 该生成规则未确认！
 */
export function pickDataFormat(printData) {
  if (Array.isArray(printData)) {
    return printData.map((i) => {
      return {
        barcode: `PN,${i.picktskNo || ''}`,
        ...i
      }
    })
  } else if (typeof printData === 'object') {
    return {
      barcode: `PN,${printData.picktskNo || ''}`,
      ...printData
    }
  } else {
    return {}
  }
}

/**
 * #SEN_A1_V1
 * @name 送货单 type=3 (与拣货单很像, 拣货单的下一步骤)
 * @property {String} barcode 条码
 * @property {String} picktskNo 拣货单号
 * @property {String} stkoutNo 出库需求单号
 * @property {String} reqTime 需求时间
 * @property {String} planOutTime 计划发货时间
 * @property {String} pickPerson 拣货人员
 * @property {String} note 备注
 * @property {String} orderCode 订单号
 * @property {String} supplierCode 供应商编码
 * @property {String} supplierName 供应商名称
 * @property {String} pickQuantTotal 拣货总数量
 * @property {Array} detail 数据列表
 * @property {Number} detail.index 序号
 * @property {String} detail.suNo 包装号
 * @property {String} detail.batchCode 批次号
 * @property {String} detail.matCode 物料编号
 * @property {String} detail.matName 物料名称
 * @property {String} detail.quant 包装数量
 * @property {String} detail.pickQuant 拣货数量
 * @property {String} detail.binCode 库位
 * @returns {Object} printData
 * @description PN,picktskNo
 * TODO: 该生成规则未确认！
 */
export function sendDataFormat(printData) {
  if (Array.isArray(printData)) {
    return printData.map((i) => {
      return {
        barcode: `PN,${i.picktskNo || ''}`,
        ...i
      }
    })
  } else if (typeof printData === 'object') {
    return {
      barcode: `PN,${printData.picktskNo || ''}`,
      ...printData
    }
  } else {
    return {}
  }
}
