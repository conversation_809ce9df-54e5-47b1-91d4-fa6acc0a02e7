<script setup>
defineProps({
  // slot
  // 盒子头部文字 加粗主题色文字
  title: {
    type: String,
    default() {
      return ''
    }
  },
  icon: {
    type: <PERSON><PERSON><PERSON>,
    default() {
      return false
    }
  },
  sticky: {
    type: <PERSON><PERSON><PERSON>,
    default() {
      return true
    }
  },
  size: {
    type: <PERSON><PERSON><PERSON>,
    default() {
      return false
    }
  },
  line: {
    type: <PERSON><PERSON><PERSON>,
    default() {
      return false
    }
  },
  curColor: {
    type: String,
    default() {
      return '#333'
    }
  }
})
</script>

<template>
  <div class="base-warp-header" :class="[{ 'is-sticky': sticky }]">
    <i class="base-warp-header-title-icon" v-if="icon" />
    <i class="base-warp-header-title-line" v-if="line" />
    <p
      class="base-warp-header-title"
      v-if="title"
      :class="size ? 'f-small' : ''"
      :style="{
        color: curColor
      }"
    >
      {{ title }}
    </p>
    <div class="base-warp-header-right">
      <slot />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.base-warp-header {
  height: auto;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  margin-bottom: $base-padding;
  &.is-sticky {
    position: sticky;
    top: 0;
    z-index: 5;
    background: #ffffff;
  }
  .base-warp-header-title {
    margin: 0 0 0 5px;
    flex-shrink: 0;
    color: $primary-font-color;
    font-weight: 600;
    font-size: $default-font-size;
    &.f-small {
      font-size: $small-font-size !important;
    }
  }
  .base-warp-header-title-icon {
    width: 10px;
    height: 10px;
    margin-left: 8px;
    transform: rotateZ(45deg);
    display: inline-block;
    background-color: $primary;
  }
  .base-warp-header-title-line {
    height: 12px;
    width: 2px;
    margin-left: 8px;
    display: inline-block;
    background-color: #3277f5;
  }
  .base-warp-header-right {
    width: 0;
    flex: 1;
    text-align: right;
  }
}
</style>
