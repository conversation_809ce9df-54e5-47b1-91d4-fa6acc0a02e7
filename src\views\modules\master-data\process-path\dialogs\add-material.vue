<script setup>
import { ref, reactive, computed, nextTick, getCurrentInstance } from 'vue'
import { GetMaterialBomItem } from '@/api/modules/mes'
import { useDictStore } from '@/stores/dict'
import { deepClone, indexMethod } from '@/common/utils'
import { ElMessage } from 'element-plus'

const instance = getCurrentInstance()
const emit = defineEmits(['determine'])
const dictStore = useDictStore()

// 弹框和表单配置项
const dialogVisible = ref(false)
const dialogOptions = reactive({
  title: '添加物料',
  width: '50%',
  closeOnClickModal: false,
  destroyOnClose: true
})

const formOptions = computed(() => ({
  size: 'default',
  // labelWidth: '120px',
  statusIcon: true,
  model: formData.value,
  inline: true
}))
const formData = ref({
  matCode: ''
})
// 分页
const page = ref({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const selectionList = ref([])
const collectList = ref([])
const bomNo = ref('')
const opCode = ref('')
// 打开弹框
const openDialog = async (list = [], data) => {
  collectList.value = []
  selectionList.value = []
  formData.value = { matCode: '' }
  page.value = { page: 1, size: 10, total: 0 }
  dialogVisible.value = true

  bomNo.value = data.bomNo
  opCode.value = data.opCode
  collectList.value = list
  await queryData()
  await nextTick()
}

// 查询
const queryData = async () => {
  try {
    loading.value = true
    // TODO 请求数据
    const res = await GetMaterialBomItem(page.value, { bomNo: bomNo.value, ...formData.value })
    page.value.total = res.total
    tableData.value = res.list.map((item) => {
      return {
        matCode: item.matCode, matName: item.matName, opCode: opCode.value, unit: item.unit, quant: item.quant,
        quantAssistant: item.quantAssistant,
        unitAssistant: item.unitAssistant,
        productionClass: item.productionClass,
        matClass1: item.matClass1,
        matClass2: item.matClass2
      }
    })

    nextTick(() => {
      const tableRef = instance.refs.tableRef.table
      tableData.value.forEach((item) => {
        // isCheck 用于已存在的工序变禁用勾选，false: 就是禁用, true: 不禁用
        if (collectList.value.find((sele) => sele.matCode === item.matCode)) {
          item.isCheck = false
          tableRef.toggleRowSelection(item, true)
        } else {
          item.isCheck = true
        }
      })
    })
  } catch (e) {
    page.value.total = 0
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 清空
const onClear = () => {
  page.value.page = 1
  formData.value = { matCode: '' }
  queryData()
}

const checkSelectSet = (row) => {
  // false: 就是禁用, true: 不禁用
  return row.isCheck
}

// 勾选
const handleSelectionChange = (list) => {
  console.log(list, 3333)
  selectionList.value = list.map((item) => {
    return {
      opCode: item.opCode || null,
      matCode: item.matCode || '',
      matName: item.matName || '',
      quant: item.quant || '',
      quantAssistant: item.quantAssistant || '',
      unit: item.unit || '',
      unitAssistant: item.unitAssistant || '',
      isWip: false,
      bomQuant: item.quant || '',
      // 勾选时不设置默认值，只初始化为空
      fromWhsCode: '',
      feedSuType: '',
      fromType: '',
      fromBinList: [],
      csmBinList: [],
      maxFeedQuant: '',
      minFeedQuant: '',
      consumeWay: '',
      consumeSort: '',
      feedSts: [],
      csmBinCode: '',
      fromBin: '',
      fromBinWhsCode: '',
      csmWhsCode: '',
      backFeedWay: '',
      subPart: false,
      repairIntercept: false,
      repairTimes: 0
    }
  })
}
// 确定
const determine = () => {
  if (selectionList.value.length === 0) return ElMessage.warning('请勾选数据')
  let result = []
  const originaCheck = deepClone(collectList.value)
  if (originaCheck.length === 0) {
    result = selectionList.value
  } else {
    // 过滤掉已勾选的
    selectionList.value.forEach((item) => {
      if (!originaCheck.find((sele) => sele.matCode === item.matCode)) {
        result.push(item)
      }
    })
  }

  // 为新添加的物料设置默认值
  result.forEach(item => {
    // ========== 在这里设置默认值 ==========
    item.fromWhsCode = '' // 原料仓库 - 填默认值
    item.feedSuType = '' // 投入方式 - 填默认值
    item.fromType = '' // 来源类型 - 填默认值
    item.maxFeedQuant = 0 // 最大投料数 - 填默认值
    item.minFeedQuant = 0 // 投料库存预指 - 填默认值
    item.consumeWay = '' // 消耗方式 - 填默认值
    item.consumeSort = '' // 扣减规则 - 填默认值
    // ========== 默认值设置结束 ==========
  })

  console.log(result, 888)
  emit('determine', result)
  dialogVisible.value = false
}

// 默认主题表格颜色
const tableRowClassName = ({ rowIndex }) => {
  if (rowIndex % 2 !== 0) {
    return 'grey-row'
  } else {
    return 'white-row'
  }
}

// #Expose
defineExpose({
  openDialog
})
</script>

<template>
  <div class="dialog">
    <el-dialog v-model="dialogVisible" v-bind="dialogOptions">
      <el-form v-bind="formOptions" ref="RefForm" v-if="formData">
        <el-form-item label="物料编号" prop="matCode">
          <el-input v-model="formData.matCode" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryData">查询</el-button>
          <el-button plain @click="onClear">清空</el-button>
        </el-form-item>
      </el-form>
      <base-table fill border v-loading="loading" :data="tableData" :page="page" @pageChange="queryData"
        @selection-change="handleSelectionChange" :row-key="(row) => row.matCode" :row-class-name="tableRowClassName"
        tableLayout="fixed" ref="tableRef">
        <el-table-column type="selection" :reserve-selection="true" :selectable="checkSelectSet" width="55" />
        <el-table-column label="序号" type="index" :index="(index) => indexMethod(index, page.page, page.size)"
          align="center" />
        <el-table-column prop="matCode" label="物料编号" align="center" />
        <el-table-column prop="matName" label="物料名称" align="center" />
        <!-- <el-table-column prop="productionClass" label="物料类型" align="center" :formatter="dictStore.formatTable('PROCLASS').format" /> -->
      </base-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="determine">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 10px 30px 0px 30px;
  box-sizing: border-box;
}

.base-table {
  height: 50vh;
}

:deep .el-table {
  .grey-row {
    background-color: #f0f2f3;
  }

  .white-row {
    background-color: white;
  }
}
</style>
