<script setup>
import { defineProps, defineEmits, computed, ref, onMounted, shallowRef, watch } from 'vue'
import { useDictStore } from '@/stores/dict'
import { useWarehouse } from '@/stores/warehouse'
import { GetWhList } from '@/api/modules/wms'
import { ElMessage } from 'element-plus'
import { asynConfirm, deepClone, getSpecificFormat } from '@/common/utils'
import useProRouting from '../../../hooks/useProRouting'
import AddMaterial from '@/views/modules/master-data/process-path/dialogs/add-material.vue'
import BatchFullDialog from '@/views/modules/master-data/process-path/components/routine/tabs/metailer-feeding/dialog/batch-full-dialog.vue'
import i18n from '@/i18n'
import { UNIT_ASSISTANT_MAP } from '@/constants/unit'

const emit = defineEmits(['update:modelValue', 'change'])
const props = defineProps({
  modelValue: {
    type: [Object],
    required: true,
    default: null
  }
})

const dictStore = useDictStore()
const proRouting = useProRouting()
const tableLoading = shallowRef(false)
const selectionList = ref([])
const whList = ref([])
const warehouseModel = ref(null)
const materialRef = ref(null)
const batchFullRef = ref(null)
const matIntableRef = ref(null)
const popData = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})
const stationList = computed(() => {
  return popData.value.workStations ? getSpecificFormat(popData.value.workStations, 'stationName', 'stationCode') : []
})

// 处理长宽的计算
function handleLengthWidth(value, row) {
  if (!value) return { length: 0, width: 0 }
  if (row.unitAssistant === 'ck') {
    const [length, width] = value.split('*').map(v => Number(v) || 0)
    return { length, width }
  }
  return { length: 0, width: 0 }
}

// 更新quantAssistant的值
function updateQuantAssistant(length, width, row) {
  if (row.unitAssistant === 'ck') {
    row.quantAssistant = `${length}*${width}`
  }
}

async function getWarehouse() {
  let res = await useWarehouse().loadData()
  warehouseModel.value = res?.warehouseList ?? []
}

// 获取仓库下拉框数据
const queryWhData = async () => {
  try {
    let res = await GetWhList()
    whList.value = res && res.length ? getSpecificFormat(res, 'descrip', 'whCode') : []
  } catch (e) {
    whList.value = []
  }
}

//选择库位触发
function onStationChange(row, i, type) {
  const { fromBinList, csmBinList } = row

  if (type == 'from') {
    if (!fromBinList || !fromBinList.length) {
      return
    }
    row.fromBin = fromBinList.length ? fromBinList[3] : ''
    row.fromBinWhsCode = fromBinList.length ? fromBinList[0] : ''
  } else {
    if (!csmBinList || !csmBinList.length) {
      return
    }
    row.csmBinCode = csmBinList.length ? csmBinList[3] : ''
    row.csmWhsCode = csmBinList.length ? csmBinList[0] : ''
  }
}

// 移除
async function onRemove(data) {
  if (await asynConfirm(i18n.$t('message.confirmDelete'))) {
    popData.value.matIn = popData.value.matIn.filter((item) => item.matCode !== data.matCode)
    ElMessage.success(i18n.$t('message.deleteSuccess'))
  }
}

// 添加物料
function onAppend() {
  const bomNo = proRouting.routingData.value.bomNo || ''
  if (bomNo == '') {
    ElMessage.warning(i18n.$t('processPath.PleaseSelectBOM'))
    return
  }
  materialRef.value.openDialog(popData.value.matIn, { bomNo, opCode: popData.value.opCode })
}

// 批量填充
const onFulled = () => {
  if (!selectionList.value.length) {
    return ElMessage.warning(i18n.$t('processPath.PleaseMatNoData'))
  }
  batchFullRef.value.openDialog(popData.value)
}

// 确定
async function addMatConfirm(list) {
  try {
    console.log(list, 'list')

    // 为新添加的物料设置默认值
    await setDefaultValues(list)

    popData.value.matIn.push(...list)
    // 清除选择框
    ElMessage.success('添加物料成功')
  } catch (error) {
    console.log(error)
  }
}

// 设置默认值函数
async function setDefaultValues(list) {
  try {
    // 获取默认仓库 - 取第一个仓库作为默认值
    const defaultWarehouse = whList.value.length > 0 ? whList.value[0].value : ''

    // 获取字典默认值
    const [investMethods, binFroms, consumeMethods, consumeMaterials] = await Promise.all([
      dictStore.options('INVEST_METHOD'),
      dictStore.options('BINFROM'),
      dictStore.options('CONSUME_METHOD'),
      dictStore.options('CONSUME_METERIAL')
    ])

    list.forEach(item => {
      // ========== 自动设置默认值 ==========
      item.fromWhsCode = defaultWarehouse // 原料仓库 - 第一个仓库
      item.feedSuType = investMethods.length > 0 ? investMethods[0].value : '' // 投入方式 - 第一个选项
      item.fromType = binFroms.length > 0 ? binFroms[0].value : '' // 来源类型 - 第一个选项
      item.fromBinList = [] // 来源库位 - 空数组
      item.csmBinList = [] // 消耗库位 - 空数组
      item.maxFeedQuant = 1000 // 最大投料数 - 默认1000
      item.minFeedQuant = 100 // 投料库存预指 - 默认100
      item.consumeWay = consumeMethods.length > 0 ? consumeMethods[0].value : '' // 消耗方式 - 第一个选项
      item.consumeSort = consumeMaterials.length > 0 ? consumeMaterials[0].value : '' // 扣减规则 - 第一个选项
      item.feedSts = [] // 上料工位指定 - 空数组
      // ========== 默认值设置结束 ==========
    })
  } catch (error) {
    console.error('设置默认值失败:', error)
    // 如果获取失败，使用空值作为默认值
    list.forEach(item => {
      item.fromWhsCode = ''
      item.feedSuType = ''
      item.fromType = ''
      item.fromBinList = []
      item.csmBinList = []
      item.maxFeedQuant = 0
      item.minFeedQuant = 0
      item.consumeWay = ''
      item.consumeSort = ''
      item.feedSts = []
    })
  }
}

function fullDataFn(formObj) {
  const {
    fromWhsCode,
    feedSuType,
    fromType,
    fromBinList,
    csmBinList,
    maxFeedQuant,
    minFeedQuant,
    backFeedWay,
    consumeWay,
    consumeSort,
    feedSts,
    fromBin,
    fromBinWhsCode,
    csmBinCode,
    csmWhsCode,
    repairIntercept,
    repairTimes
  } = formObj
  console.log(formObj)
  selectionList.value.forEach((item) => {
    item.fromWhsCode = fromWhsCode
    item.feedSuType = feedSuType
    item.fromType = fromType
    item.fromBinList = fromBinList
    item.csmBinList = csmBinList
    item.maxFeedQuant = maxFeedQuant
    item.minFeedQuant = minFeedQuant
    item.backFeedWay = backFeedWay
    item.consumeWay = consumeWay
    item.consumeSort = consumeSort
    item.feedSts = feedSts
    item.fromBin = fromBin
    item.fromBinWhsCode = fromBinWhsCode
    item.csmBinCode = csmBinCode
    item.csmWhsCode = csmWhsCode
    item.repairIntercept = repairIntercept
    item.repairTimes = repairTimes
  })
  matIntableRef.value.clearSelection()
}

onMounted(() => {
  queryWhData()
  getWarehouse()
})
</script>
<template>
  <div>
    <el-button type="primary" class="mb-2" :loading="tableLoading" :disabled="popData.hasPassOp" @click="onAppend()">{{
      $t('processPath.AddMaterial') }}</el-button>
    <el-button type="primary" class="mb-2" :loading="tableLoading" @click="onFulled()">{{ $t('processPath.BatchFill')
    }}</el-button>
    <el-table border :data="popData.matIn" v-loading="tableLoading" @selection-change="selectionList = $event"
      ref="matIntableRef">
      <el-table-column fixed="left" type="selection" width="55" />
      <el-table-column fixed="left" :label="$t('base.No')" type="index" align="center" />
      <el-table-column fixed="left" prop="matCode" :label="$t('base.mateCode')" align="center" width="100" />
      <el-table-column fixed="left" prop="matName" :label="$t('base.mateName')" align="center" width="100" />
      <el-table-column prop="bomQuant" :label="$t('processPath.BOMUsage')" align="center" width="180px">
        <template #default="{ row }">
          <el-input-number v-model="row.bomQuant" :min="0" placeholder="请输入" />
        </template>
      </el-table-column>
      <el-table-column prop="fromWhsCode" :label="$t('processPath.MaterialWarehouse')" align="center" width="150px">
        <template #default="{ row }">
          <el-select v-model="row.fromWhsCode" :placeholder="$t('form.pleaseSelect')" clearable>
            <el-option v-for="item in whList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="unit" :label="$t('base.unit')" align="center"
        :formatter="dictStore.formatTable('UNITTYPE').format" />
      <el-table-column prop="unitAssistant" label="副单位" align="center">
        <template #default="{ row }">
          {{ UNIT_ASSISTANT_MAP[row.unitAssistant] }}
        </template>
      </el-table-column>
      <el-table-column prop="quantAssistant" label="副单位数量" align="center" width="300px">
        <template #default="{ row }">
          <template v-if="row.unitAssistant === 'ck'">
            <div class="flex items-center gap-1">
              <el-input-number
                :model-value="handleLengthWidth(row.quantAssistant, row).length"
                @update:model-value="(val) => updateQuantAssistant(val, handleLengthWidth(row.quantAssistant, row).width, row)"
                :min="0" 
                placeholder="长" 
                style="width: 140px" 
              />
              <span>×</span>
              <el-input-number
                :model-value="handleLengthWidth(row.quantAssistant, row).width"
                @update:model-value="(val) => updateQuantAssistant(handleLengthWidth(row.quantAssistant, row).length, val, row)"
                :min="0" 
                placeholder="宽" 
                style="width: 140px" 
              />
            </div>
          </template>
          <template v-else>
            <el-input-number v-model="row.quantAssistant" :min="0" placeholder="请输入" />
          </template>
        </template>
      </el-table-column>
      <el-table-column prop="feedSuType" :label="$t('processPath.InvestmentMethod')" align="center" width="180px">
        <template #default="{ row }">
          <el-select-dict dictType="INVEST_METHOD" clearable v-model="row.feedSuType" />
        </template>
      </el-table-column>
      <el-table-column prop="fromType" :label="$t('processPath.SourceType')" align="center" width="180px">
        <template #default="{ row }">
          <el-select-dict dictType="BINFROM" clearable v-model="row.fromType" />
        </template>
      </el-table-column>
      <el-table-column prop="fromBinList" :label="$t('processPath.SourceLocation')" align="center" width="130px">
        <template #default="{ row, $index }">
          <el-cascader v-model="row.fromBinList" :options="warehouseModel" clearable
            :placeholder="$t('form.pleaseSelect')" @change="onStationChange(row, $index, 'from')" />
        </template>
      </el-table-column>
      <el-table-column prop="csmBinList" :label="$t('processPath.ConsumptionLocation')" align="center" width="130px">
        <template #default="{ row, $index }">
          <el-cascader v-model="row.csmBinList" :options="warehouseModel" clearable
            :placeholder="$t('form.pleaseSelect')" @change="onStationChange(row, $index, 'csm')" />
        </template>
      </el-table-column>
      <el-table-column prop="maxFeedQuant" :label="$t('processPath.maximumFeeding')" align="center" width="180px">
        <template #default="{ row }">
          <el-input-number v-model="row.maxFeedQuant" :min="0" />
        </template>
      </el-table-column>
      <el-table-column prop="minFeedQuant" :label="$t('processPath.FeedStock')" align="center" width="180px">
        <template #default="{ row }">
          <el-input-number v-model="row.minFeedQuant" :min="0" />
        </template>
      </el-table-column>
      <!-- <el-table-column prop="backFeedWay" label="退料方式" align="center" width="130px">
        <template #default="{ row }">
          <el-select-dict dictType="BACK_METHOD" v-model="row.backFeedWay" />
        </template>
      </el-table-column> -->
      <el-table-column prop="consumeWay" :label="$t('processPath.consumptionMethod')" align="center" width="180px">
        <template #default="{ row }">
          <el-select-dict dictType="CONSUME_METHOD" clearable v-model="row.consumeWay" />
        </template>
      </el-table-column>
      <el-table-column prop="consumeSort" :label="$t('processPath.DeductionRules')" align="center" width="180px">
        <template #default="{ row }">
          <el-select-dict dictType="CONSUME_METERIAL" v-model="row.consumeSort" />
        </template>
      </el-table-column>
      <el-table-column prop="feedSts" :label="$t('processPath.LoadingStation')" align="center" width="200px">
        <template #default="{ row }">
          <el-select v-model="row.feedSts" :placeholder="$t('form.pleaseSelect')" multiple clearable filterable>
            <el-option v-for="item in stationList" :key="item.id" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="repairIntercept" :label="$t('fan-xiu-ci-shu-xian-zhi')" align="center" width="200px">
        <template v-slot="scope">
          <el-switch v-model="scope.row.repairIntercept"
            style="--el-switch-on-color: #3277f5; --el-switch-off-color: #b4b4b4" />
        </template>
      </el-table-column>
      <el-table-column prop="repairTimes" :label="$t('xian-zhi-ci-shu')" align="center" width="180px">
        <template v-slot="scope">
          <el-input-number v-model="scope.row.repairTimes" v-if="scope.row.repairIntercept" :min="0" />
          <div v-else>{{ scope.row.repairIntercept || '无' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="subPart" :label="$t('processPath.SubpartScan')" align="center">
        <template v-slot="scope">
          <el-switch v-model="scope.row.subPart"
            style="--el-switch-on-color: #3277f5; --el-switch-off-color: #b4b4b4" />
        </template>
      </el-table-column>
      <el-table-column prop="" :label="$t('base.operation')" align="center" fixed="right">
        <template #default="{ row }">
          <el-button type="danger" link @click="onRemove(row)" v-if="!row.isWip"> {{ $t('base.remove') }} </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 物料弹框 -->
    <add-material ref="materialRef" @determine="addMatConfirm" />
    <!-- 批量填充 -->
    <batch-full-dialog ref="batchFullRef" @saveFormData="fullDataFn" />
  </div>
</template>
<style lang="scss" scoped>
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.gap-1 {
  gap: 0.25rem;
}
</style>
