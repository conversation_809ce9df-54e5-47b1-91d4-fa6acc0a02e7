import request from '@/common/request.js'

//获取字典数据getDictTypeList
export const getDictTypeList = (params = {}) => {
  return request({
    url: '/mes_api/v1/dict/findChildren',
    method: 'GET',
    params
  })
}

/*
 * 库存冻结解冻
 *  @param { params, data: Object }
 *  @returns { Promise }
 */
// 库存冻结解冻-物料编号查询
export const getMatMaterialMasterList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/matMaterialMaster/pages',
    method: 'POST',
    params,
    data
  })
}

// 库存冻结解冻-库位号查询
export const getBinList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmStorageBin/pages',
    method: 'POST',
    params,
    data
  })
}

// 库存冻结解冻-table查询
export const queryPaginGroupWmquant = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/paginGroupWmquant',
    method: 'POST',
    params,
    data
  })
}

/*
 * 库存冻结解冻记录
 *  @param { params, data: Object }
 *  @returns { Promise }
 */

// 库存冻结解冻记录-table查询
export const queryTrcStockLog = (params = {}, data = {}) => {
  return request({
    // url: '/mes_api/v1/trcStockLog/pages',
    url: '/mes_api/v1/wmHolding/pages',
    method: 'POST',
    params,
    data
  })
}

// 库存冻结解冻-冻结|解冻提交
export const freezeSubmitAndThawSubmit = (urlKey = '', data = {}) => {
  return request({
    url: `/mes_api/v1/stockDeal/${urlKey}`,
    method: 'POST',
    data
  })
}

// 库存冻结解冻记录-详情 初始化table查询
export const initTrcStockQuant = (params = {}, data = {}) => {
  return request({
    // url: '/mes_api/v1/trcStockQuant/pages',
    url: '/mes_api/v1/wmHoldingItem/pages',
    method: 'POST',
    params,
    data
  })
}

//保质期预警分页查询
export const getShelfLifeWarningList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/expirationPages',
    method: 'POST',
    params,
    data
  })
}

//库龄预警分页查询
export const getInventoryAgeWarningList = (params = {}, data = {}) => {
  return request({
    url: '/mes_api/v1/wmQuant/storagePages',
    method: 'POST',
    params,
    data
  })
}
