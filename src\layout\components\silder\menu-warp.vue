<script setup>
import { computed } from 'vue'
import MenuItem from './menu-item.vue'
import { hasAuth } from '@/common/auth'
const props = defineProps({
  list: {
    type: Array,
    required: true,
    default() {
      return []
    }
  }
})
const menus = computed(() => {
  return props.list.filter((i) => {
    if (i.hidden) return false
    if (i.meta && i.meta.auth) {
      return hasAuth(i.meta.auth)
    } else {
      return true
    }
  })
})
</script>
<template>
  <el-scrollbar>
    <MenuItem v-for="item in menus" :key="item.path" :data="item" />
  </el-scrollbar>
</template>
<style scoped lang="scss"></style>
