import request from '@/common/request.js'

// 采购订单查询
export function querySupplierOrderList(params, data) {
    return request({
        url: '/mes_api/v1/wmPurchaseOrder/orderQuery',
        method: 'POST',
        params,
        data
    })
}

// 通过订单号查询订单明细
export function querySupplierOrderDetail(params) {
    return request({
        url: '/mes_api/v1/wmPurchaseItem/pageByPoNo',
        method: 'GET',
        params
    })
}
// 供应商门户送货单查询
export function querySupplierDeliveryList(params, data) {
    return request({
        url: '/mes_api/v1/wmPurchaseOrder/supperDeliveryNote',
        method: 'POST',
        params,
        data
    })
}
// 发货单号查询发货明细
export function queryDeliveryDetail(params) {
    return request({
        url: '/mes_api/v1/wmPurchaseOrder/pageByStkinNoAndMatCode',
        method: 'GET',
        params
    })
}