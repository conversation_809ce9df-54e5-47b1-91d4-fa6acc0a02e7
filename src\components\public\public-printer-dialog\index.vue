<template>
  <el-dialog v-model="visible" title="打印" :width="340" class="p-p-d-dialog">
    <el-tabs v-model="actived">
      <el-tab-pane label="本地打印" name="local">
        <div class="printer-box" @click.stop>
          <main class="printer-box-main">
            <p>
              <span>模板类型: </span>
              <span>{{ selectedTemplateType }}</span>
            </p>
            <p>
              <span>打印模板: </span>
              <el-select v-model="templateCode" style="width: 150px" class="no-border-select">
                <el-option v-for="item in templateOptions" :key="item.templateCode" :label="item.templateName" :value="item.templateCode" />
              </el-select>
            </p>
          </main>
          <footer class="printer-box-footer">
            <el-button @click="close">关闭</el-button>
            <el-button type="primary" @click="goPrintLocal" :loading="submitLoading">打印</el-button>
          </footer>
        </div>
      </el-tab-pane>
      <el-tab-pane label="网络打印" name="network">
        <div class="printer-box" @click.stop>
          <header class="printer-box-header">
            <img src="./printer.png" class="printer-box-img" />
            <i class="printer-box-img-status" :class="selectedPrinterStatus && selectedPrinterStatus.connection" />
            <div class="printer-box-header-form">
              <div class="sec-wrap">
                <el-select v-model="selectedPrinter" @change="onSelectPrinter" style="width: 160px" class="no-border-select">
                  <el-option v-for="item in Printer.printers.value" :key="item" :label="item" :value="item" />
                </el-select>
                <span class="cont-icon">
                  <Loading v-if="loading" class="rotate-anim" />
                  <Refresh v-else @click="load" />
                </span>
              </div>
              <p>
                <span>连接状态: </span>
                <span v-if="loadingStatus">获取中..</span>
                <span v-else-if="selectedPrinterStatus">{{ selectedPrinterStatus.connection }}</span>
              </p>
              <p>
                <span>打印机状态: </span>
                <span v-if="loadingStatus">获取中..</span>
                <span v-else-if="selectedPrinterStatus">{{ selectedPrinterStatus.status }}</span>
              </p>
            </div>
          </header>
          <main class="printer-box-main">
            <p>
              <span>模板类型: </span>
              <span>{{ selectedTemplateType }}</span>
            </p>
            <p>
              <span>打印模板: </span>
              <el-select v-model="templateCode" style="width: 150px" class="no-border-select">
                <el-option v-for="item in templateOptions" :key="item.templateCode" :label="item.templateName" :value="item.templateCode" />
              </el-select>
            </p>
            <p>
              <span>打印份数: </span>
              <el-input-number v-model="copies" :min="1" :max="99" size="small" />
            </p>
          </main>
          <footer class="printer-box-footer">
            <el-button @click="close">关闭</el-button>
            <el-button type="primary" @click="goPrintNetwork" :loading="submitLoading">打印</el-button>
          </footer>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script setup>
import { onMounted, ref, computed } from 'vue'
import { Refresh, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import usePrinter from '@/hooks/usePrinter'
import { options as templatetypeOptions } from './templatetype.js'

const Printer = usePrinter()
const emit = defineEmits(['goPrint'])
const props = defineProps({
  templateCode: {
    type: String,
    default: ''
  },
  templateType: {
    type: String,
    default: ''
  }
})

const visible = ref(false)
const loading = ref(false)
const loadingStatus = ref(false)
const actived = ref('local')
const submitLoading = ref(false)
const selectedPrinter = ref('')
const selectedPrinterStatus = ref(null)
const templateCode = ref('')
const copies = ref(1)
const templateOptions = computed(() => {
  if (props.templateType) {
    return Printer.templates.value.filter((i) => i.templateType === props.templateType)
  }
  return Printer.templates.value
})
const selectedTemplateType = computed(() => {
  let typecode = props.templateType
  if (templateCode.value) {
    const fd = Printer.templates.value.find((i) => i.templateCode === templateCode.value)
    if (fd) {
      typecode = fd.templateType
    }
  }
  if (typecode) {
    const fd = templatetypeOptions.find((i) => i.value === typecode)
    if (fd) {
      return fd.label
    }
    return props.templateType
  } else {
    return '未知'
  }
})

async function open() {
  if (visible.value) {
    close()
  } else {
    visible.value = true
    templateCode.value = templateCode.value || props.templateCode
    await loadPrinterStatus()
    Printer.throttleGetTemplates()
  }
}

function goPrintLocal() {
  if (!templateCode.value) {
    return ElMessage.error('请选择打印模板')
  }
  submitLoading.value = true
  emit('goPrint', submitLocal, cencel)
}

function goPrintNetwork() {
  if (!selectedPrinter.value) {
    return ElMessage.error('请选择打印机')
  }
  if (!templateCode.value) {
    return ElMessage.error('请选择打印模板')
  }
  submitLoading.value = true
  emit('goPrint', submitNetwork, cencel)
}

function cencel() {
  submitLoading.value = false
}

function close() {
  visible.value = false
  copies.value = 1
}

function onSelectPrinter() {
  Printer.selectPrinter(selectedPrinter.value)
  loadPrinterStatus(true)
}

async function load() {
  try {
    loading.value = true
    await Printer.getPrinters()
    await loadPrinterStatus(true)
  } catch (e) {
    console.error(e)
  } finally {
    loading.value = false
  }
}

async function loadPrinterStatus(reload) {
  try {
    loadingStatus.value = true
    if (selectedPrinter.value) {
      selectedPrinterStatus.value = await Printer.getPrinterStatus(selectedPrinter.value, reload)
    }
  } catch (e) {
    console.error(e)
  } finally {
    loadingStatus.value = false
  }
}

async function submitNetwork() {
  try {
    // 传入数据>历史选择>默认数据
    templateCode.value = data.templateCode || templateCode.value || props.templateCode

    if (Array.isArray(data.printData)) {
      if (!data.printData.length) {
        throw '没有打印数据'
      }
    } else {
      if (!data.printData) {
        throw '没有打印数据'
      }
    }
    await Printer.printLocal({
      templateCode: templateCode.value,
      printData: data.printData,
      options: {
        copies: copies.value
      }
    })
    ElMessage.success('操作成功')
    close()
  } catch (e) {
    if (typeof e === 'string') {
      ElMessage.error(e)
    } else if (e.message) {
      ElMessage.error(e.message)
    } else {
      ElMessage.error('操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

async function submitLocal(data) {
  try {
    // 传入数据>历史选择>默认数据
    templateCode.value = data.templateCode || templateCode.value || props.templateCode

    if (Array.isArray(data.printData)) {
      if (!data.printData.length) {
        throw '没有打印数据'
      }
    } else {
      if (!data.printData) {
        throw '没有打印数据'
      }
    }
    await Printer.printLocal({
      templateCode: templateCode.value,
      printData: data.printData,
      options: {}
    })
    ElMessage.success('操作成功')
    close()
  } catch (e) {
    if (typeof e === 'string') {
      ElMessage.error(e)
    } else if (e.message) {
      ElMessage.error(e.message)
    } else {
      ElMessage.error('操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

onMounted(() => {
  selectedPrinter.value = Printer.defaultPrinter.value
})

defineExpose({
  open
})
</script>
<style scoped>
.el-popper {
  background-color: red;
  padding-top: 0 !important;
}

.printer-box {
}

.printer-box-header {
  display: inline-flex;
  align-items: flex-start;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.printer-box-main {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.printer-box-footer {
  text-align: right;
  padding: 10px;
  padding-bottom: 0;
}

.printer-box-img {
  width: 60px;
  height: 60px;
  margin-right: 20px;
}

.printer-box-img-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  left: 50px;
}
.printer-box-img-status.Unknown {
  background-color: gray;
  box-shadow: 0 0 2px gray;
}

.printer-box-img-status.Failed {
  background-color: #ff1919;
  box-shadow: 0 0 2px #ff1919;
}
.printer-box-img-status.Success {
  background-color: #4bb21c;
  box-shadow: 0 0 2px #4bb21c;
}

.printer-box-header-form {
}

/deep/.no-border-select.el-select .el-input__wrapper {
  box-shadow: none;
  border: none;
  padding: 0;
}

/deep/.no-border-select.el-select .el-input__wrapper,
/deep/.no-border-select.el-select:hover .el-input__wrapper,
/deep/.no-border-select.el-select:focus-visible .el-input__wrapper,
/deep/.no-border-select.el-select .el-input.is-focus .el-input__wrapper,
/deep/.no-border-select.el-select .el-input__wrapper.is-focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.printer-box-main {
  display: flex;
  flex-direction: column;
}

.printer-box-main p {
  font-size: 14px;
  margin-bottom: 6px;
}

.sec-wrap {
  display: flex;
  align-items: center;
}

.cont-icon svg {
  color: #3277f5;
  width: 18px;
  height: 18px;
  margin-left: 20px;
  cursor: pointer;
}
.rotate-anim {
  animation: rotate 1s linear infinite;
}
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
<style>

.p-p-d-dialog .el-dialog__body{
    padding-top: 0 !important;
}
</style>